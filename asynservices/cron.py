# -*- coding: utf-8 -*-
from celery import Celery
import json
from api.openstack.client import Client
from celery.schedules import crontab
from api.model.instances import InstanceCreateFrom, InstanceActionFrom
from api.model.instances import InstanceCreateFrom
from api.model.volumes import VolumeCreate<PERSON>rom, VolumeActionFrom
from datetime import datetime
from datetime import date
import time
import re

from sqlalchemy.orm import scoped_session, sessionmaker
from sqlalchemy import create_engine
from contextlib import contextmanager
from sqlalchemy.orm import sessionmaker
from sqlalchemy.exc import OperationalError
#from db.model.user import User
import settings
import requests
import traceback
from pytz import timezone

import logging
from asynservices.db import Database
from db.model.desk  import DeskPloy, Desk, DeskPool, DeskPoolPloy
from db.model.device import DevicePloy, Device
from datetime import timedelta
import pytz

shanghai_tz = timezone('Asia/Shanghai')
utc_tz = timezone('UTC')
    
if settings.REDIS_PWD:
    redis_backend_url = "redis://:%s@%s:%s/3" % (settings.REDIS_PWD, settings.REDIS_HOSTNAME, settings.REDIS_PORT)
    redis_broker_url = "redis://:%s@%s:%s/4" % (settings.REDIS_PWD, settings.REDIS_HOSTNAME, settings.REDIS_PORT)
else:
    redis_backend_url = "redis://%s:%s/3" % (settings.REDIS_HOSTNAME, settings.REDIS_PORT)
    redis_broker_url = "redis://%s:%s/4" % (settings.REDIS_HOSTNAME, settings.REDIS_PORT)   

app = Celery('tasks', backend=redis_backend_url, broker=redis_broker_url)

db = Database()


@app.on_after_configure.connect
def setup_periodic_tasks(sender, **kwargs):
    #sender.add_periodic_task(10.0, add.s(3, 56).set(expires=30), name="test")
    sender.add_periodic_task(1800.0, device_check.s(), name='device_check')

    """模式匹配 星期天是0  # 数据库约定 "16:04,1,2,3,4" 周一 二 三 四 16:04 运行  要增加4个定时任务
    sender.add_periodic_task(
        crontab(hour=7, minute=30, day_of_week=1),
        test.s('Happy Mondays!'),
    )
    """


    """模式匹配 每天运行 # 数据库约定 "16:04" 每天 16:04运行
    sender.add_periodic_task(
        crontab(hour=13, minute=53),
        add.s(1, 2), name="ddd"
    )
    """

    with db.session_scope() as session:
        #加入关机定时任务
        ploy_list = session.query(DeskPloy).filter(DeskPloy.key.in_(["shutdown_clock", "open_clock", "snapshot_backup"])).all()
        for ploy in ploy_list:
            print(ploy.value)
            desk = session.query(Desk).filter_by(id=ploy.desk_id).first()
            if not desk:
                print("------------no desk poly.id %s" % ploy.id)
                continue
            vm_id = desk.vm_id
            desk_id = desk.id
            desk_name = desk.name

            #匹配每星期
            pattern_week = r'^\d{2}:\d{2}(,\d+)+$'
            if re.match(pattern_week, ploy.value):
                shanghai_time_str = ploy.value.split(",")[0]
                today = date.today()
                shanghai_time = shanghai_tz.localize(datetime.combine(today, datetime.strptime(shanghai_time_str, "%H:%M").time()))
                utc_time = shanghai_time.astimezone(utc_tz)
                #time_obj = datetime.strptime(time_str, "%H:%M")
                week_list = ploy.value.split(",")[1:]
                weeks = ",".join(week_list)
                if ploy.key == "shutdown_clock":
                    name = "%s_%s_%s" % ("shutdown_clock", desk_name, vm_id)
                    print("set %s crontab shutdown_clock at: %s:%s %s" % (desk.name, utc_time.hour, utc_time.minute, weeks))
                    sender.add_periodic_task(
                            crontab(hour=utc_time.hour, minute=utc_time.minute, day_of_week=weeks),
                            power_down.s(vm_id), name=name
                          )
                if ploy.key == "open_clock":
                    name = "%s_%s_%s" % ("open_clock", desk_name, vm_id)
                    print("set %s crontab open_clock at: %s:%s %s" % (desk.name, utc_time.hour, utc_time.minute, weeks))
                    sender.add_periodic_task(
                            crontab(hour=utc_time.hour, minute=utc_time.minute, day_of_week=weeks),
                            power_up.s(vm_id), name=name
                          )

                if ploy.key == "snapshot_backup":
                    name = "%s_%s_%s" % ("snapshot_backup", desk_name, vm_id)
                    print("set %s crontab snapshot_backup at: %s:%s %s" % (desk.name, utc_time.hour, utc_time.minute, weeks))
                    sender.add_periodic_task(
                            crontab(hour=utc_time.hour, minute=utc_time.minute, day_of_week=weeks),
                            backup_vm.s(vm_id, desk_name, desk_id), name=name
                          )
        
        
        #桌面池策略
        ploy_list = session.query(DeskPoolPloy).filter(DeskPoolPloy.key.in_(["shutdown_clock", "open_clock", "snapshot_backup"])).all()
        for ploy in ploy_list:
            print(ploy.value)

            #匹配每星期
            pattern_week = r'^\d{2}:\d{2}(,\d+)+$'
            if re.match(pattern_week, ploy.value):
                shanghai_time_str = ploy.value.split(",")[0]
                today = date.today()
                shanghai_time = shanghai_tz.localize(datetime.combine(today, datetime.strptime(shanghai_time_str, "%H:%M").time()))
                
                utc_time = shanghai_time.astimezone(utc_tz)
                #time_obj = datetime.strptime(time_str, "%H:%M")
                week_list = ploy.value.split(",")[1:]
                weeks = ",".join(week_list)
                if ploy.key == "shutdown_clock":
                    name = "%s_pool_id_%s" % ("shutdown_clock", ploy.deskpool_id)
                    print("set %s crontab shutdown_clock at: %s:%s %s" % (ploy.deskpool_id, utc_time.hour, utc_time.minute, weeks))
                    sender.add_periodic_task(
                            crontab(hour=utc_time.hour, minute=utc_time.minute, day_of_week=weeks),
                            power_down_pool.s(ploy.deskpool_id), name=name
                          )
                if ploy.key == "open_clock":
                    name = "%s_pool_id_%s" % ("open_clock", ploy.deskpool_id)
                    print("set %s crontab open_clock at: %s:%s %s" % (ploy.deskpool_id, utc_time.hour, utc_time.minute, weeks))
                    sender.add_periodic_task(
                            crontab(hour=utc_time.hour, minute=utc_time.minute, day_of_week=weeks),
                            power_up_pool.s(ploy.deskpool_id), name=name
                          )

                if ploy.key == "snapshot_backup":
                    name = "%s_pool_id_%s" % ("snapshot_backup", ploy.deskpool_id)
                    print("set %s crontab snapshot_backup at: %s:%s %s" % (desk.name, utc_time.hour, utc_time.minute, weeks))
                    sender.add_periodic_task(
                            crontab(hour=utc_time.hour, minute=utc_time.minute, day_of_week=weeks),
                            backup_vm_pool.s(vm_id, ploy.deskpool_id), name=name
                          )
                                        
            """                                    
            #匹配每天
            pattern_day = r'^\d{2}:\d{2}$'
            if re.match(pattern_day, ploy.value):
                utc_time = datetime.strptime(ploy.value, "%H:%M")
                
                if ploy.key == "shutdown_clock":
                    print("set %s crontab shutdown_clock at:%s" % (desk.name, ploy.value))
                    sender.add_periodic_task(
                        crontab(hour=utc_time.hour, minute=utc_time.minute),
                        power_down.s(vm_id)
                    )                
                if ploy.key == "open_clock":
                    print("set %s crontab open_clock at:%s" % (desk.name, ploy.value))
                    sender.add_periodic_task(
                        crontab(hour=utc_time.hour, minute=utc_time.minute),
                        power_up.s(vm_id)
                    )
                if ploy.key == "snapshot_backup":
                    print("set %s crontab snapshot_backup at:%s" % (desk.name, ploy.value))
                    sender.add_periodic_task(
                        crontab(hour=utc_time.hour, minute=utc_time.minute),
                        backup_vm.s(vm_id, desk_name, desk_id)
                    )                                    
        
            """


@app.task
def add(x, y):
    print("333333333")
    return x + y


# 半小时检查一次 终端移除
@app.task
def device_check():
    #加入关机定时任务
    ploy_list = session.query(DevicePloy).filter(DevicePloy.key.in_(["terminal_remove"])).all()
    for ploy in ploy_list:
        ex_day = int(ploy.value)
        device = session.query(Device).filter_by(id=ploy.device_id).first()
        print("terminal_remove %s %s" % (device.ip_addr, ploy.value))
        if device.disconnect_at > device.connect_at:
            if (datetime.now() - device.disconnect_at ) > timedelta(days=ex_day):
                device.remove()



# 桌面池开机
@app.task
def power_up_pool(pool_id):
    desk_pool = session.query(DeskPool).filter_by(id=pool_id).first()    
    for desk in desk_pool.desks:
        vm_id = desk.vm_id
        print("run power_up %s" % vm_id)
        instanceaction = InstanceActionFrom
        instanceaction.id = vm_id
        instanceaction.action = "start"
        instanceaction.data = "thxh"
    
        client = Client()
        client.NovaClient.openstack_server_action(client, instanceaction)



# 开机
@app.task
def power_up(vm_id):
    print("run power_up %s" % vm_id)
    instanceaction = InstanceActionFrom
    instanceaction.id = vm_id
    instanceaction.action = "start"
    instanceaction.data = "thxh"

    client = Client()
    client.NovaClient.openstack_server_action(client, instanceaction)



# 桌面关机
@app.task
def power_down_pool(pool_id):
    desk_pool = session.query(DeskPool).filter_by(id=pool_id).first()    
    for desk in desk_pool.desks:
        vm_id = desk.vm_id
        print("run power_down %s" % vm_id)
        instanceaction = InstanceActionFrom
        instanceaction.id = vm_id
        instanceaction.action = "stop"
        instanceaction.data = "thxh"
    
        client = Client()
        client.NovaClient.openstack_server_action(client, instanceaction)


# 关机
@app.task
def power_down(vm_id):
    print("run power_down %s" % vm_id)
    instanceaction = InstanceActionFrom
    instanceaction.id = vm_id
    instanceaction.action = "stop"
    instanceaction.data = "thxh"

    client = Client()
    client.NovaClient.openstack_server_action(client, instanceaction)


# 桌面池备份
@app.task
def backup_vm_pool(pool_id):
    desk_pool = session.query(DeskPool).filter_by(id=pool_id).first()    
    for desk in desk_pool.desks:
        vm_id = desk.vm_id
        desk_name = desk.name
        desk_id = desk.id
        print("run backup_vm %s" % vm_id)
        now = datetime.now()
        nowstr = now.strftime("%Y-%m-%d-%H:%M:%S")
        client = Client()
        data = client.NovaClient.openstack_get_volume_attachment(client, vm_id)
        volume_id = data["volumeAttachments"][0]["volumeId"]
        volumeform = VolumeActionFrom
        volumeform.id = volume_id
        volumeform.action = "create"
        volumeform.data = "%s-定时备份-%s" % (desk_name, nowstr)
        # client = Client()
        # client.VolumeClient.openstack_volume_snapshot(client, volumeform)
        data = {
            "id": desk_id,
            "vm_id": vm_id,
            "name": volumeform.data,
            "force": True
        }
    
        # 定义目标 URL
        url = "http://%s:%s/v1/desk/snapshot" % (settings.GOAPI_URI, settings.GOAPI_PORT)
    
        # 使用 requests 库发送 POST 请求
        requests.post(url, json=data)


# 备份
@app.task
def backup_vm(vm_id, desk_name, desk_id):
    print("run backup_vm %s" % vm_id)
    now = datetime.now()
    nowstr = now.strftime("%Y-%m-%d-%H:%M:%S")
    client = Client()
    data = client.NovaClient.openstack_get_volume_attachment(client, vm_id)
    volume_id = data["volumeAttachments"][0]["volumeId"]
    volumeform = VolumeActionFrom
    volumeform.id = volume_id
    volumeform.action = "create"
    volumeform.data = "%s-定时备份-%s" % (desk_name, nowstr)
    # client = Client()
    # client.VolumeClient.openstack_volume_snapshot(client, volumeform)
    data = {
        "id": desk_id,
        "vm_id": vm_id,
        "name": volumeform.data,
        "force": True
    }

    # 定义目标 URL
    url = "http://%s:%s/v1/desk/snapshot" % (settings.GOAPI_URI, settings.GOAPI_PORT)

    # 使用 requests 库发送 POST 请求
    requests.post(url, json=data)

import sys

east_eight_zone = pytz.timezone('Asia/Shanghai')

if 'beat' in sys.argv:
    print("Running as a beat!")
elif 'worker' in sys.argv:
    print("Running as worker!")
    #一次运行 需要转换成多少秒后运行  从数据库读出来写出调用方法
    #模式匹配 数据库约定 "2023-09-09 16:04"
    with db.session_scope() as session:
        ploy_list = session.query(DeskPloy).filter(DeskPloy.key.in_(["shutdown_clock", "open_clock", "snapshot_backup"])).all()
        for ploy in ploy_list:
            print("------------ploy id: %s ---- ploy value: %s" % (ploy.id, ploy.value))
            desk = session.query(Desk).filter_by(id=ploy.desk_id).first()
            if desk:
                print("has no desk is ploy------------ploy id: %s ---- ploy value: %s" % (ploy.id, ploy.value))
                vm_id = desk.vm_id
                desk_name = desk.name 
                desk_id = desk.id
                pattern = r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}$'
                if re.match(pattern, ploy.value):
                    time_obj = datetime.strptime(ploy.value, "%Y-%m-%d %H:%M")
                    current_time = datetime.now()
                    time_difference = time_obj - current_time
                    print("++++++++++++++++++++++++++++time_difference+++++++++++++++++++++++++++||||",time_difference, "|||-----------****--------")
                    time_difference_in_seconds = time_difference.total_seconds()
                    if time_difference_in_seconds < 0:
                        continue
                    
                    if ploy.key == "shutdown_clock":
                        eta = datetime.utcnow() + timedelta(seconds=time_difference_in_seconds)
                        eta_east_eight = eta.replace(tzinfo=pytz.utc).astimezone(east_eight_zone)
                        power_down.apply_async(args=(vm_id,), eta=eta_east_eight)
                    if ploy.key == "open_clock":
                        eta = datetime.utcnow() + timedelta(seconds=time_difference_in_seconds)
                        eta_east_eight = eta.replace(tzinfo=pytz.utc).astimezone(east_eight_zone)
                        power_up.apply_async(args=(vm_id,), eta=eta_east_eight)
                    if ploy.key == "snapshot_backup":
                        eta = datetime.utcnow() + timedelta(seconds=time_difference_in_seconds)
                        eta_east_eight = eta.replace(tzinfo=pytz.utc).astimezone(east_eight_zone)
                        backup_vm.apply_async(args=(vm_id, desk_name, desk_id,), eta=eta_east_eight)



        ploy_list = session.query(DeskPoolPloy).filter(DeskPoolPloy.key.in_(["shutdown_clock", "open_clock", "snapshot_backup"])).all()
        #ploy_list = session.query(DeskPloy).filter(DeskPloy.key.in_(["shutdown_clock", "open_clock", "snapshot_backup"])).all()
        for ploy in ploy_list:
            print("-- pool ----------ploy id: %s ---- ploy value: %s" % (ploy.id, ploy.value))
            
            deskpool_id = ploy.deskpool_id
            pattern = r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}$'
            if re.match(pattern, ploy.value):
                time_obj = datetime.strptime(ploy.value, "%Y-%m-%d %H:%M")
                current_time = datetime.now()
                time_difference = time_obj - current_time
                time_difference_in_seconds = time_difference.total_seconds()
                if time_difference_in_seconds < 0:
                    continue
                
                if ploy.key == "shutdown_clock":
                    eta = datetime.utcnow() + timedelta(seconds=time_difference_in_seconds)
                    eta_east_eight = eta.replace(tzinfo=pytz.utc).astimezone(east_eight_zone)
                    power_down_pool.apply_async(args=(deskpool_id,), eta=eta_east_eight)
                if ploy.key == "open_clock":
                    eta = datetime.utcnow() + timedelta(seconds=time_difference_in_seconds)
                    eta_east_eight = eta.replace(tzinfo=pytz.utc).astimezone(east_eight_zone)
                    power_up_pool.apply_async(args=(deskpool_id,), eta=eta_east_eight)
                if ploy.key == "snapshot_backup":
                    eta = datetime.utcnow() + timedelta(seconds=time_difference_in_seconds)
                    eta_east_eight = eta.replace(tzinfo=pytz.utc).astimezone(east_eight_zone)
                    backup_vm_pool.apply_async(args=(deskpool_id,), eta=eta_east_eight)
                        

