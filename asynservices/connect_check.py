# -*- coding: utf-8 -*-
from celery import Celery
import json
from api.openstack.client import Client
from celery.schedules import crontab
from api.model.instances import InstanceCreateFrom, InstanceActionFrom
from api.model.instances import InstanceCreateFrom
from api.model.volumes import VolumeCreate<PERSON>rom, VolumeActionFrom
from datetime import datetime
import time
import re

from sqlalchemy.orm import scoped_session, sessionmaker
from sqlalchemy import create_engine
from contextlib import contextmanager
from sqlalchemy.orm import sessionmaker
from sqlalchemy.exc import OperationalError
#from db.model.user import User
import settings
import requests
import traceback
from pytz import timezone

import logging
from asynservices.db import Database
from db.model.desk  import DeskPloy, Desk
from db.model.device import DevicePloy, Device
from datetime import timedelta
import sys
import psutil
import socket
import netifaces as ni
import redis
    
if settings.REDIS_PWD:
    redis_backend_url = "redis://:%s@%s:%s/5" % (settings.REDIS_PWD, settings.REDIS_HOSTNAME, settings.REDIS_PORT)
    redis_broker_url = "redis://:%s@%s:%s/6" % (settings.REDIS_PWD, settings.REDIS_HOSTNAME, settings.REDIS_PORT)
else:
    redis_backend_url = "redis://%s:%s/5" % (settings.REDIS_HOSTNAME, settings.REDIS_PORT)
    redis_broker_url = "redis://%s:%s/6" % (settings.REDIS_HOSTNAME, settings.REDIS_PORT)   

app = Celery('tasks', backend=redis_backend_url, broker=redis_broker_url)

db = Database()



redis_client = redis.StrictRedis(host=settings.REDIS_HOSTNAME, port=settings.REDIS_PORT, db=7)

def check_in(device_ip, service_ip):
    redis_client.sadd(device_ip, service_ip)
    redis_client.expire(device_ip, 60)
    print(f"{device_ip} {service_ip} 已签到")

def get_total_checkins(device_ip):
    total_checkins = redis_client.scard(device_ip)
    return total_checkins


def monitor_tcp_connection(source_ip):
    connections = psutil.net_connections(kind='tcp')

    for conn in connections:
        if (
            conn.status == psutil.CONN_ESTABLISHED and
            conn.laddr.ip == source_ip
            #conn.raddr.ip == dest_ip and
            #conn.raddr.port == dest_port
        ):
            print(f"连接状态: ESTABLISHED")
            return True

    return False

def get_local_ip_of_default_gateway():
    try:
        # 获取默认网关的IP地址
        gateway_info = ni.gateways()
        if 'default' in gateway_info and ni.AF_INET in gateway_info['default']:
            gateway_ip = gateway_info['default'][ni.AF_INET][0]

            # 使用socket库来获取与默认网关关联的本地IP地址
            with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
                s.connect((gateway_ip, 0))
                local_ip = s.getsockname()[0]

            return local_ip
    except Exception as e:
        print(f"Error: {e}")

    return None

local_ip = get_local_ip_of_default_gateway()

@app.on_after_configure.connect
def setup_periodic_tasks(sender, **kwargs):
    #sender.add_periodic_task(10.0, add.s(3, 56).set(expires=30), name="test")
    sender.add_periodic_task(1800.0, tcp_connect_check.s(), name='tcp_connect_check')



#检查小盒TCP连接状态　没有任何一个服务器和小盒有TCP连接．说明小盒已断开　写入断开时间
@app.task
def tcp_connect_check():
    with db.session_scope() as session:
        ploy_list = session.query(DevicePloy).filter(DevicePloy.key.in_(["terminal_remove"])).all()
        for ploy in ploy_list:
            print(ploy.value)
            device = session.query(Device).filter_by(id=ploy.device_id).first()
            source_ip = device.ip_addr
            #判断断开连接比创建连接晚 说明最后是断开的
            dis_conn_at = device.disconnect_at
            conn_at = device.connect_at
            if not dis_conn_at or (dis_conn_at < conn_at):
                #这个设备没有连接到任何虚机　连续连接三次
                if not monitor_tcp_connection(source_ip):
                    #写入redis　小盒ip,　本地ip服务端　sleep 30秒查询
                    check_in(source_ip, local_ip)
                    
                    time.sleep(30)
                    if len(get_total_checkins(source_ip)) == 3:
                        device.disconnect_at =  datetime.now()
                        session.add(device)
                    


if 'beat' in sys.argv:
    print("Running as a beat!")
elif 'worker' in sys.argv:
    print("Running as worker!")

