# -*- coding: utf-8 -*-
from celery import Celery
import json
from api.openstack.client import Client
from celery.schedules import crontab
from api.model.instances import InstanceCreateFrom
from db.model.desk import Desk
from db.model.task import Task, InstanceCreateTask
from api.model.instances import InstanceCreateFrom
from api.model.volumes import VolumeCreateFrom
from api.zun.client import ZunClient

import time

from sqlalchemy.orm import scoped_session, sessionmaker
from sqlalchemy import create_engine
from contextlib import contextmanager
from sqlalchemy.orm import sessionmaker
from sqlalchemy.exc import OperationalError
#from db.model.user import User
import settings
import requests
import traceback
from pytz import timezone

from asynservices.bo.thedesk import *
from asynservices.bo.theweb import *
from asynservices.bo.ws import *
import logging
from asynservices.db import Database




if settings.REDIS_PWD:
    redis_backend_url = "redis://:%s@%s:%s/1" % (settings.REDIS_PWD, settings.REDIS_HOSTNAME, settings.REDIS_PORT)
    redis_broker_url = "redis://:%s@%s:%s/0" % (settings.REDIS_PWD, settings.REDIS_HOSTNAME, settings.REDIS_PORT)
else:
    redis_backend_url = "redis://%s:%s/1" % (settings.REDIS_HOSTNAME, settings.REDIS_PORT)
    redis_broker_url = "redis://%s:%s/0" % (settings.REDIS_HOSTNAME, settings.REDIS_PORT)   

app = Celery('tasks', backend=redis_backend_url, broker=redis_broker_url)


@app.on_after_configure.connect
def setup_periodic_tasks(sender, **kwargs):
    # Calls test('hello') every 10 seconds.
    #sender.add_periodic_task(10.0, test.s('hello'), name='add every 10')
    sender.add_periodic_task(10.0, create_vm_when_volume_finished.s('start'), name='vm_every_10')
    sender.add_periodic_task(10.0, create_desk_when_volume_finished.s('start'), name='desk_every_10')
    
    sender.add_periodic_task(10.0, mount_ex_volume_for_vm.s('start'), name='ex_vm_every_10')
    sender.add_periodic_task(10.0, mount_desk_ex_volume_for_vm.s('start'), name='desk_ex_vm_every_10')
    
    
    #sender.add_periodic_task(10.0, upload_image_to_ceph.s('start'), name='imagecreatefrom every 10')

    # Calls test('world') every 30 seconds
    #sender.add_periodic_task(30.0, test.s('world'), expires=10)

    # Executes every Monday morning at 7:30 a.m.
    """
    sender.add_periodic_task(
        crontab(hour=7, minute=30, day_of_week=1),
        test.s('Happy Mondays!'),
    )
    """



@app.task(max_retries=3)
def create_volume_for_fromsnapshot_vm(create_volume_form):
    global db
    try:
        db = Database()
        bo_create_volume_for_fromsnapshot_vm(db.session_scope, create_volume_form)

    except Exception as exc:
        traceback.print_exc()
        raise create_volume_for_fromsnapshot_vm.retry(exc=exc, countdown=10)



"""
创建主机任务时，云硬盘创建完成 触发定时任务 检测到硬盘创建完成时，继续创建云主机
"""
@app.task
def create_vm_when_volume_finished(arg):
    db = Database()
    bo_create_vm_when_volume_finished(db.session_scope)

@app.task
def create_desk_when_volume_finished(arg):
    db = Database()
    bo_create_desk_when_volume_finished(db.session_scope)


"""
创建虚机时挂载云硬盘
"""
@app.task
def mount_ex_volume_for_vm(arg):
    db = Database()
    bo_mount_ex_volume_for_vm(db.session_scope)
    
@app.task
def mount_desk_ex_volume_for_vm(arg):
    db = Database()
    bo_mount_desk_ex_volume_for_vm(db.session_scope)



@app.task
def upload_image_to_ceph(arg):
    db = Database()
    bo_upload_image_to_ceph(db.session_scope)
    


# def create_container(form):
#     client = ZunClient()
#     restart_policy = form.get("restart_policy", {})
#     if restart_policy:
#         Name = restart_policy.get("Name", "")
#         if Name == "":
#             form["restart_policy"] = None

#     container = client.ZunClient.openstack_create_container(client,form)
    

# def run_container(form):
#     client = ZunClient()
#     container_id = form.get('id')
#     if not container_id:
#         return {"msg": "error", "error": "容器ID不能为空"}
    
#     # 获取容器详情，检查容器状态
#     container_detail = client.ZunClient.openstack_container_details(client, container_id)
#     if container_detail.get("msg") != "ok":
#         return {"msg": "error", "error": "获取容器详情失败", "detail": container_detail}
    
#     container = container_detail.get("container", {})
#     container_status = container.get("status", "")
    
#     # 如果容器状态为created，则运行容器
#     if container_status == "created":
#         return client.ZunClient.openstack_container_start(client, container_id)
#     else:
#         return {"msg": "error", "error": f"容器状态不是created，当前状态: {container_status}"}

@app.task
def create_container_task(form):
    """
    创建容器的任务
    创建完成后会回调run_container_task进行状态检查和启动
    """
    try:
        restart_policy = form.get("restart_policy", {})
        if restart_policy:
            Name = restart_policy.get("Name", "")
            if Name == "":
                form["restart_policy"] = None
        client = ZunClient()
        result = client.ZunClient.openstack_create_container(client, form)
        
        # 如果创建成功，启动回调任务检查状态并运行容器
        if result and isinstance(result, dict) and "uuid" in result:
            container_id = result.get("uuid")
            # 创建一个表单，包含容器ID
            run_form = {"id": container_id}
            # 调度run_container_task，使用countdown参数设置延迟执行(先等待1秒)
            run_container_task.apply_async(args=[run_form], countdown=1)
            return {"msg": "ok", "container": result}
        else:
            return {"msg": "error", "error": "创建容器失败", "detail": result}
    except Exception as exc:
        traceback.print_exc()
        return {"msg": "error", "error": str(exc)}

@app.task(bind=True, max_retries=30)
def run_container_task(self, form):
    """
    检查容器状态并运行容器的任务
    如果容器状态不是created，则每10秒重试一次，最多重试30次(总计5分钟)
    """
    try:
        client = ZunClient()
        container_id = form.get('id')
        if not container_id:
            return {"msg": "error", "error": "容器ID不能为空"}
        
        # 获取容器详情，检查容器状态
        container_detail = client.ZunClient.openstack_container_details(client, container_id)
        if container_detail.get("msg") != "ok":
            # 如果获取容器详情失败，可能是容器还未就绪，进行重试
            raise Exception(f"获取容器详情失败: {container_detail}")
        
        container = container_detail.get("container", {})
        container_status = container.get("status", "")
        
        # 如果容器状态为created，则运行容器
        if container_status == "Created":
            result = client.ZunClient.openstack_container_start(client, container_id)
            return {"msg": "ok", "result": result}
        else:
            # 如果容器状态不是created，则进行重试
            # 每10秒重试一次，最多重试30次(总计5分钟)
            raise Exception(f"容器状态不是created，当前状态: {container_status}")
            
    except Exception as exc:
        # 记录当前重试次数和最大重试次数
        current_retry = self.request.retries
        max_retries = self.max_retries
        
        if current_retry < max_retries:
            # 如果还未达到最大重试次数，则进行重试
            # 每10秒重试一次
            time.sleep(10)
            print(f"容器 {form.get('id')} 未就绪，当前重试次数: {current_retry}/{max_retries}")
            raise self.retry(exc=exc)
        else:
            # 如果已达到最大重试次数，则返回错误
            print(f"容器 {form.get('id')} 启动失败，已达到最大重试次数: {max_retries}")
            return {"msg": "error", "error": f"容器启动超时(5分钟)，最后错误: {str(exc)}"}

# 使用示例
# @app.task
# def example_create_and_run_container(container_form):
#     """
#     示例：创建并运行容器的任务
#     首先创建容器，然后自动回调任务检查状态并运行容器
    
#     使用方法：
#     example_create_and_run_container.delay({
#         "name": "test-container",
#         "image": "nginx:latest",
#         "image_driver": "docker",
#         # 其他容器参数...
#     })
#     """
#     return create_container_task.delay(container_form)

@app.task
def create_desks_by_deskpoolid(deskpool_id, template_id, ids):
    db = Database()
    
    if ids:
        print("have ids %s------------------------" % ids)
        bo_create_desk_deskpoolid_userids(db.session_scope, deskpool_id, template_id, ids)
    else:
        print("no ids -----------------------------")
        bo_create_desk_deskpoolid(db.session_scope, deskpool_id, template_id)

        
# 补齐桌面池里的虚机
@app.task
def fill_up_deskpool_use_new_vm(deskpool_id, template_id):
    db = Database()
    bo_create_desk_deskpoolid(db.session_scope, deskpool_id, template_id)


# 备份还原虚机
@app.task
def desk_backup_restore(vm_id, template_id, snap_id):
    db = Database()
    bo_desk_backup_restore(db.session_scope, vm_id, template_id, snap_id)
    

@app.task
def send(vmid, msg):
    bo_send(vmid, msg)

