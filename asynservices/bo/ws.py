from celery import Celery
import json
import pika
from api.openstack.client import Client
from celery.schedules import crontab
from api.model.instances import InstanceCreateFrom
from db.model.desk import Desk
from db.model.task import Task, InstanceCreateTask
from api.model.instances import InstanceCreateFrom
from api.model.volumes import Volume<PERSON><PERSON><PERSON>rom

from sqlalchemy.orm import scoped_session, sessionmaker
from sqlalchemy import create_engine
import time


def bo_send(vmid, msg):

    import time
    print("dddddddddddddd")
    time.sleep(10)
    print("ssssssssssssss")
    credentials = pika.PlainCredentials('guest', 'guest')  # mq用户名和密码
    # 虚拟队列需要指定参数 virtual_host，如果是默认的可以不填。
    connection = pika.BlockingConnection(pika.ConnectionParameters(host = '127.0.0.1',port = 5672,credentials = credentials))
    channel=connection.channel()
    # 声明消息队列，消息将在这个队列传递，如不存在，则创建
    result = channel.queue_declare(queue = vmid)
    """"
    for i in range(10):
        message=json.dumps({'OrderId':"1000%s"%i})
    # 向队列插入数值 routing_key是队列名
        channel.basic_publish(exchange = '',routing_key = 'python-test',body = message)
        print(message)
    """
    channel.basic_publish(exchange='/',routing_key=vmid, body=msg)    
    connection.close()