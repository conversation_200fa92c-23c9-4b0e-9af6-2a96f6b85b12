from celery import Celery
import json
from datetime import datetime

from api.log.log import CustomLogger
from api.openstack.client import Client
from celery.schedules import crontab
from api.model.instances import InstanceCreateFrom,InstanceActionFrom,InstanceISOEditFrom,InstanceISOCreateFrom,InstanceISOCreateFromDriver
from db.model.desk import Desk
from db.model.task import Task, InstanceCreateTask, ImageCreateTask, ExVolumeCreateTask
from db.model.vm import VmGroup , Vms,Volumes
from sqlalchemy import and_
from db.model.user import User
from api.model.volumes import VolumeCreateFrom,VolumeAttachFrom

import time
import settings
import traceback
import os
from pytz import timezone

import logging

tz = timezone('Asia/Shanghai')
new_logger = CustomLogger(settings.JSON_LOG_ASYN_PATH)
def timetz(*args):
    return datetime.now(tz).timetuple()

access_log = logging.getLogger(__name__)
#access_log = logging.getLogger('tornado.access')
#access_log.propagate = False
# make sure access log is enabled even if error level is WARNING|ERROR
access_log.setLevel(logging.INFO)

log_file = settings.LOG_ASYN_PATH
file_handler = logging.handlers.TimedRotatingFileHandler(log_file, when="d", interval=1, backupCount=30)

logging.Formatter.converter = timetz

log_formatter = logging.Formatter(
    "%(asctime)s;%(module)s;%(levelname)s;%(lineno)d;%(message)s",datefmt='%Y/%m/%d %H:%M:%S',
)
file_handler.setFormatter(log_formatter)

access_log.addHandler(file_handler)



#def bo_create_volume_for_fromsnapshot_vm(session_scope, instance_id, snapshotRef, user_id, is_operator):
def bo_create_volume_for_fromsnapshot_vm(session_scope, create_volume_form):
    instance_id = create_volume_form["instance_id"]
    snapshotRef = create_volume_form["snapshotRef"]
    user_id = create_volume_form["user_id"]
    is_operator = create_volume_form["user_role_name"] == "operator"
    username = create_volume_form["username"]
    instance_name = create_volume_form["instance_name"]

    while True:
        client = Client()
        detail = client.NovaClient.openstack_get_server_detail(client, instance_id)
        if detail["status"] == "BUILD":
            print("is building...")
            time.sleep(6)  # 等待10秒再次检查状态
        else:
            break  # 如果状态不是"BUILD"，则跳出循环

    volumename = "%s-volume" % detail["name"]

    if detail["status"] == "ERROR" :
        print("-------------------------")
        print(detail)
        print("build error return...")
        access_log.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                        {"username": username,
                         "op": "快照创建虚拟机-云硬盘",
                         "object": instance_name,
                         "role": create_volume_form["user_role_name"],
                         "result": "失败",
                         })
        return "from snapshot build vm error"
    print("--------------------")
    print(detail["status"])
    print("--------------------")
    
    #print(detail["volumeid"])
    res = client.VolumeClient.openstack_edit_volume(client,detail["volumeid"],volumename)
    print("id_operator",is_operator)
    if is_operator:
        print("create_volume_form!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!")
        with session_scope() as session:
            volume_first = session.query(Volumes).filter(Volumes.user_id==user_id, Volumes.vm_id==instance_id, Volumes.volume_id==snapshotRef).first()
            print("volume_first______前")
            if not volume_first:
                print("create_volume_form!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!____back",volume_first)
                my_volume = Volumes()
                my_volume.vm_id = instance_id
                my_volume.user_id = user_id
                my_volume.volume_id = detail["volumeid"]
                session.add(my_volume)
                
    access_log.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                    {"username": username,
                     "op": "快照创建虚拟机-云硬盘",
                     "object": instance_name,
                     "role": create_volume_form["user_role_name"],
                     "result": "成功",
                     })            

    return "ok"   

class InstanceAttachForm(object):
    id : str
    vmid : str

def bo_mount_ex_volume_for_vm(session_scope):
    print("enter run bo_mount_ex_volume_for_vm task ................")
    #session.commit()
    
    with session_scope() as session:
    
        try:
            #mytask = session.query(Task).with_for_update(nowait=False).filter(Task.method=="instancecreatefrom").first()
            mytask = session.query(Task).filter(Task.method=="ex_volume").with_for_update(nowait=False).first()
            if mytask:
                mytask.param = mytask.param + 1
                session.add(mytask)
        except Exception as e:
            #traceback.print_exc()
            print("bo_mount_ex_volume_for_vm   locking !!!!")
            return "locking "                
        
        
        ex_task = session.query(ExVolumeCreateTask).filter(ExVolumeCreateTask.status=='ready').first()
        if ex_task:
            
            try:
                vm_task_id = ex_task.create_task_id
                volume_id = ex_task.volume_id
                
                client = Client()
                volume = client.VolumeClient.openstack_get_volume_detail(client, volume_id)
                if volume["status"] != "available":
                    print("volume:%s not available" % volume_id)
                    return 
                
                create_vm_task = session.query(InstanceCreateTask).filter(InstanceCreateTask.id==vm_task_id, InstanceCreateTask.status=="complete").first()
                if not create_vm_task:
                    print("desk create task: %s no complete" % vm_task_id)
                    return

                if create_vm_task:
                    instance = client.NovaClient.openstack_getpost_server_detail(client,create_vm_task.vmid)
                    if instance["status"] != "ACTIVE":
                        print("vm:%s status not ACTIVE" % create_vm_task.vmid)
                        return                
                
                    #挂载云硬盘
                    attach_form = InstanceAttachForm()
                    attach_form.id = volume_id
                    attach_form.vmid = create_vm_task.vmid
                
                    host = client.NovaClient.openstack_attach_volume_to_server(client,attach_form)
                    
                    session.query(ExVolumeCreateTask).filter(ExVolumeCreateTask.id==ex_task.id).update({"status":"complete"})
            
            except Exception as e:
                traceback.print_exc()
                session.query(ExVolumeCreateTask).filter(ExVolumeCreateTask.id==ex_task.id).update({"status":"error"}) 
                session.commit()            
            


def bo_create_vm_when_volume_finished(session_scope ):
    print("enter run bo_create_vm_when_volume_finished task ................")
    #session.commit()

    with session_scope() as session:

        try:
            #mytask = session.query(Task).with_for_update(nowait=False).filter(Task.method=="instancecreatefrom").first()
            mytask = session.query(Task).filter(Task.method=="instancecreatefrom").with_for_update(nowait=False).first()
            if mytask:
                mytask.param = mytask.param + 1
                session.add(mytask)
        except Exception as e:
            #traceback.print_exc()
            print("bo_create_vm_when_volume_finished   locking !!!!")
            return "locking "

        print("get  bo_create_vm_when_volume_finished task !!!!")

        ins = session.query(InstanceCreateTask).filter(InstanceCreateTask.status=='ready').first()
        if not ins:
            return "no task"
        
        print("--------------找到任务----------------")
        print("--------打印任务信息开始 --------")
        user = session.query(User).filter(User.id==ins.user_id).first()
        print("用户ID: %s" % ins.user_id)
        print("用户角色: %s" % user.role_name)
        print("IP地址: %s" % ins.ipv4)
        print("ISO: %s" % ins.iso)
        print("操作系统: %s" % ins.os_type)
        print("vm_id: %s" % ins.vmid)
        print("task_id: %s" % ins.id)
        print("name: %s" % ins.name)
        print("--------打印任务信息结束 --------")

        client = Client()


        if ins.iso != "ISO":
            create_vm_with_available_volume(session, ins, user, client)
            #attach_volume_to_vm_when_available_volume(session, ins, user, client)

        if ins.iso == "ISO":
            create_vm_with_iso(session, ins, user, client)
            #attach_volume_create_vm_with_iso(session, ins, user, client)

    print("end  bo_create_vm_when_volume_finished task .........................")
    return "ok" 

def create_vm_with_iso(session, ins, user, client):
    # sourcery skip: low-code-quality
    try:
        drive_ok = False

        if ins.driver_volume_id:
            volume_driver = client.VolumeClient.openstack_get_volume_detail(client,ins.driver_volume_id)
            if volume_driver["status"] == "available":
                drive_ok = True


        else:
            drive_ok = True


        volume = client.VolumeClient.openstack_get_volume_detail(client,ins.volume_id)

        if volume["status"] == "available" and drive_ok:
            print("-----volume_iso:ok!!-------")

            flavor_res = client.FlavorClient.openstack_get_flavor_detail(client,ins.flavorRef)

            print(flavor_res)

            create_form = VolumeCreateFrom()
            create_form.name = ins.name
            create_form.size = flavor_res["disk"]
            create_form.imageRef = ""
            create_form.description = ""

            volumenew = client.VolumeClient.openstack_create_blank_volume(client,create_form)
            print(volumenew)
            while volumenew["status"] != "available":
                time.sleep(6)
                volumenew = client.VolumeClient.openstack_get_volume_detail(client,volumenew["id"])

            if ins.driver_volume_id:
                form = InstanceISOCreateFromDriver()
                form.flavorRef = ins.flavorRef
                form.name = ins.name
                form.imageRef = ins.imageRef
                form.uuid2 = ins.volume_id
                form.availability_zone = ins.availability_zone
                form.uuid = volumenew["id"]
                form.networkRef = ins.networkRef
                form.uuid_driver = volume_driver["id"]
                form.ipv4 = ins.ipv4
                form.os_type = ins.os_type
                insnew = client.NovaClient.openstack_create_server_with_iso_driver(client, form  ) 
            else : 
                form = InstanceISOCreateFrom()
                form.flavorRef = ins.flavorRef
                form.name = ins.name
                form.imageRef = ins.imageRef
                form.uuid2 = ins.volume_id
                form.availability_zone = ins.availability_zone
                form.uuid = volumenew["id"]
                form.networkRef = ins.networkRef
                form.ipv4 = ins.ipv4
                form.os_type = ins.os_type
                print("jjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjjj")
                print(form.__dict__)
                                #insnew = client.NovaClient.openstack_create_server_with_iso(client, form  ) 

                if hasattr(settings, 'ARM'):
                    insnew = client.NovaClient.openstack_create_server_with_iso_arm(client, form  )
                else:
                    insnew = client.NovaClient.openstack_create_server_with_iso(client, form  ) 


            print("xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx")
            print(insnew["id"])
            instance = client.NovaClient.openstack_getpost_server_detail(client,insnew["id"])

            while instance["status"] != "ACTIVE":
                instance = client.NovaClient.openstack_getpost_server_detail(client,instance["id"])
                time.sleep(6)

            insnew = client.NovaClient.openstack_edit_iso_server(client,instance["id"],"iso")

            session.query(InstanceCreateTask).filter(InstanceCreateTask.id==ins.id).update({"status":"complete", "vmid": instance["id"]})
            new_logger.log(
                user.username,
                "虚机操作",
                "创建虚拟机",
                "成功",
                user.role_name,
                f'{user.role_name} 使用{ins.iso}创建虚拟机: {instance["name"]}({instance["id"]}),成功',
            )
            if user.role_name == "operator":
                print("operator")
                group = session.query(VmGroup).filter(and_( VmGroup.user_id==ins.user_id,VmGroup.pid== -1 ) ).first()

                vm = Vms()
                vm.vmid = instance["id"]
                vm.user_id = ins.user_id
                vm.vmgroup_id = group.id
                session.add(vm)

                my_volume_iso = Volumes()
                my_volume_iso.vm_id = instance["id"]
                my_volume_iso.user_id = ins.user_id
                my_volume_iso.volume_id = ins.volume_id
                session.add(my_volume_iso)

                my_volume = Volumes()
                my_volume.vm_id = instance["id"]
                my_volume.user_id = ins.user_id
                my_volume.volume_id = volumenew["id"]
                session.add(my_volume)

                my_volume_driver = Volumes()
                my_volume_driver.vm_id = instance["id"]
                my_volume_driver.user_id = ins.user_id
                my_volume_driver.volume_id = ins.driver_volume_id
                session.add(my_volume_driver)

                print("add vms volumes")
            session.commit()

        elif volume["status"] == "error":
            session.query(InstanceCreateTask).filter(InstanceCreateTask.id==ins.id).update({"status":"error"})
            session.commit()
            new_logger.log(
                user.username,
                "虚机操作",
                "创建虚拟机",
                "失败",
                user.role_name,
                f"{user.role_name} 使用{ins.iso}创建虚拟机: {ins.name}{ins.id},失败",
            )
    except Exception as e:
        traceback.print_exc()
        session.query(InstanceCreateTask).filter(InstanceCreateTask.id==ins.id).update({"status":"error"})
        new_logger.log(
            user.username,
            "虚机操作",
            "创建虚拟机",
            "失败",
            user.role_name,
            f"{user.role_name} 使用{ins.iso}创建虚拟机: {ins.name}({ins.id}),失败",
        )
        session.commit()


def attach_volume_create_vm_with_iso(session, ins, user, client):
    # sourcery skip: low-code-quality
    try:
        drive_ok = False

        ins = session.query(InstanceCreateTask).filter(InstanceCreateTask.id==ins.id).first()
        print("----------------重新查询任务----------------")
        print("ISO: %s" % ins.iso)
        print("操作系统: %s" % ins.os_type)
        print("vm_id: %s" % ins.vmid)
        print("task_id: %s" % ins.id)
        print("name: %s" % ins.name)
        print("----------------重新查询任务结束----------------")
        if ins.status != "ready":
            print("-----状态已修改了 重复执行-----------%s--" % ins.id)
            return

        instance = client.NovaClient.openstack_getpost_server_detail(client, ins.vmid)
        # if not instance or not instance.get("status", ""):
        #     return

        if instance["status"] in ["DELETED", "ERROR",  "UNKNOWN"]:
            print("空虚机状态异常 %s" % instance["status"])
            session.query(InstanceCreateTask).filter(InstanceCreateTask.id==ins.id).update({"status":"error"})
            session.commit()
            new_logger.log(
                user.username,
                "虚机操作",
                "创建虚拟机",
                "失败",
                user.role_name,
                f"{user.role_name} 使用{ins.iso}创建虚拟机: {ins.name},失败",
            )
            
            return

        volume = client.VolumeClient.openstack_get_volume_detail(client, ins.volume_id)
        if volume["status"] in ["error", "deleting", "error_deleting"]:
            print("云硬盘状态异常 %s" % volume["status"])
            session.query(InstanceCreateTask).filter(InstanceCreateTask.id==ins.id).update({"status":"error"})
            session.commit()
            new_logger.log(
                user.username,
                "虚机操作",
                "创建虚拟机",
                "失败",
                user.role_name,
                f"{user.role_name} 使用{ins.iso}创建虚拟机: {ins.name},失败",
            )
            #修改空虚机状态为error
            client.NovaClient.openstack_server_vm_set_status_error(client, ins.vmid)
            return

        if volume["status"] == "available" and instance["status"] == "ACTIVE":
            print("-----volume_iso:ok!!-------")
            # 挂载光驱盘
            # attach_form = VolumeAttachFrom()
            # attach_form.id = ins.volume_id
            # attach_form.vmid = ins.vmid

            attach_form = {
                "id": ins.volume_id,
                "vmid": ins.vmid,
                "device": "/dev/hda"
            }

            flavor_res = client.FlavorClient.openstack_get_flavor_detail(client,ins.flavorRef)

            print(flavor_res)

            create_form = VolumeCreateFrom()
            create_form.name = ins.name
            create_form.size = flavor_res["disk"]
            create_form.imageRef = ""
            create_form.description = ""

            volumenew = client.VolumeClient.openstack_create_blank_volume(client,create_form)
            print(volumenew)
            check_test = 1
            while volumenew["status"] != "available":
                time.sleep(6)
                volumenew = client.VolumeClient.openstack_get_volume_detail(client,volumenew["id"])
                check_test = check_test + 1
                if volumenew["status"] in ["error", "deleting", "error_deleting"] or check_test > 10:
                    print("创建新的空白云硬盘失败 超过时间或错误状态 ------ 最终状态是 %s" % volumenew["status"])
                    session.query(InstanceCreateTask).filter(InstanceCreateTask.id==ins.id).update({"status":"error"})
                    session.commit()
                    new_logger.log(
                        user.username,
                        "虚机操作",
                        "创建虚拟机",
                        "失败",
                        user.role_name,
                        f"{user.role_name} 使用{ins.iso}创建虚拟机: {ins.name}{ins.id},失败",
                    )
                    return

            # 删除虚机网络
            detail = client.NovaClient.openstack_get_server_detail_v2(client, ins.vmid)
            empty_server_net = detail.get("addresses", "")
            print("网络信息：", empty_server_net)
            if empty_server_net != "":
                mac_addr = empty_server_net[list(empty_server_net.keys())[0]][0]['OS-EXT-IPS-MAC:mac_addr']
                interface = client.NeutronClient.openstack_get_all_filter_ports(client, f"mac_address={mac_addr}")
                if not interface:
                    print("异常的网络端口：mac_addr：{}".format(mac_addr))
                if interface and interface[0].id:
                    now_port_id = interface[0].id
                    detach_and_delete(client, ins.vmid, now_port_id)

            #软删除空虚机
            client.NovaClient.openstack_delete_server(client, ins.vmid)

            if ins.driver_volume_id:
                form = InstanceISOCreateFromDriver()
                form.flavorRef = ins.flavorRef
                form.name = ins.name
                form.imageRef = ins.imageRef
                form.uuid2 = ins.volume_id
                form.availability_zone = ins.availability_zone
                form.uuid = volumenew["id"]
                form.networkRef = ins.networkRef
                form.uuid_driver = ins.driver_volume_id
                form.ipv4 = ins.ipv4
                form.os_type = ins.os_type                               
                print("下面是ISO有驱动创建虚机参数:")
                print(form.__dict__)

                insnew = client.NovaClient.openstack_create_server_with_iso_driver(client, form  ) 
            else : 
                form = InstanceISOCreateFrom()
                form.flavorRef = ins.flavorRef
                form.name = ins.name
                form.imageRef = ins.imageRef
                form.uuid2 = ins.volume_id
                form.availability_zone = ins.availability_zone
                form.uuid = volumenew["id"]
                form.networkRef = ins.networkRef
                form.ipv4 = ins.ipv4
                form.os_type = ins.os_type
                print("下面是ISO无驱动创建虚机参数:")
                print(form.__dict__)
                

                if hasattr(settings, 'ARM'):
                    insnew = client.NovaClient.openstack_create_server_with_iso_arm(client, form  )
                else:
                    insnew = client.NovaClient.openstack_create_server_with_iso(client, form  ) 


            
            """  下面代码在创建虚机完成时 在虚机描述中加了一个iso的描述，但是这个描述是不对的，所以注释掉,找不到任何理由加这个描述
                    如果需要使用，请把ins.vmid 参数更改为 insnew["id"]
            instance = client.NovaClient.openstack_getpost_server_detail(client,ins.vmid)

            while instance["status"] != "ACTIVE":
                instance = client.NovaClient.openstack_getpost_server_detail(client,ins.vmid)
                time.sleep(6)

            insnew = client.NovaClient.openstack_edit_iso_server(client,ins.vmid,"iso")
            """

            time.sleep(6)

            #硬删除空虚机
            instanceform = {
                "id": ins.vmid,
                "action": "forceDelete",
            }
            client.NovaClient.openstack_server_action_v2(client, instanceform)

            if user.role_name == "operator":
                print("operator")
                group = session.query(VmGroup).filter(and_( VmGroup.user_id==ins.user_id,VmGroup.pid== -1 ) ).first()

                vm = Vms()
                vm.vmid = ins.vmid
                vm.user_id = ins.user_id
                vm.vmgroup_id = group.id
                session.add(vm)

                my_volume_iso = Volumes()
                my_volume_iso.vm_id = ins.vmid
                my_volume_iso.user_id = ins.user_id
                my_volume_iso.volume_id = ins.volume_id
                session.add(my_volume_iso)

                my_volume = Volumes()
                my_volume.vm_id = ins.vmid
                my_volume.user_id = ins.user_id
                my_volume.volume_id = volumenew["id"]
                session.add(my_volume)

                my_volume_driver = Volumes()
                my_volume_driver.vm_id = instance["id"]
                my_volume_driver.user_id = ins.user_id
                my_volume_driver.volume_id = ins.driver_volume_id
                session.add(my_volume_driver)

                print("add vms volumes")
            session.commit()


            #启动数据盘 在虚机创建完成后
            new_instance = client.NovaClient.openstack_getpost_server_detail(client,insnew["id"])
            while new_instance["status"] != "ACTIVE":
                time.sleep(6)
                if new_instance["status"] in ["DELETED", "ERROR",  "UNKNOWN"]:
                    print("真虚机状态异常 %s" % new_instance["status"])
                    session.query(InstanceCreateTask).filter(InstanceCreateTask.id==ins.id).update({"status":"error"})
                    session.commit()
                    new_logger.log(
                        user.username,
                        "虚机操作",
                        "创建虚拟机",
                        "失败",
                        user.role_name,
                        f"{user.role_name} 使用{ins.iso}创建虚拟机: {ins.name}{ins.id},失败",
                    )
                    return               
                new_instance = client.NovaClient.openstack_getpost_server_detail(client,insnew["id"])
            
            ex_task = session.query(ExVolumeCreateTask).filter(ExVolumeCreateTask.create_task_id==ins.id).first()
            if ex_task:
                volume = client.VolumeClient.openstack_get_volume_detail(client, ex_task.volume_id)
                check_test = 0
                while volume["status"] != "available":
                    time.sleep(6)
                    print("volume:%s not available" % ex_task.volume_id)
                    volumenew = client.VolumeClient.openstack_get_volume_detail(client,ex_task.volume_id)
                    check_test = check_test + 1
                    if volumenew["status"] in ["error", "deleting", "error_deleting"] or check_test > 10:
                        print("创建新的空白云硬盘失败 超过时间或错误状态 ------ 最终状态是 %s" % volumenew["status"]) 
                        session.query(ExVolumeCreateTask).filter(ExVolumeCreateTask.id==ex_task.id).update({"status":"error"}) 
                                   
                #挂载数据盘
                attach_form = InstanceAttachForm()
                attach_form.id = ex_task.volume_id
                attach_form.vmid = insnew["id"]
            
                host = client.NovaClient.openstack_attach_volume_to_server(client,attach_form)
                
                session.query(ExVolumeCreateTask).filter(ExVolumeCreateTask.id==ex_task.id).update({"status":"complete"})    


            #虚机创建成功
            session.query(InstanceCreateTask).filter(InstanceCreateTask.id==ins.id).update({"status":"complete", "vmid": ins.vmid})
            new_logger.log(
                user.username,
                "虚机操作",
                "创建虚拟机",
                "成功",
                user.role_name,
                f'{user.role_name} 使用{ins.iso}创建虚拟机: {instance["name"]}({instance["id"]}),成功',
            )

    except Exception as e:
        traceback.print_exc()
        session.query(InstanceCreateTask).filter(InstanceCreateTask.id==ins.id).update({"status":"error"})
        new_logger.log(
            user.username,
            "虚机操作",
            "创建虚拟机",
            "失败",
            user.role_name,
            f"{user.role_name} 使用{ins.iso}创建虚拟机: {ins.name},失败",
        )
        session.commit()

def detach_and_delete(client, form_id, now_portid):
    try:
        client.NovaClient.openstack_detach_interface(client, form_id, now_portid)
        time.sleep(7)
        client.NeutronClient.openstack_delete_port(client, now_portid)
    except Exception as e:
        print("删除网络出错：", e)



def create_vm_with_available_volume(session, ins, user, client):
    try:
        volume = client.VolumeClient.openstack_get_volume_detail(client,ins.volume_id)
        if volume["status"] == "available":
            form = InstanceCreateFrom()
            form.flavorRef = ins.flavorRef
            form.name = ins.name
            form.imageRef = ins.imageRef
            form.uuid = ins.volume_id
            form.availability_zone = ins.availability_zone
            form.networkRef = ins.networkRef
            form.ipv4 = ins.ipv4
            form.os_type = ins.os_type

            instance = client.NovaClient.openstack_create_server(client, form  )
            session.query(InstanceCreateTask).filter(InstanceCreateTask.id==ins.id).update({"status":"complete", "vmid": instance["id"]})
            new_logger.log(
                user.username,
                "虚机操作",
                "创建虚拟机",
                "成功",
                user.role_name,
                f'{user.role_name} 使用{ins.iso}创建虚拟机: {ins.name}({instance["id"]}),成功',
            )
            if user.role_name == "operator":
                group = session.query(VmGroup).filter(and_( VmGroup.user_id==ins.user_id,VmGroup.pid== -1 ) ).first()
                print(f"groupid:{group.id}")
                print("111")
                print(instance)
                vm = Vms()
                vm.vmid = instance["id"]
                vm.user_id = ins.user_id
                vm.vmgroup_id = group.id
                session.add(vm)
                print("2222")
                my_volume = Volumes()
                my_volume.vm_id = instance["id"]
                my_volume.user_id = ins.user_id
                my_volume.volume_id = ins.volume_id
                session.add(my_volume)
                print("33333")

            session.commit()

        elif volume["status"] == "error":
            print("555")
            session.query(InstanceCreateTask).filter(InstanceCreateTask.id==ins.id).update({"status":"error"})
            session.commit()
            new_logger.log(
                user.username,
                "虚机操作",
                "创建虚拟机",
                "失败",
                user.role_name,
                f"{user.role_name} 使用{ins.iso}创建虚拟机: {ins.name},失败",
            )
    except Exception as e:
        traceback.print_exc()
        session.query(InstanceCreateTask).filter(InstanceCreateTask.id==ins.id).update({"status":"error"})
        new_logger.log(
            user.username,
            "虚机操作",
            "创建虚拟机",
            "失败",
            user.role_name,
            f"{user.role_name} 使用{ins.iso}创建虚拟机: {ins.name},失败",
        )
        session.commit()


def attach_volume_to_vm_when_available_volume(session, ins, user, client):
    try:
        
        ins = session.query(InstanceCreateTask).filter(InstanceCreateTask.id==ins.id).first()
        print("----------------重新查询任务----------------")
        print("ISO: %s" % ins.iso)
        print("操作系统: %s" % ins.os_type)
        print("vm_id: %s" % ins.vmid)
        print("task_id: %s" % ins.id)
        print("name: %s" % ins.name)
        print("----------------重新查询任务结束----------------")
        if ins.status != "ready":
            print("-----状态已修改了 重复执行-----------%s--" % ins.id)
            return

        instance = client.NovaClient.openstack_getpost_server_detail(client,ins.vmid)
        volume = client.VolumeClient.openstack_get_volume_detail(client,ins.volume_id)
        # if not instance or not instance.get("status", ""):
        #     return

        if instance["status"] in ["DELETED", "ERROR", "UNKNOWN"]:
            print("空虚机状态异常 %s" % instance["status"])
            session.query(InstanceCreateTask).filter(InstanceCreateTask.id==ins.id).update({"status":"error"})
            session.commit()
            new_logger.log(
                user.username,
                "虚机操作",
                "创建虚拟机",
                "失败",
                user.role_name,
                f"{user.role_name} 使用{ins.iso}创建虚拟机: {ins.name},失败",
            )
            return

        if volume["status"] in ["error", "deleting", "error_deleting"]:
            print("云硬盘状态异常 %s" % volume["status"])
            session.query(InstanceCreateTask).filter(InstanceCreateTask.id==ins.id).update({"status":"error"})
            session.commit()
            new_logger.log(
                user.username,
                "虚机操作",
                "创建虚拟机",
                "失败",
                user.role_name,
                f"{user.role_name} 使用{ins.iso}创建虚拟机: {ins.name},失败",
            )
            #修改空虚机状态为error
            client.NovaClient.openstack_server_vm_set_status_error(client, ins.vmid)
            return
        
        if volume["status"] == "available" and instance["status"] == "ACTIVE":


            # 删除虚机网络
            detail = client.NovaClient.openstack_get_server_detail_v2(client, ins.vmid)
            empty_server_net = detail.get("addresses", "")
            print("网络信息：", empty_server_net)
            if empty_server_net != "":
                mac_addr = empty_server_net[list(empty_server_net.keys())[0]][0]['OS-EXT-IPS-MAC:mac_addr']
    
                interface = client.NeutronClient.openstack_get_all_filter_ports(client, f"mac_address={mac_addr}")
                if not interface:
                    print("异常的网络端口：mac_addr：{}".format(mac_addr))
                if interface and interface[0].id:
                    now_port_id = interface[0].id
                    detach_and_delete(client, ins.vmid, now_port_id)

            # 软删了空虚机
            print("软删除虚拟机")
            start_time = time.time()
            client.NovaClient.openstack_delete_server(client, ins.vmid)
            end_time = time.time()
            execution_time = end_time - start_time
            print(f"-------------------------- Execution time for openstack_delete_server: {execution_time:.4f} seconds")

            #创建虚机
            form = InstanceCreateFrom()
            form.flavorRef = ins.flavorRef
            form.name = ins.name
            form.imageRef = ins.imageRef
            form.uuid = ins.volume_id
            form.availability_zone = ins.availability_zone
            form.networkRef = ins.networkRef
            form.ipv4 = ins.ipv4
            form.os_type = ins.os_type
            insnew = client.NovaClient.openstack_create_server(client, form  )
            

            time.sleep(6)

            #硬删除空虚机
            instanceform = {
                "id": ins.vmid,
                "action": "forceDelete",
            }
            print("硬删除虚拟机")
            client.NovaClient.openstack_server_action_v2(client, instanceform)

            if user.role_name == "operator":
                group = session.query(VmGroup).filter(and_( VmGroup.user_id==ins.user_id,VmGroup.pid== -1 ) ).first()
                print(f"groupid:{group.id}")
                print("111")
                vm = Vms()
                vm.vmid = ins.vmid
                vm.user_id = ins.user_id
                vm.vmgroup_id = group.id
                session.add(vm)
                print("2222")
                my_volume = Volumes()
                my_volume.vm_id = ins.vmid
                my_volume.user_id = ins.user_id
                my_volume.volume_id = ins.volume_id
                session.add(my_volume)
                print("33333")

            session.commit()


            #启动数据盘 在虚机创建完成后
            new_instance = client.NovaClient.openstack_getpost_server_detail(client,insnew["id"])
            while new_instance["status"] != "ACTIVE":
                time.sleep(6)
                if new_instance["status"] in ["DELETED", "ERROR",  "UNKNOWN"]:
                    print("真虚机状态异常 %s" % new_instance["status"])
                    session.query(InstanceCreateTask).filter(InstanceCreateTask.id==ins.id).update({"status":"error"})
                    session.commit()
                    new_logger.log(
                        user.username,
                        "虚机操作",
                        "创建虚拟机",
                        "失败",
                        user.role_name,
                        f"{user.role_name} 使用{ins.iso}创建虚拟机: {ins.name},失败",
                    )
                    return               
                new_instance = client.NovaClient.openstack_getpost_server_detail(client,insnew["id"])
            
            ex_task = session.query(ExVolumeCreateTask).filter(ExVolumeCreateTask.create_task_id==ins.id).first()
            if ex_task:
                volume = client.VolumeClient.openstack_get_volume_detail(client, ex_task.volume_id)
                check_test = 0
                while volume["status"] != "available":
                    time.sleep(6)
                    print("volume:%s not available" % ex_task.volume_id)
                    volumenew = client.VolumeClient.openstack_get_volume_detail(client,ex_task.volume_id)
                    check_test = check_test + 1
                    if volumenew["status"] in ["error", "deleting", "error_deleting"] or check_test > 10:
                        print("创建新的空白云硬盘失败 超过时间或错误状态 ------ 最终状态是 %s" % volumenew["status"]) 
                        session.query(ExVolumeCreateTask).filter(ExVolumeCreateTask.id==ex_task.id).update({"status":"error"}) 
                                   
                #挂载云硬盘
                attach_form = InstanceAttachForm()
                attach_form.id = ex_task.volume_id
                attach_form.vmid = insnew["id"]
            
                host = client.NovaClient.openstack_attach_volume_to_server(client,attach_form)
                
                session.query(ExVolumeCreateTask).filter(ExVolumeCreateTask.id==ex_task.id).update({"status":"complete"})            

            
            
                
            #写入日志成功创建虚机
            session.query(InstanceCreateTask).filter(InstanceCreateTask.id==ins.id).update({"status":"complete", "vmid": ins.vmid})
            new_logger.log(
                user.username,
                "虚机操作",
                "创建虚拟机",
                "成功",
                user.role_name,
                f'{user.role_name} 使用{ins.iso}创建虚拟机: {ins.name},成功',
            )                

    except Exception as e:
        traceback.print_exc()
        session.query(InstanceCreateTask).filter(InstanceCreateTask.id==ins.id).update({"status":"error"})
        new_logger.log(
            user.username,
            "虚机操作",
            "创建虚拟机",
            "失败",
            user.role_name,
            f"{user.role_name} 使用{ins.iso}创建虚拟机: {ins.name},失败",
        )
        session.commit()


def bo_upload_image_to_ceph(session_scope ):
    
    a = datetime.now()
    
    print("enter run bo_upload_image_to_ceph task ................")
    
    with session_scope() as session:
        try:
            #mytask = session.query(Task).with_for_update(nowait=False).filter(Task.method=="imagescreatefrom").first()
            #mytask = session.query(Task).filter(Task.method=="imagescreatefrom").with_for_update(nowait=False).first()
            #mytask = session.query(Task).filter(Task.method=="imagescreatefrom").with_for_update(nowait=False).first()
            ins = session.query(ImageCreateTask).filter(ImageCreateTask.status=='ready').with_for_update(nowait=False).first()
            ins.status = "complete"
            session.add(ins)
        except Exception as e:
            #traceback.print_exc()
            print("bo_upload_image_to_ceph   locking !!!!")
            return "locking "
        
        
        print("get  bo_upload_image_to_ceph task !!!!")
        
        #ins = session.query(ImageCreateTask).filter(ImageCreateTask.status=='ready').first()
        
        print("11111111111")
        if ins:
            filename = ins.filename
            image_id = ins.image_id
            fd = open(filename, "rb")
            print("2222222222")
            
            try:
                client = Client()
                data = fd.read()
                print("333333333")
                res = client.ImageClient.openstack_upload_image(client, image_id, data)
                print("44444444444")
                #session.query(ImageCreateTask).filter(ImageCreateTask.id==ins.id).update({"status":"complete"})
                print("5555555555555")
                fd.close()
                os.remove(filename)
            except Exception as e:
                print("666666666666666")
                pass
    
            finally:
                fd.close()
                #os.remove(filename)


    
    
    
    b = datetime.now()    
    print("end bo_upload_image_to_ceph task ........................%d." % (b-a).seconds)
    
    
    return "ok"      
    
    
    
