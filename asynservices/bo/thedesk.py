from api.openstack.client import Client
from celery.schedules import crontab
from api.model.instances import InstanceCreateFrom
from api.model.instances import Instance<PERSON>reate<PERSON>rom,InstanceAction<PERSON>rom,InstanceISOEditFrom,InstanceISOCreateFrom,InstanceISOCreateFromDriver
from db.model.desk import Desk, DeskPool, DeskTemplate, DeskUser
from db.model.task import Task, DeskCreateTask, ImageCreateTask, DeskCreateTask, ExVolumeCreateDeskTask
from api.model.instances import InstanceCreateFrom
from api.model.volumes import VolumeCreateFrom
from db.model.vm import VmGroup , Vms,Volumes
from db.model.user import User
from sqlalchemy import and_
from sqlalchemy.orm import scoped_session, sessionmaker
from sqlalchemy import create_engine
import time
import settings
import traceback


class VolumeCreateFrom:
    name : str
    size : int
    imageRef: str
    description : str

class InstanceAttachForm(object):
    id : str
    vmid : str

class InstanceCreateFromV2(object):
    name : str
    imageRef: str
    flavorRef: str
    networkRef: str
    count: int
    uuid : str
    availability_zone : str
    ipv4 : str
    os_type: str

def bo_mount_desk_ex_volume_for_vm(session_scope):
    print("enter run bo_mount_ex_volume_for_vm task ................")
    #session.commit()
    
    with session_scope() as session:
    
        try:
            #mytask = session.query(Task).with_for_update(nowait=False).filter(Task.method=="instancecreatefrom").first()
            mytask = session.query(Task).filter(Task.method=="ex_volume").with_for_update(nowait=False).first()
            if mytask:
                mytask.param = mytask.param + 1
                session.add(mytask)
        except Exception as e:
            #traceback.print_exc()
            print("bo_mount_ex_volume_for_vm   locking !!!!")
            return "locking "                
        
        
        ex_task = session.query(ExVolumeCreateDeskTask).filter(ExVolumeCreateDeskTask.status=='ready').first()
        if ex_task:
            
            try:
                vm_task_id = ex_task.create_desk_task_id
                volume_id = ex_task.volume_id
                
                client = Client()
                volume = client.VolumeClient.openstack_get_volume_detail(client, volume_id)
                if volume["status"] != "available":
                    print("volume:%s not available" % volume_id)
                    return 
                
                create_vm_task = session.query(DeskCreateTask).filter(DeskCreateTask.id==vm_task_id, DeskCreateTask.status=="complete").first()
                if not create_vm_task:
                    print("desk create task: %s no complete" % vm_task_id)
                    return 
                
                desk = session.query(Desk).filter(Desk.id==create_vm_task.desk_id).first()
                
                if desk:
                    client = Client()
                    instance = client.NovaClient.openstack_getpost_server_detail(client,desk.vm_id)
                    print("-----------------111")
                    print(desk.vm_id)
                    print(vm_task_id)
                    print(volume_id)
                    print(instance)
                    print("-------------------")
                    if instance["status"] != "ACTIVE":
                        print("vm:%s status not ACTIVE" % desk.vm_id)
                        return                
                
                    #挂载云硬盘
                    attach_form = InstanceAttachForm()
                    attach_form.id = volume_id
                    attach_form.vmid = desk.vm_id
                
                    host = client.NovaClient.openstack_attach_volume_to_server(client,attach_form)
                    
                    session.query(ExVolumeCreateDeskTask).filter(ExVolumeCreateDeskTask.id==ex_task.id).update({"status":"complete"})
            
            except Exception as e:
                traceback.print_exc()
                session.query(ExVolumeCreateDeskTask).filter(ExVolumeCreateDeskTask.id==ex_task.id).update({"status":"error"}) 
                session.commit()         
                
def bo_create_desk_deskpoolid(session_scope, deskpoolid, template_id):
    
    with session_scope() as session:
        print("------------44444444444444----------------%s" % deskpoolid)
        pool = session.query(DeskPool).filter_by(id=deskpoolid).first()
        template = session.query(DeskTemplate).filter_by(id=template_id).first()

        create_vm_count = 0
        
        #检查是否要建的机器
        for desk in pool.desks:
            if not desk.vm_id:
                create_vm_count = create_vm_count + 1
                bo_create_single_desk_vm_new(session, desk, template)



def bo_desk_backup_restore(session_scope, vm_id, template_id, snap_id):


        with session_scope() as session:
            
            template = session.query(DeskTemplate).filter_by(id=template_id).first()
            desk = session.query(Desk).filter_by(vm_id=vm_id).first()
            
            client = Client()
            ipv4 = ""
            # subnet_id = client.NeutronClient.openstack_get_subnet_from_network(client, template.network_id)
            subnet_id = template.subnet_id
            vmport = client.NeutronClient.openstack_create_port(client, template.network_id, ipv4, subnet_id)
    
            #创建volume
            create_form = VolumeCreateFrom()
            create_form.name = desk.name
            create_form.size = template.vdisk_gb
            create_form.imageRef = snap_id
            create_form.description = ""
            
            print("------------")
            print(create_form)
            volume = client.VolumeClient.openstack_create_volume_from_snap_id(client, create_form)
            print(volume)
            print("--------------")
            
            #写入异步任务                  
            task = DeskCreateTask()
            task.name = desk.name
            task.flavorRef = template.flavor_id
            #task.imageRef = snap_id
            #task.imageRef = template.glance_id
            task.imageRef = ""
            task.volume_id = volume["id"]
            task.iso  = "false"
            task.availability_zone = template.availability_zone
            task.user_id = 1
            task.desk_id = desk.id
            task.networkRef  = template.network_id
            task.ipv4 = vmport
    
            session.add(task)
            session.commit()

def bo_create_desk_deskpoolid_userids(session_scope, deskpoolid, template_id, ids):
    
    with session_scope() as session:
        print("------------44444444444444----------------%s" % deskpoolid)
        pool = session.query(DeskPool).filter_by(id=deskpoolid).first()
        template = session.query(DeskTemplate).filter_by(id=template_id).first()
        
        
        #print(pool)
        i = 0
        user_count = len(ids)
        for desk in pool.desks:
            print(desk.id)
            print(user_count)
            print(i)
            print(ids)
            if i < user_count:
                desk_user = session.query(DeskUser).filter_by(id=ids[i]).first()
                if desk_user:
                    desk.manual_deskusers.append(desk_user)
                    session.add(desk)
                    session.flush()
                
            #bo_create_single_desk_vm(session, desk.id, imageRef, flavorRef, networkRef)
            bo_create_single_desk_vm_new(session, desk, template)
            i = i + 1


def bo_create_single_desk_vm_new(session, desk, template):
        
        client = Client()
        vmport = client.NeutronClient.openstack_create_port_withoutip(client,
                                                                      template.network_id,
                                                                      template.subnet_id,
                                                                      desk.name)

        #创建volume
        create_form = VolumeCreateFrom()
        create_form.name = desk.name
        create_form.size = template.vdisk_gb
        create_form.imageRef = template.glance_id
        create_form.description = ""
                
        volume = client.VolumeClient.openstack_create_volume(client, create_form)
        
        #写入异步任务                  
        task = DeskCreateTask()
        task.name = desk.name
        task.flavorRef = template.flavor_id
        task.imageRef = template.glance_id
        task.volume_id = volume["id"]
        task.iso  = "false"
        task.availability_zone = template.availability_zone
        task.user_id = 1
        task.desk_id = desk.id
        task.networkRef  = template.network_id
        task.ipv4 = vmport
        task.os_type = template.os_type
        session.add(task)
        session.commit()
        
        if template.ex_volume_size:
            exv =  VolumeCreateFrom()
            exv.name = "%s_数据盘" %  desk.name
            exv.size =  template.ex_volume_size
            exv.description = "%s虚机的数据盘" %  desk.name

            res = client.VolumeClient.openstack_create_blank_volume(client,exv)
            client.VolumeClient.openstack_update_volume_bootable(client, res["id"])
            
            ex_task = ExVolumeCreateDeskTask()
            ex_task.volume_id = res["id"]
            ex_task.size = template.ex_volume_size
            ex_task.create_desk_task_id = task.id
            
            session.add(ex_task)
            session.commit()            



def bo_create_desk_when_volume_finished(session_scope ):
    print("enter run bo_create_desk_when_volume_finished task ................")
    #session.commit()
    
    with session_scope() as session:
    
        try:
            #mytask = session.query(Task).with_for_update(nowait=False).filter(Task.method=="instancecreatefrom").first()
            mytask = session.query(Task).filter(Task.method=="instancecreatefrom").with_for_update(nowait=False).first()
            if mytask:
                mytask.param = mytask.param + 1
                session.add(mytask)
        except Exception as e:
            #traceback.print_exc()
            print("bo_create_vm_when_volume_finished   locking !!!!")
            return "locking "
        
        print("get  bo_create_vm_when_volume_finished task !!!!")
        
        ins = session.query(DeskCreateTask).filter(DeskCreateTask.status=='ready').first()
        if ins:
            print("---------------")
            print(ins.user_id)
            user = session.query(User).filter(User.id==ins.user_id).first()
            print(user.role_name)
            print(ins.ipv4)
            print(ins.iso)
            print("----------------")
        
            try:
                if ins.iso == "false":
                    client = Client()
                    
                    volume = client.VolumeClient.openstack_get_volume_detail(client,ins.volume_id)
                    if volume["status"] == "available":
    
                        form = InstanceCreateFromV2()
                        form.flavorRef = ins.flavorRef
                        form.name = ins.name
                        form.imageRef = ins.imageRef
                        form.uuid = ins.volume_id
                        form.availability_zone = ins.availability_zone
                        form.networkRef = ins.networkRef
                        form.ipv4 = ins.ipv4
                        form.os_type = ins.os_type
                        
                        if ins.imageRef == "":
                            instance = client.NovaClient.openstack_create_server_from_snapshot(client, form  )
                        else:
                            instance = client.NovaClient.openstack_create_server(client, form  )  
                        
                        
                        
                        session.query(DeskCreateTask).filter(DeskCreateTask.id==ins.id).update({"status":"complete"})
                        session.query(Desk).filter(Desk.id==ins.desk_id).update({"vm_id":instance["id"]})
                        
                        if user.role_name == "operator":
                            group = session.query(VmGroup).filter(and_( VmGroup.user_id==ins.user_id,VmGroup.pid== -1 ) ).first()
                            print("groupid:%s" % group.id)
                            print("111")
                            print(instance)
                            vm = Vms()
                            vm.vmid = instance["id"]
                            vm.user_id = ins.user_id
                            vm.vmgroup_id = group.id
                            session.add(vm)
                            print("2222")
                            my_volume = Volumes()
                            my_volume.vm_id = instance["id"]
                            my_volume.user_id = ins.user_id
                            my_volume.volume_id = ins.volume_id
                            session.add(my_volume)
                            print("33333")
                        
                        session.commit()
                    
                    elif volume["status"] == "error":
                        print("create volume error")
                        session.query(DeskCreateTask).filter(DeskCreateTask.id==ins.id).update({"status":"error"}) 
                        return 
                        #session.commit()
                        
                elif ins.iso == "true":
                    client = Client()
                    drive_ok = False
                    
                    if ins.driver_volume_id:
                        volume_driver = client.VolumeClient.openstack_get_volume_detail(client,ins.driver_volume_id)
                        if volume_driver["status"] == "available":
                            drive_ok = True
                            
                        
                    else:
                        drive_ok = True
                    
                    
                    volume = client.VolumeClient.openstack_get_volume_detail(client,ins.volume_id)
                    
                    if volume["status"] == "available" and drive_ok:
                        print("-----volume_iso:ok!!-------")
                        
                        flavor_res = client.FlavorClient.openstack_get_flavor_detail(client,ins.flavorRef)
                        
                        print(flavor_res)
                        
                        create_form = VolumeCreateFrom()
                        create_form.name = ins.name
                        create_form.size = flavor_res["disk"]
                        create_form.imageRef = ""
                        create_form.description = ""
                        
                        volumenew = client.VolumeClient.openstack_create_blank_volume(client,create_form)
                        print(volumenew)
                        while volumenew["status"] != "available":
                            time.sleep(6)
                            volumenew = client.VolumeClient.openstack_get_volume_detail(client,volumenew["id"])
                        
                        if ins.driver_volume_id:
                            form = InstanceISOCreateFromDriver()
                            form.flavorRef = ins.flavorRef
                            form.name = ins.name
                            form.imageRef = ins.imageRef
                            form.uuid2 = ins.volume_id
                            form.availability_zone = ins.availability_zone
                            form.uuid = volumenew["id"]
                            form.networkRef = ins.networkRef
                            form.uuid_driver = volume_driver["id"]
                            form.ipv4 = ins.ipv4
                            insnew = client.NovaClient.openstack_create_server_with_iso_driver(client, form  ) 
                        else : 
                            form = InstanceISOCreateFrom()
                            form.flavorRef = ins.flavorRef
                            form.name = ins.name
                            form.imageRef = ins.imageRef
                            form.uuid2 = ins.volume_id
                            form.availability_zone = ins.availability_zone
                            form.uuid = volumenew["id"]
                            form.networkRef = ins.networkRef
                            form.ipv4 = ins.ipv4

                            if hasattr(settings, 'ARM'):
                                insnew = client.NovaClient.openstack_create_server_with_iso_arm(client, form  )
                            else:
                                insnew = client.NovaClient.openstack_create_server_with_iso(client, form  ) 
                        
                        
               
                        print(insnew["id"])
                        instance = client.NovaClient.openstack_getpost_server_detail(client,insnew["id"])
    
                        while instance["status"] != "ACTIVE":
                            instance = client.NovaClient.openstack_getpost_server_detail(client,instance["id"])
    
                        insnew = client.NovaClient.openstack_edit_iso_server(client,instance["id"],"iso")
    
                        session.query(DeskCreateTask).filter(DeskCreateTask.id==ins.id).update({"status":"complete"})  
                        if user.role_name == "operator":
                            print("operator")
                            group = session.query(VmGroup).filter(and_( VmGroup.user_id==ins.user_id,VmGroup.pid== -1 ) ).first()
    
                            vm = Vms()
                            vm.vmid = instance["id"]
                            vm.user_id = ins.user_id
                            vm.vmgroup_id = group.id
                            session.add(vm)
                            
                            my_volume_iso = Volumes()
                            my_volume_iso.vm_id = instance["id"]
                            my_volume_iso.user_id = ins.user_id
                            my_volume_iso.volume_id = ins.volume_id
                            session.add(my_volume_iso)
                            
                            my_volume = Volumes()
                            my_volume.vm_id = instance["id"]
                            my_volume.user_id = ins.user_id
                            my_volume.volume_id = volumenew["id"]
                            session.add(my_volume)
                            
                            my_volume_driver = Volumes()
                            my_volume_driver.vm_id = instance["id"]
                            my_volume_driver.user_id = ins.user_id
                            my_volume_driver.volume_id = ins.driver_volume_id
                            session.add(my_volume_driver)
                            
                            print("add vms volumes")
                        session.commit()
                        
                        
                        
                    elif volume["status"] == "error":
                        session.query(DeskCreateTask).filter(DeskCreateTask.id==ins.id).update({"status":"error"}) 
                        session.commit()
            except Exception as e:
                traceback.print_exc()
                session.query(DeskCreateTask).filter(DeskCreateTask.id==ins.id).update({"status":"error"}) 
                session.commit()

    print("end  bo_create_vm_when_volume_finished task .........................")
    return "ok"    
