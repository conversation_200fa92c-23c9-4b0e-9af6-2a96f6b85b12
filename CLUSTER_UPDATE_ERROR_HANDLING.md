# 主机聚合更新错误处理指南

## 问题描述

当尝试更新主机聚合的可用区时，遇到以下错误：

```json
{
  "badRequest": {
    "code": 400,
    "message": "Cannot update aggregate 2. Reason: One or more hosts contain instances in this zone."
  }
}
```

## 错误原因

这个错误表明：
1. 主机聚合中的某些主机上有正在运行的虚拟机实例
2. OpenStack 为了保证数据一致性，不允许在有活跃实例时更改可用区
3. 这是 OpenStack 的安全机制，防止实例的可用区信息不一致

## 解决方案

### 方案1: 智能部分更新（推荐）

我们的接口现在支持智能处理这种情况：

```bash
curl -X PUT http://localhost:8080/v1/update/clusters/full \
  -H "Content-Type: application/json" \
  -d '{
    "id": 2,
    "name": "updated-cluster-name",
    "availability_zone": "new-zone",
    "metadata": {
      "environment": "production",
      "updated": "2024-01-01"
    }
  }'
```

**响应示例（部分成功）**：
```json
{
  "msg": "partial_success",
  "updated": ["名称", "元数据"],
  "failed": ["可用区"],
  "error_message": "无法更新可用区：主机聚合中有 3 个正在运行的实例。涉及主机: host1, host2",
  "instance_info": {
    "has_instances": true,
    "instance_count": 3,
    "hosts_with_instances": ["host1", "host2"],
    "total_hosts": 3
  }
}
```

### 方案2: 分步更新

#### 步骤1: 先更新名称和元数据
```bash
curl -X PUT http://localhost:8080/v1/update/clusters/full \
  -H "Content-Type: application/json" \
  -d '{
    "id": 2,
    "name": "production-cluster",
    "metadata": {
      "environment": "production",
      "team": "infrastructure"
    }
  }'
```

#### 步骤2: 处理实例后更新可用区
```bash
# 处理完实例后
curl -X PUT http://localhost:8080/v1/update/clusters/full \
  -H "Content-Type: application/json" \
  -d '{
    "id": 2,
    "availability_zone": "new-production-zone"
  }'
```

### 方案3: 实例迁移方案

#### 选项A: 实例迁移
1. **迁移实例到其他主机**：
   ```bash
   # 使用 Nova API 迁移实例
   nova live-migration <instance-id> <target-host>
   ```

2. **更新可用区**：
   ```bash
   curl -X PUT http://localhost:8080/v1/update/clusters/full \
     -H "Content-Type: application/json" \
     -d '{"id": 2, "availability_zone": "new-zone"}'
   ```

3. **迁移实例回来**（可选）

#### 选项B: 停止实例方案
1. **停止所有实例**：
   ```bash
   # 停止实例
   nova stop <instance-id>
   ```

2. **更新可用区**
3. **重启实例**：
   ```bash
   # 重启实例
   nova start <instance-id>
   ```

#### 选项C: 新建聚合方案
1. **创建新的主机聚合**
2. **将主机添加到新聚合**
3. **从旧聚合移除主机**
4. **删除旧聚合**

## 接口改进

### 新增功能

1. **实例检查**：
   - 自动检查主机聚合中是否有运行的实例
   - 提供详细的实例信息

2. **智能更新**：
   - 自动处理部分更新成功的情况
   - 优先更新可以成功的字段

3. **详细错误信息**：
   - 提供具体的实例数量和主机信息
   - 给出明确的解决建议

### 响应类型

#### 完全成功
```json
{
  "msg": "ok"
}
```

#### 部分成功
```json
{
  "msg": "partial_success",
  "updated": ["名称", "元数据"],
  "failed": ["可用区"],
  "error_message": "详细错误信息",
  "instance_info": {
    "has_instances": true,
    "instance_count": 3,
    "hosts_with_instances": ["host1", "host2"],
    "total_hosts": 3
  }
}
```

#### 完全失败
```json
{
  "msg": "error",
  "error": "错误信息"
}
```

## 最佳实践

### 1. 生产环境建议
- 在维护窗口期间更新可用区
- 提前规划实例迁移策略
- 使用分步更新减少风险

### 2. 更新顺序建议
1. 先更新元数据（记录变更信息）
2. 再更新名称（如果需要）
3. 最后更新可用区（确保无实例运行）

### 3. 监控和日志
- 所有操作都会记录到系统日志
- 部分成功的操作会特别标记
- 可以通过日志追踪更新历史

## 故障排除

### 常见问题

1. **Q: 为什么不能强制更新可用区？**
   A: 这是 OpenStack 的安全机制，防止实例元数据不一致

2. **Q: 如何快速找到运行的实例？**
   A: 使用我们的接口会自动提供实例信息

3. **Q: 更新失败后如何回滚？**
   A: 名称和元数据的更新可以通过再次调用接口回滚

### 调试步骤

1. **检查响应信息**：
   ```bash
   # 查看详细的错误信息和实例信息
   ```

2. **验证实例状态**：
   ```bash
   # 确认实例是否真的在运行
   nova list --host <hostname>
   ```

3. **检查日志**：
   ```bash
   # 查看系统日志了解详细情况
   ```

## 测试

使用提供的测试脚本验证功能：

```bash
python test_cluster_with_instances.py
```

这个脚本会测试：
- 有实例运行时的更新行为
- 部分更新的处理
- 错误信息的准确性
