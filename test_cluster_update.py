#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试主机聚合更新接口的脚本
"""

import requests
import json

# 配置
BASE_URL = "http://localhost:8080"  # 根据实际情况修改
HEADERS = {
    "Content-Type": "application/json",
    "Accept": "application/json"
}

def test_update_cluster_name_only():
    """测试只更新集群名称的接口"""
    print("=== 测试更新集群名称接口 ===")
    
    url = f"{BASE_URL}/v1/update/clusters/name"
    data = {
        "id": 1,
        "name": "test-cluster-new-name"
    }
    
    try:
        response = requests.put(url, headers=HEADERS, data=json.dumps(data))
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("msg") == "ok":
                print("✅ 更新集群名称成功")
            else:
                print(f"❌ 更新失败: {result}")
        else:
            print(f"❌ 请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def test_update_cluster_full():
    """测试更新集群完整信息的接口"""
    print("\n=== 测试更新集群完整信息接口 ===")
    
    url = f"{BASE_URL}/v1/update/clusters/full"
    data = {
        "id": 1,
        "name": "test-cluster-full-update",
        "availability_zone": "test-zone",
        "metadata": {
            "environment": "test",
            "owner": "admin",
            "description": "测试集群"
        }
    }
    
    try:
        response = requests.put(url, headers=HEADERS, data=json.dumps(data))
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("msg") == "ok":
                print("✅ 更新集群完整信息成功")
            else:
                print(f"❌ 更新失败: {result}")
        else:
            print(f"❌ 请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def test_update_cluster_partial():
    """测试部分更新集群信息"""
    print("\n=== 测试部分更新集群信息 ===")
    
    # 只更新可用区
    url = f"{BASE_URL}/v1/update/clusters/full"
    data = {
        "id": 1,
        "availability_zone": "new-zone"
    }
    
    try:
        response = requests.put(url, headers=HEADERS, data=json.dumps(data))
        print(f"只更新可用区 - 状态码: {response.status_code}")
        print(f"响应: {response.text}")
        
    except Exception as e:
        print(f"❌ 请求异常: {e}")
    
    # 只更新元数据
    data = {
        "id": 1,
        "metadata": {
            "updated_at": "2024-01-01",
            "version": "2.0"
        }
    }
    
    try:
        response = requests.put(url, headers=HEADERS, data=json.dumps(data))
        print(f"只更新元数据 - 状态码: {response.status_code}")
        print(f"响应: {response.text}")
        
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def test_invalid_requests():
    """测试无效请求"""
    print("\n=== 测试无效请求 ===")
    
    url = f"{BASE_URL}/v1/update/clusters/full"
    
    # 缺少 ID
    data = {
        "name": "test-without-id"
    }
    
    try:
        response = requests.put(url, headers=HEADERS, data=json.dumps(data))
        print(f"缺少ID - 状态码: {response.status_code}")
        print(f"响应: {response.text}")
        
    except Exception as e:
        print(f"❌ 请求异常: {e}")
    
    # 空请求体
    try:
        response = requests.put(url, headers=HEADERS, data=json.dumps({}))
        print(f"空请求体 - 状态码: {response.status_code}")
        print(f"响应: {response.text}")
        
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def main():
    """主函数"""
    print("开始测试主机聚合更新接口...")
    
    # 测试原有的名称更新接口
    test_update_cluster_name_only()
    
    # 测试新的完整更新接口
    test_update_cluster_full()
    
    # 测试部分更新
    test_update_cluster_partial()
    
    # 测试无效请求
    test_invalid_requests()
    
    print("\n测试完成!")

if __name__ == "__main__":
    main()
