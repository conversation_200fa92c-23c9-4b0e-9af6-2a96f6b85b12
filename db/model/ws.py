# -*- coding: utf-8 -*-

from sqlalchemy import Column, String, Integer, create_engine, ForeignKey, Unicode
from sqlalchemy.orm import sessionmaker, relationship
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime
import datetime
Base = declarative_base()


class WsClients(Base):
    __tablename__ = 'wsclients'
    id = Column(Integer, primary_key=True, autoincrement=True)
    session_id = Column(Unicode(64), nullable=False)
    username = Column(Unicode(32), nullable=False)
    protocol = Column(Unicode(32), nullable=False)
    host = Column(Unicode(32), nullable=False)
    path = Column(Unicode(64), nullable=False)
    callback_path = Column(Unicode(64), nullable=False)

class WsSession(Base):
    __tablename__ = 'wssession'
    id = Column(Integer, primary_key=True, autoincrement=True)
    session_id = Column(Unicode(64), nullable=False)
    username = Column(Unicode(32), nullable=False)
    vm_id = Column(Unicode(64), nullable=True)
    vm_name = Column(Unicode(64), nullable=True)
    vm_ip = Column(Unicode(32), nullable=True)
    client_ip = Column(Unicode(32), nullable=True)
    desk_id = Column(Integer, nullable=True)
    create_at = Column(DateTime(timezone=True), default=datetime.datetime.now, nullable=False)
