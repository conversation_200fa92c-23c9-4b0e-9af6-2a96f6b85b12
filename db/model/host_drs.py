from sqlalchemy import Column, String, Integer
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy import  Column, Integer, String

Base = declarative_base()


class ClusterDrs(Base):
    __tablename__ = 'host_drs'
    id          = Column(Integer, primary_key=True, autoincrement=True)
    auto        = Column(String(16))
    enabled     = Column(String(16))
    interval    = Column(String(32))
    strategy    = Column(String(64))
    cpu_enabled = Column(String(32))
    mem_enabled = Column(String(32))
