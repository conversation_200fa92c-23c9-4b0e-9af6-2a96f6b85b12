# -*- coding: utf-8 -*-

from sqlalchemy import Column, String, Integer, create_engine, ForeignKey, Unicode
from sqlalchemy.orm import sessionmaker, relationship
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime
import datetime
Base = declarative_base()

class migrate_cmd(Base):
    __tablename__ = 'migrate_cmd'
    id = Column(Integer, primary_key=True, autoincrement=True)
    domain = Column('domain', Unicode(64))
    from_ip = Column('from_ip', Unicode(32))
    to_ip = Column('to_ip', Unicode(32))
    status = Column('status', Unicode(32))
    create_at = Column(DateTime(timezone=True), default=datetime.datetime.now)
    updated_at = Column(DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now) 
    expired_at = Column(DateTime(timezone=True))