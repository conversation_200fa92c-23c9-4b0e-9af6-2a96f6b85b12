# -*- coding: utf-8 -*-

from sqlalchemy import Column, String, Integer, create_engine, ForeignKey, Unicode
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime
import datetime
Base = declarative_base()




class Event(Base):
    __tablename__ = 'events'
    id = Column(Integer, primary_key=True, autoincrement=True)
    username = Column(Unicode(32), nullable=False)
    name = Column(Unicode(16))
    level = Column(Unicode(16))
    op = Column(Unicode(16))
    desc = Column(Unicode(128))
    create_at = Column(DateTime(timezone=True), default=datetime.datetime.now)

class Config(Base):
    __tablename__ = 'global_config'
    id = Column(Integer, primary_key=True, autoincrement=True)
    key = Column(Unicode(64), nullable=False)
    value = Column(Unicode(16))
    config_type = Column(Unicode(16))
    create_at = Column(DateTime(timezone=True), default=datetime.datetime.now)
    create_at = Column(DateTime(timezone=True), default=datetime.datetime.now)
