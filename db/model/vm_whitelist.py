# -*- coding: utf-8 -*-

from sqlalchemy import Column, String, Integer, create_engine, ForeignKey, Unicode
from sqlalchemy.orm import sessionmaker, relationship
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime
import datetime
Base = declarative_base()


class VmWhitelist(Base):
    __tablename__ = 'ha_vm_whitelist'
    id = Column(Integer, primary_key=True, autoincrement=True)
    vm_id = Column(String(255))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
