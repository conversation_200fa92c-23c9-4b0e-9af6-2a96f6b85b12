# -*- coding: utf-8 -*-

from sqlalchemy import Column, String, Integer, create_engine, ForeignKey, Unicode
from sqlalchemy.orm import sessionmaker, relationship , backref
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime

from db.model.user import User

import datetime
Base = declarative_base()


group_vm_relationship = Table(
    "group_vm_relationship",
    Base.metadata,
    Column("vmgroup_id", Integer, ForeignKey("vmgroup.id"), nullable=False, primary_key=True),
    Column("vms_id", Integer, ForeignKey("vms.id"), nullable=False, primary_key=True)
)



class VmGroup(Base):
    __tablename__ = 'vmgroup'
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(Unicode(32),  nullable=False)
    pid = Column(Integer)
    user_id = Column(Integer)
    pp = Column(Unicode(128),  nullable=False)
    vms = relationship("Vms", secondary=group_vm_relationship, back_populates='vmgroup')

class Vms(Base):
    __tablename__ = 'vms'
    id = Column(Integer, primary_key=True, autoincrement=True)
    vmid = Column(Unicode(64))
    user_id = Column(Integer)
    #user_id = Column(Integer, ForeignKey('users.id', ondelete='CASCADE'))
    vmgroup = relationship("VmGroup", secondary=group_vm_relationship, back_populates='vms')
    vmgroup_id = Column(Integer)
    #user = relationship("User", single_parent=True, cascade='all, delete-orphan', passive_deletes=True, back_populates="vms")

class Volumes(Base):
    __tablename__ = 'volumes'
    id = Column(Integer, primary_key=True, autoincrement=True)
    volume_id = Column(Unicode(64))
    vm_id = Column(Unicode(64))
    user_id = Column(Integer)    

class Snapshot(Base):
    __tablename__ = 'snapshots'
    id = Column(Integer, primary_key=True, autoincrement=True)
    snapshot_id = Column(Unicode(64))
    user_id = Column(Integer)   
