# -*- coding: utf-8 -*-

from sqlalchemy import Column, String, Integer, create_engine, ForeignKey, Unicode
from sqlalchemy.orm import sessionmaker, relationship
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime
import datetime
Base = declarative_base()


device_devicegroup_r = Table(
    "device_devicegroup_r",
    Base.metadata,
    Column("device_id", Integer, ForeignKey("device.id"), nullable=False, primary_key=True),
    Column("devicegroup_id", Integer, ForeignKey("device_group.id"), nullable=False, primary_key=True)
)



class DeviceGroup(Base):
    __tablename__ = 'device_group'
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(Unicode(32),  nullable=False)
    pid = Column(Integer)
    user_id = Column(Integer)
    pp = Column(Unicode(128),  nullable=False)
    device = relationship("Device", secondary=device_devicegroup_r, back_populates='device_group')
    
class Device(Base):
    __tablename__ = 'device'
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(Unicode(32),  nullable=False)
    device_info = Column(Unicode(255), nullable=False)
    ip_addr = Column(Unicode(64), nullable=False)
    serial_number = Column(Unicode(64), nullable=False)
    client_version = Column(Unicode(64), nullable=False)
    mac_addr = Column(Unicode(32), nullable=False)
    status = Column(Unicode(32), nullable=False)
    create_at = Column(DateTime(timezone=True), default=datetime.datetime.now)
    updated_at = Column(DateTime(timezone=True), default=datetime.datetime.now)
    connect_at = Column(DateTime(timezone=True))
    disconnect_at = Column(DateTime(timezone=True))
    expired_at = Column(DateTime(timezone=True))
    device_group = relationship("DeviceGroup", secondary=device_devicegroup_r, back_populates='device')

class DevicePloy(Base):
    __tablename__ = 'device_ploy'
    id = Column(Integer, primary_key=True, autoincrement=True)
    device_id = Column(Integer)
    name = Column(Unicode(64))
    key = Column(Unicode(32))
    value = Column(Unicode(64))
    button_type = Column(Unicode(32))
    name = Column(Unicode(64))
    name = Column(Unicode(64))
    create_at = Column(DateTime(timezone=True), default=datetime.datetime.now)


