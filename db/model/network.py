# -*- coding: utf-8 -*-

from sqlalchemy import Column, String, Integer, create_engine, ForeignKey, Unicode
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime
import datetime
Base = declarative_base()




class Network(Base):
    __tablename__ = 'network'
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(Unicode(32),  nullable=False)
    cidr = Column(Unicode(16))
    vlanid = Column(Unicode(16))
    bridge = Column(Unicode(32))
    bonding = Column(Unicode(32))
    devinfo = Column(Unicode(16), nullable=False, default="on")
    speed = Column(Unicode(16), nullable=False, default="on")
    type = Column(Unicode(16), nullable=False, default="on")

