# -*- coding: utf-8 -*-

from sqlalchemy import Column, String, Integer, create_engine, ForeignKey, Unicode
from sqlalchemy.orm import sessionmaker, relationship
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime
import datetime
Base = declarative_base()

class ClusterHost(Base):
    __tablename__ = 'cluster_host'

    ip = Column('ip', Unicode(32), primary_key=True)
    cluster_id = Column('cluster_id', Integer, primary_key=True)
    cluster_name = Column('cluster_name', Unicode(32))
    hostname = Column('hostname', Unicode(32))
    cpu_usage_upper = Column('cpu_usage_upper', Unicode(16))
    memory_usage_upper = Column('memory_usage_upper', Unicode(16))
    host_horizon_usage = Column('host_horizon_usage', Unicode(16))
    migrate_type = Column('migrate_type', Unicode(16))
    state = Column('state', Unicode(16))
    state_updated = Column('state_updated', DateTime(timezone=True))
    status = Column('status', Unicode(16))
    weihu_state = Column('weihu_state', Unicode(16))
    weihu_state_updated = Column('weihu_state_updated', DateTime(timezone=True))
    enabled_auto = Column('enabled_auto', Unicode(16))

    def __repr__(self):
        return "<ClusterHost(cluster_id='%s', cluster_name='%s', ip='%s', hostname='%s')>" % (self.cluster_id, self.cluster_name, self.ip, self.host_name)
