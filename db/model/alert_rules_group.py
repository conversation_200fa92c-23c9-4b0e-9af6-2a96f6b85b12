# -*- coding: utf-8 -*-

from sqlalchemy import Column, String, Integer, create_engine, ForeignKey, Unicode
from sqlalchemy.orm import sessionmaker, relationship
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime
import datetime
Base = declarative_base()

class AlertRuleGroups(Base):
    __tablename__ = 'alert_rule_groups'

    id = Column(Integer, primary_key=True, autoincrement=True)
    rules = Column(String(255), nullable=False)
    description = Column(String(255))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    reserved1 = Column(String(255))
    reserved2 = Column(String(255))

    def __repr__(self):
        return f"<AlertRule(id={self.id}, rules='{self.rules}')>"
