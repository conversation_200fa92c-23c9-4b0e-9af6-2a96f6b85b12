# -*- coding: utf-8 -*-

from sqlalchemy import Column, String, Integer, create_engine, ForeignKey, Unicode
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime
import datetime
Base = declarative_base()




class Cloudy(Base):
    __tablename__ = 'cloudy'
    id = Column(Integer, primary_key=True, autoincrement=True)
    username = Column(Unicode(64), nullable=False)
    password = Column(Unicode(64), nullable=False)
    ip = Column(Unicode(32), nullable=False)
    create_at = Column(DateTime(timezone=True), default=datetime.datetime.now)
    
class CloudyMaster(Base):
    __tablename__ = 'cloudy_master'
    id = Column(Integer, primary_key=True, autoincrement=True)
    cloudy_id = Column(Unicode(64), nullable=False)
    cloudy_domain = Column(Unicode(64), nullable=False)
    cloudy_ip = Column(Unicode(64), nullable=False)
    cloudy_port = Column(Unicode(64), nullable=False)
    create_at = Column(DateTime(timezone=True), default=datetime.datetime.now)


    
    