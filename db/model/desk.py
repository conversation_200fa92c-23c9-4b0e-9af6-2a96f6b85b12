# -*- coding: utf-8 -*-

from sqlalchemy import Column, String, Integer, create_engine, ForeignKey, Unicode
from sqlalchemy.orm import sessionmaker, relationship
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime
import datetime
Base = declarative_base()



desk_grantpolicy_r = Table(
    "desk_grantpolicy_r",
    Base.metadata,
    Column("desk_id", Integer, ForeignKey("desk.id"), nullable=False, primary_key=True),
    Column("grantpolicy_id", Integer, Foreign<PERSON>ey("deskpool_grant_policy.id"), nullable=False, primary_key=True)
)

deskpool_whitelist_r = Table(
    "deskpool_whitelist",
    Base.metadata,
    Column("deskpool_id", Integer, ForeignKey("deskpool.id"), nullable=False, primary_key=True),
    Column("whitelist_id", <PERSON>te<PERSON>, <PERSON><PERSON><PERSON>("whitelist.id"), nullable=False, primary_key=True)
)

deskpool_deskpoolgroup_r = Table(
    "deskpool_deskpoolgroup_r",
    Base.metadata,
    Column("deskpool_id", Integer, ForeignKey("deskpool.id"), nullable=False, primary_key=True),
    Column("deskpool_group_id", Integer, ForeignKey("deskpool_group.id"), nullable=False, primary_key=True)
)

deskpool_desktemplate_r = Table(
    "deskpool_desktemplate_r", Base.metadata,
    Column("deskpool_id", Integer,  ForeignKey("deskpool.id"), nullable=False, primary_key=True),
    Column("desktemplate_id",Integer, ForeignKey("desk_template.id"), nullable=False, primary_key=True),
)


class DeskPoolGroup(Base):
    __tablename__ = 'deskpool_group'
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(Unicode(32),  nullable=False)
    pid = Column(Integer)
    user_id = Column(Integer)
    pp = Column(Unicode(128),  nullable=False)
    group_type = Column(Unicode(32),  nullable=False)
    deskpool = relationship("DeskPool", secondary=deskpool_deskpoolgroup_r, back_populates='deskpool_group')

class DeskTemplate(Base):
    __tablename__ = 'desk_template'
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(Unicode(64), nullable=False)
    glance_id = Column(Unicode(64), nullable=False)
    flavor_id = Column(Unicode(64), nullable=False)
    network_id = Column(Unicode(64), nullable=False)
    subnet_id = Column(Unicode(64), nullable=False)
    availability_zone = Column(Unicode(64), nullable=False, default="desk")
    vcpu = Column(Integer)
    vmem_gb = Column(Integer)
    vdisk_gb = Column(Integer)
    status = Column(Unicode(32))
    ex_volume_size = Column(Integer)
    os_type = Column(Unicode(32))
    deskpool = relationship("DeskPool", secondary=deskpool_desktemplate_r, back_populates='template')


"""
granttype: 分配方式: 空池模式: manual  预授权模式: auto  手动模式: manual  注：这个字段和分配表中的config_type冗余了
deskpoolclass: 桌面池模式  专用模式 no_restore   还原模式 restore
sharetype: 共享分配 yes no

"""

class DeskPool(Base):
    __tablename__ = 'deskpool'
    id = Column(Integer, primary_key=True, autoincrement=True)
    desksource = Column(Unicode(32), nullable=False)
    count = Column(Integer, nullable=False)
    name = Column(Unicode(16), nullable=False)
    granttype = Column(Unicode(16), nullable=False, default="manual")
    deskpoolclass = Column(Unicode(16), nullable=False, default="no_restore")
    sharetype = Column(Unicode(16), nullable=False, default="no")
    create_at = Column(DateTime(timezone=True), default=datetime.datetime.now)
    status = Column(Unicode(32), nullable=False, default="enable")
    template = relationship("DeskTemplate", secondary=deskpool_desktemplate_r, back_populates="deskpool")
    desks = relationship("Desk", back_populates='deskpool')
    #whitelist = relationship("Whitelist", secondary=deskpool_whitelist_r)
    acgroup = relationship("AcGroup", uselist=False, back_populates="deskpool")
    deskpool_group = relationship("DeskPoolGroup", secondary=deskpool_deskpoolgroup_r, back_populates='deskpool')
    
    def inject_from_dict(self, input_dict):
        for key, value in input_dict.items():
            if hasattr(self, key):
                setattr(self, key, value)

class Whitelist(Base):
    __tablename__ = 'whitelist'
    id = Column(Integer, primary_key=True, autoincrement=True)
    device_id = Column(Unicode(64), nullable=False)

 
    
class AcGroup(Base):
    __tablename__ = 'acgroup'
    id = Column(Integer, primary_key=True, autoincrement=True)
    usb = Column(Unicode(16), nullable=False, default="on")
    clipboard = Column(Unicode(16), nullable=False, default="on")
    deskpool_id = Column(Integer, ForeignKey('deskpool.id'))
    deskpool = relationship("DeskPool", back_populates="acgroup")   


class UserSession(Base):
    __tablename__ = 'usersession'
    id = Column(Integer, primary_key=True, autoincrement=True)
    session_id = Column(Unicode(64))
    username = Column(Unicode(32))
    create_at = Column(DateTime(timezone=True), default=datetime.datetime.now)
    
device_devicegroup_r = Table(
    "deskuser_deskusergroup_r",
    Base.metadata,
    Column("deskuser_id", Integer, ForeignKey("deskuser.id"), nullable=False, primary_key=True),
    Column("deskusergroup_id", Integer, ForeignKey("desk_user_group.id"), nullable=False, primary_key=True)
)



class DeskUserGroup(Base):
    __tablename__ = 'desk_user_group'
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(Unicode(32),  nullable=False)
    pid = Column(Integer)
    user_id = Column(Integer)
    pp = Column(Unicode(128),  nullable=False)
    deskuser = relationship("DeskUser", secondary=device_devicegroup_r, back_populates='deskuser_group')

desk_manual_allocation_deskuser_r = Table(
    "desk_manual_allocation_deskuser_r",
    Base.metadata,
    Column("deskuser_id", Integer, ForeignKey("deskuser.id"), nullable=False, primary_key=True),
    Column("desk_id", Integer, ForeignKey("desk.id"), nullable=False, primary_key=True)
)
    
desk_auto_allocation_deskuser_r = Table(
    "desk_auto_allocation_deskuser_r",
    Base.metadata,
    Column("deskuser_id", Integer, ForeignKey("deskuser.id"), nullable=False, primary_key=True),
    Column("desk_id", Integer, ForeignKey("desk.id"), nullable=False, primary_key=True)
)


desk_allocation_deskuser_r = Table(
    "desk_allocation_deskuser_r",
    Base.metadata,
    Column("desk_allocation_id", Integer, ForeignKey("desk_allocation.id"), nullable=False, primary_key=True),
    Column("deskuser_id", Integer, ForeignKey("deskuser.id"), nullable=False, primary_key=True)
)
    
    
    
class DeskUser(Base):
    __tablename__ = 'deskuser'
    id = Column(Integer, primary_key=True, autoincrement=True)
    username = Column(Unicode(32), unique=True, nullable=False)
    name = Column(Unicode(32))
    password = Column(Unicode(64))
    domain = Column(Unicode(16), nullable=False, default="local")
    organize = Column(Unicode(64))
    email = Column(Unicode(64))
    phone = Column(Unicode(64))
    session_id = Column(Unicode(64))
    user_source_id = Column(Integer)
    status = Column(Unicode(16), nullable=False, default="on")
    created_at = Column(DateTime(timezone=True), default=datetime.datetime.now)
    deskuser_group = relationship("DeskUserGroup", secondary=device_devicegroup_r, back_populates='deskuser')
    manual_desks = relationship("Desk", secondary=desk_manual_allocation_deskuser_r, back_populates='manual_deskusers')
    auto_desks = relationship("Desk", secondary=desk_auto_allocation_deskuser_r, back_populates='auto_deskusers')
    desks_allocations = relationship("DeskAllocation", secondary=desk_allocation_deskuser_r, back_populates='deskusers')
        


class UserSource(Base):
    __tablename__ = 'usersource'
    id = Column(Integer, primary_key=True, autoincrement=True)
    ldap_server = Column(Unicode(64))
    autosync = Column(Unicode(64))
    domain = Column(Unicode(64))
    user = Column(Unicode(64))
    psw = Column(Unicode(64))
    

class DeskPoolGrantPolicy(Base):
    __tablename__ = 'deskpool_grant_policy'
    id = Column(Integer, primary_key=True, autoincrement=True)
    deskpool_id = Column(Integer)
    domain = Column(Unicode(64))
    dnpath = Column(Unicode(64))
    username = Column(Unicode(64))
    name = Column(Unicode(64))
    objecttype = Column(Unicode(64))
    objectguid = Column(Unicode(64))
    #desks = relationship("Desk", secondary=desk_grantpolicy_r)



    
class DeskAllocation(Base):
    __tablename__ = 'desk_allocation'
    id = Column(Integer, primary_key=True, autoincrement=True)
    deskpool_id = Column(Integer)
    name = Column(Unicode(64))
    config_type = Column(Unicode(16))
    create_at = Column(DateTime(timezone=True), default=datetime.datetime.now)
    deskusers = relationship("DeskUser", secondary=desk_allocation_deskuser_r, back_populates='desks_allocations')



class Desk(Base):
    __tablename__ = 'desk'
    id = Column(Integer, primary_key=True, autoincrement=True)
    vm_id = Column(Unicode(64))
    vm_name = Column(Unicode(64))
    domain = Column(Unicode(64))
    objectguid = Column(Unicode(64))
    username = Column(Unicode(64))
    name = Column(Unicode(64))
    in_recycle = Column(Unicode(64), nullable=False, default="false")
    dnpath = Column(Unicode(64))
    deskclass = Column(Unicode(16), nullable=False, default="fixed")
    status = Column(Unicode(16), nullable=False, default="uninitialized")
    enabled = Column(Unicode(16), nullable=False, default="no")
    single_grant = Column(Unicode(16), nullable=False, default="no")
    create_at = Column(DateTime(timezone=True), default=datetime.datetime.now)
    #grantpolicys = relationship("DeskPoolGrantPolicy", secondary=desk_grantpolicy_r)
    deskpool_id = Column(Integer, ForeignKey('deskpool.id'))
    deskpool = relationship("DeskPool", single_parent=True,  back_populates="desks")
    #deskpool_id = Column(Integer, ForeignKey('deskpool.id', ondelete='CASCADE'))
    #deskpool = relationship("DeskPool", single_parent=True, cascade='all, delete-orphan', passive_deletes=True, back_populates="desks")
    manual_deskusers = relationship("DeskUser", secondary=desk_manual_allocation_deskuser_r, back_populates='manual_desks')
    auto_deskusers = relationship("DeskUser", secondary=desk_auto_allocation_deskuser_r, back_populates='auto_desks')

class DeskPloy(Base):
    __tablename__ = 'desk_ploy'
    id = Column(Integer, primary_key=True, autoincrement=True)
    desk_id = Column(Integer)
    name = Column(Unicode(64))
    key = Column(Unicode(32))
    value = Column(Unicode(64))
    button_type = Column(Unicode(32))
    name = Column(Unicode(64))
    name = Column(Unicode(64))
    create_at = Column(DateTime(timezone=True), default=datetime.datetime.now)
    
class DeskPoolPloy(Base):
    __tablename__ = 'deskpool_ploy'
    id = Column(Integer, primary_key=True, autoincrement=True)
    deskpool_id = Column(Integer)
    name = Column(Unicode(64))
    key = Column(Unicode(32))
    value = Column(Unicode(64))
    button_type = Column(Unicode(32))
    name = Column(Unicode(64))
    name = Column(Unicode(64))
    create_at = Column(DateTime(timezone=True), default=datetime.datetime.now)
    
class ClickHelp(Base):
    __tablename__ = 'click_help'
    id = Column(Integer, primary_key=True, autoincrement=True)
    desk_id = Column(Integer)
    user_id = Column(Integer)
    device_id = Column(Integer)
    title = Column(Unicode(32))
    desc = Column(Unicode(255))
    status = Column(Unicode(32))
    create_at = Column(DateTime(timezone=True), default=datetime.datetime.now)        
    updated_at = Column(DateTime(timezone=True), default=datetime.datetime.now)        

