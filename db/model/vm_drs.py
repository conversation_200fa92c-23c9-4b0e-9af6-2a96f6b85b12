# -*- coding: utf-8 -*-

from sqlalchemy import Column, String, Integer, create_engine, ForeignKey, Unicode
from sqlalchemy.orm import sessionmaker, relationship
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime
import datetime
Base = declarative_base()


class VmDrs(Base):
    __tablename__ = 'vm_drs'
    id = Column(Integer, primary_key=True, autoincrement=True)
    vm_id = Column(String(64))
    cpu = Column(String(16))
    mem = Column(String(16))
    interval = Column(String(32))
    cpu_enabled = Column(String(32))
    mem_enabled = Column(String(32))
    enabled = Column(String(32))
