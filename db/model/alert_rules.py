# -*- coding: utf-8 -*-

from sqlalchemy import Column, String, Integer, create_engine, ForeignKey, Unicode
from sqlalchemy.orm import sessionmaker, relationship
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime
import datetime
Base = declarative_base()

class AlertRule(Base):
    __tablename__ = 'alert_rules'

    id = Column('id', Integer, primary_key=True, autoincrement=True)
    name = Column('name', String(255), nullable=False)
    expr = Column('expr', String(512), nullable=False)
    expr_code = Column('expr_code', String(32), nullable=False, default='0')
    value = Column('value', Integer, nullable=False)
    unit = Column('unit', String(32))
    for_interval = Column('for_interval', String(20), nullable=False)
    report_interval = Column('report_interval', String(20), nullable=False, default='10m')
    critical_value = Column('critical_value', Integer)
    major_value = Column('major_value', Integer)
    warning_value = Column('warning_value', Integer)
    info_value = Column('info_value', Integer)
    description = Column('description', String(255))
    summary = Column('summary', String(255))
    job = Column('job', String(255))
    alert_type = Column('alert_type', String(64))
    created_at = Column('created_at', DateTime(timezone=True), server_default=func.now())
    updated_at = Column('updated_at', DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    is_default = Column('is_default', Integer)
    status = Column('status', String(50))
    order_id = Column('order_id', Integer, nullable=False, default=0)

    def __repr__(self):
        return f"<AlertRule(id={self.id}, name='{self.name}', expr='{self.expr}', severity='{self.severity}')>"

    def to_dict(self):

        created_at_str = self.created_at.isoformat() if self.created_at else None
        updated_at_str = self.updated_at.isoformat() if self.updated_at else None

        return {
            "id": self.id,
            "name": self.name,
            "expr": self.expr,
            "expr_code": self.expr_code,
            "value": self.value,
            "unit": self.unit,
            "for_interval": self.for_interval,
            "report_interval": self.report_interval,
            "critical_value": self.critical_value,
            "major_value": self.major_value,
            "warning_value": self.warning_value,
            "info_value": self.info_value,
            "description": self.description,
            "summary": self.summary,
            "job": self.job,
            "alert_type": self.alert_type,
            "created_at": created_at_str,
            "updated_at": updated_at_str,
            "is_default": self.is_default,
            "status": self.status,
            "order_id": self.order_id,
        }
