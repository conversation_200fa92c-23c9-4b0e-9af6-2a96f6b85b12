# -*- coding: utf-8 -*-

from sqlalchemy import <PERSON>um<PERSON>, String, Integer, create_engine, ForeignKey, Unicode
from sqlalchemy.orm import sessionmaker, relationship
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime
import datetime
Base = declarative_base()


class ACGlobal(Base):
    __tablename__ = 'ac_global'
    id = Column(Integer, primary_key=True, autoincrement=True)
    k = Column(Unicode(32), nullable=False)
    v = Column(Unicode(64), nullable=False)
    t = Column(Unicode(16), nullable=False)
    name = Column(Unicode(64), nullable=False)

    
class AcUsb(Base):
    __tablename__ = 'acusb'
    id = Column(Integer, primary_key=True, autoincrement=True)
    global_deny = Column(Unicode(32), nullable=False)
    global_allow = Column(Unicode(32), nullable=False)
    byte_two_way_deny = Column(Unicode(32), nullable=False)
    byte_two_way_allow = Column(Unicode(32), nullable=False)
    byte_in_to_out_allow = Column(Unicode(32), nullable=False)
    byte_out_to_in_allow = Column(Unicode(32), nullable=False)
    file_two_way_deny = Column(Unicode(32), nullable=False)
    file_two_way_allow = Column(Unicode(32), nullable=False)
    file_in_to_out_allow = Column(Unicode(32), nullable=False)
    file_out_to_in_allow = Column(Unicode(32), nullable=False)
    
    
class AcUsbWhiteList(Base):
    __tablename__ = 'whitelist'
    id = Column(Integer, primary_key=True, autoincrement=True)
    vm_name = Column(Unicode(64), nullable=False)
    
class License(Base):
    __tablename__ = 'license'
    id = Column(Integer, primary_key=True, autoincrement=True)
    license = Column(Unicode(3000), nullable=False)    
    
    
    
    
    
