# -*- coding: utf-8 -*-

from sqlalchemy import Column, String, Integer, create_engine, ForeignKey, Unicode
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime
import datetime
Base = declarative_base()




class Task(Base):
    __tablename__ = 'tasks'
    id = Column(Integer, primary_key=True, autoincrement=True)
    method = Column(Unicode(32), nullable=False)
    param = Column(Integer)
    complete = Column(Unicode(16), nullable=False, default="no")
    status = Column(Unicode(16), nullable=False, default="ready")
    scheduled = Column(DateTime(timezone=True), default=datetime.datetime.now)
    create_at = Column(DateTime(timezone=True), default=datetime.datetime.now)
    

class InstanceCreateTask(Base):
    __tablename__ = 'instance_create_tasks'
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(Unicode(32), nullable=False)
    imageRef = Column(Unicode(64), nullable=False)
    flavorRef = Column(Unicode(64), nullable=False)
    networkRef = Column(Unicode(64), nullable=False)
    volume_id = Column(Unicode(64), nullable=False)
    user_id = Column(Integer, nullable=False)
    availability_zone = Column(Unicode(64), default="")
    status = Column(Unicode(16), nullable=False, default="ready")
    iso = Column(Unicode(16), nullable=False, default="false")
    driver_volume_id = Column(Unicode(64), nullable=False)
    ipv4 = Column(Unicode(64),default="")
    create_at = Column(DateTime(timezone=True), default=datetime.datetime.now)
    vmid = Column(Unicode(64),default="")
    os_type = Column(Unicode(64),default="")

class ExVolumeCreateTask(Base):
    __tablename__ = 'ex_volume_task'
    id = Column(Integer, primary_key=True, autoincrement=True)
    volume_id = Column(Unicode(64), nullable=False)
    size = Column(Unicode(64), nullable=False)
    volume_name = Column(Unicode(64), nullable=False)
    create_task_id = Column(Unicode(64), nullable=False)
    retry_count = Column(Integer, nullable=False)
    status = Column(Unicode(16), nullable=False, default="ready")
    created_at = Column(DateTime(timezone=True), default=datetime.datetime.now)
    updated_at = Column(DateTime(timezone=True), default=datetime.datetime.now)


class DeskCreateTask(Base):
    __tablename__ = 'desk_create_tasks'
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(Unicode(32), nullable=False)
    imageRef = Column(Unicode(64), nullable=False)
    flavorRef = Column(Unicode(64), nullable=False)
    networkRef = Column(Unicode(64), nullable=False)
    volume_id = Column(Unicode(64), nullable=False)
    user_id = Column(Integer, nullable=False)
    desk_id = Column(Integer, nullable=False)
    availability_zone = Column(Unicode(64), default="")
    status = Column(Unicode(16), nullable=False, default="ready")
    iso = Column(Unicode(16), nullable=False, default="false")
    driver_volume_id = Column(Unicode(64), nullable=False)
    ipv4 = Column(Unicode(64),default="")
    create_at = Column(DateTime(timezone=True), default=datetime.datetime.now)
    os_type = Column(Unicode(64),default="")

class ExVolumeCreateDeskTask(Base):
    __tablename__ = 'ex_desk_volume_task'
    id = Column(Integer, primary_key=True, autoincrement=True)
    volume_id = Column(Unicode(64), nullable=False)
    size = Column(Unicode(64), nullable=False)
    volume_name = Column(Unicode(64), nullable=False)
    create_desk_task_id = Column(Unicode(64), nullable=False)
    retry_count = Column(Integer, nullable=False)
    status = Column(Unicode(16), nullable=False, default="ready")
    created_at = Column(DateTime(timezone=True), default=datetime.datetime.now)
    updated_at = Column(DateTime(timezone=True), default=datetime.datetime.now)


class ImageCreateTask(Base):
    __tablename__ = 'images_create_tasks'
    id = Column(Integer, primary_key=True, autoincrement=True)
    image_id = Column(Unicode(64), nullable=False)
    filename = Column(Unicode(255), nullable=False)
    status = Column(Unicode(16), nullable=False, default="ready")
    create_at = Column(DateTime(timezone=True), default=datetime.datetime.now)
    


class VMwareMigrateTask(Base):
    __tablename__ = 'vmware_migrate_tasks'
    id = Column(Integer, primary_key=True, autoincrement=True)
    vmware_name = Column(Unicode(64), nullable=False)
    vm_name = Column(Unicode(64), nullable=False)
    network_uuid = Column(Unicode(64), nullable=False)
    vm_cpu = Column(Integer, nullable=False)
    vm_ram = Column(Integer, nullable=False)
    vm_disks = Column(Unicode(64), nullable=False)
    cloudy_id = Column(Unicode(64), nullable=False)
    cloudy_host = Column(Unicode(64), nullable=False)
    vm_os = Column(Unicode(64), nullable=False)
    vm_os_version = Column(Unicode(64), nullable=False)
    source_datacenter = Column(Unicode(32), nullable=False)
    source_cluster = Column(Unicode(32), nullable=False)
    source_host = Column(Unicode(32), nullable=False)
    target_zone = Column(Unicode(32), nullable=False)
    error_reason = Column(Unicode(255), nullable=False)
    error_step = Column(Unicode(32), nullable=False)
    status = Column(Unicode(16), nullable=False, default="wait")
    task_status = Column(Unicode(16), nullable=False, default="start")
    create_at = Column(DateTime(timezone=True), default=datetime.datetime.now)    
    complete_at = Column(DateTime(timezone=True))    
    
    
