[db_settings]
# Used to identify which repository this database is versioned under.
# You can use the name of your project.
repository_id=theweb

# The name of the database table used to track the schema version.
# This name shouldn't already be used by your project.
# If this is changed once a database is under version control, you'll need to
# change the table name in each database too.
version_table=migrate_version

# When committing a change script, <PERSON><PERSON><PERSON> will attempt to generate the
# sql for all supported databases; normally, if one of them fails - probably
# because you don't have that database installed - it is ignored and the
# commit continues, perhaps ending successfully.
# Databases in this list MUST compile successfully during a commit, or the
# entire commit will fail. List the databases your application will actually
# be using to ensure your updates to that database work properly.
# This must be a list; example: ['postgres','sqlite']
required_dbs=[]

# When creating new change scripts, <PERSON><PERSON><PERSON> will stamp the new script with
# a version number. By default this is latest_version + 1. You can set this
# to 'true' to tell <PERSON><PERSON><PERSON> to use the UTC timestamp instead.
use_timestamp_numbering=False
