import pymysql
import time
import psycopg2
import sys
sys.path.append(".")
import settings
import traceback
import subprocess

out_time = time.time()

def the_check_with_retry(host, port, user, password, targetdb,  driver, retries=20, delay=30):
    for i in range(retries):
        rtn = 0
        conn = None
        try:
            if driver == "kingbase":
                import kingbase_dialect
                conn = psycopg2.connect(
                    host=host,
                    port=int(port),
                    user=user,
                    password=password
                )
                conn.autocommit = True
            if driver == "mysql":
                conn = pymysql.connect(
                    host=host,
                    port=int(port),
                    user=user,
                    password=password
                )
            

                conn.autocommit = True
            cursor = conn.cursor()

            if driver == "kingbase":
                cursor.execute("SELECT datname FROM pg_database;")
            else:
                cursor.execute("SHOW DATABASES;")

            data = cursor.fetchall()
            for d in data:
                if d[0] == targetdb:
                    rtn = 1
                    break

            if rtn == 0:
                if driver == "kingbase":
                    cursor.execute(f"CREATE DATABASE {targetdb};")
                else:
                    cursor.execute(f"CREATE DATABASE {targetdb};")
            else:
                cursor.execute(f"USE {targetdb};" if driver != "kingbase" else f"SET search_path TO {targetdb};")
                cursor.execute("SHOW TABLES;" if driver != "kingbase" else "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public';")
                data = cursor.fetchall()
                for t in data:
                    if t[0] == "migrate_version":
                        rtn = 2
                        break
                    else:
                        rtn = 3

            cursor.close()
            conn.close()
            
            if rtn == 2:
                run_manage_py_commands_upgrade()
            else:
                run_manage_py_commands()
                    
            
            return rtn
        except Exception as e:
            traceback.print_exc()
            time.sleep(delay)
            



def dqlite_check_with_retry(host, port, user, password, targetdb,  driver, retries=20, delay=30):
    for i in range(retries):
        rtn = 0
        try:
                
            print(host)
            print(port)
            conn = dbapi2.connect(
                host=host,
                port=int(port)
            )
            cursor = conn.cursor()

            with conn.cursor() as cursor:

                # 查询 sqlite_master 表，判断表是否存在
                cursor.execute("""
                    SELECT name FROM sqlite_master WHERE type='table' AND name=?;
                """, ("migrate_version",))
                
                # 获取查询结果
                table_exists = cursor.fetchone()
                
                if table_exists:
                    rtn = 2
                else:
                    rtn = 3

                
                if rtn == 2:
                    run_manage_py_commands_upgrade()
                else:
                    run_manage_py_commands()
            
    
                return rtn
        
        except Exception as e:
            traceback.print_exc()
            time.sleep(delay)

    


def run_manage_py_commands_upgrade():
    try:

        # 执行 python3 db/manage.py upgrade
        subprocess.run(["python3", "db/manage.py", "upgrade"], check=True)
        print("upgrade executed successfully.")
        
    except subprocess.CalledProcessError as e:
        print(f"Error occurred while executing commands: {e}")


def run_manage_py_commands():
    try:
        # 执行 python3 db/manage.py version_control
        subprocess.run(["python3", "db/manage.py", "version_control"], check=True)
        print("version_control executed successfully.")

        # 执行 python3 db/manage.py upgrade
        subprocess.run(["python3", "db/manage.py", "upgrade"], check=True)
        print("upgrade executed successfully.")
        
    except subprocess.CalledProcessError as e:
        print(f"Error occurred while executing commands: {e}")




print("---------------")
print(settings.DRIVER)
print("---------------")
if settings.DRIVER == "mysql":
    mysql_r = the_check_with_retry(
            settings.DB_HOSTNAME,
            settings.DB_PORT,
            settings.DB_USERNAME,
            settings.DB_PASSWORD,
            settings.DB_DATABASE,
            settings.DRIVER,
            retries=20, 
            delay=30
    )
    print(mysql_r)
    
if settings.DRIVER == "kingbase":
    import kingbase_dialect
    kingbase_r = the_check_with_retry(
            settings.DB_HOSTNAME,
            settings.DB_PORT,
            settings.DB_USERNAME,
            settings.DB_PASSWORD,
            settings.DB_DATABASE,
            settings.DRIVER,
            retries=20, 
            delay=30
    )

    print(kingbase_r)
if settings.DRIVER == "dqlite":
    import pydqlite.dbapi2 as dbapi2
    # import dqlite_dialect  # dqlite 的 SQLAlchemy 方言
    
    dqlite_r = dqlite_check_with_retry(
            settings.DB_HOSTNAME,
            settings.DB_PORT,
            settings.DB_USERNAME,
            settings.DB_PASSWORD,
            settings.DB_DATABASE,
            settings.DRIVER,
            retries=20, 
            delay=30
    ) 
    print(dqlite_r)


