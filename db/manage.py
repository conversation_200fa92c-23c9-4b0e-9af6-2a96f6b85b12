#!/usr/bin/env python
from migrate.versioning.shell import main

import sys
sys.path.append(".")


import settings
print(settings.DB_USERNAME)

if settings.DRIVER == "kingbase":
    import kingbase_dialect
if settings.DRIVER == "dqlite":
    import dqlite_dialect  # dqlite 的 SQLAlchemy 方言

if __name__ == '__main__':
    if settings.DRIVER == "mysql":
        main(repository='db', url='mysql+pymysql://%s:%s@%s:%s/%s?charset=utf8' % (settings.DB_USERNAME,
                                                                                settings.DB_PASSWORD,
                                                                                settings.DB_HOSTNAME,
                                                                                settings.DB_PORT,
                                                                                settings.DB_DATABASE), debug='False')
    if settings.DRIVER == "kingbase":
        main(repository='db', url='kingbase+psycopg2://%s:%s@%s:%s/%s' % (settings.DB_USERNAME,
                                                                        settings.DB_PASSWORD,
                                                                        settings.DB_HOSTNAME,
                                                                        settings.DB_PORT,
                                                                        settings.DB_DATABASE), debug='False')

    if settings.DRIVER == "dqlite":
        main(repository='db', url='dqlite+pydqlite://***************:9001/', echo=True)