from sqlalchemy import *
from migrate import *

from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime

meta = MetaData()

mylicense = Table(
    'license', meta,
    Column('id', Integer, primary_key=True),
    Column('license', String(3000)),
    
)


def upgrade(migrate_engine):
    meta.bind = migrate_engine
    mylicense.create()


def downgrade(migrate_engine):
    meta.bind = migrate_engine
    mylicense.drop()

