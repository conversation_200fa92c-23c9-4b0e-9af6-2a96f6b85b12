from sqlalchemy import Table, Column, Integer, String, MetaData, create_engine
from sqlalchemy import DateTime

meta = MetaData()


vm_drs = Table(
    'vm_drs', meta,
    Column('id', Integer, primary_key=True, autoincrement=True),
    <PERSON>umn('vm_id', String(64)),
    <PERSON>umn('cpu', String(16)),
    <PERSON>umn('mem', String(16)),
    <PERSON>umn('interval', String(32)),
    <PERSON>umn('cpu_enabled', String(32)),
    Column('mem_enabled', String(32)),
    Column('enabled', String(32))
)


host_drs = Table(
    'host_drs', meta,
    Column('id', Integer, primary_key=True, autoincrement=True),
    Column('cpu_enabled', String(32)),
    Column('mem_enabled', String(32)),
    Column('interval', String(32)),
    Column('auto', String(32)),
    <PERSON>umn('enabled', String(32)),
    Column('strategy', String(32)), # 策略 1: 偏保守 主机负载 > 70% 差值 30%  2: 默认  主机负载 > 60% 差值 20% 3: 偏激进  主机负载 > 50% 差值 10%
)


def upgrade(migrate_engine):
    # 绑定引擎到元数据
    meta.bind = migrate_engine
    vm_drs.create(migrate_engine)
    host_drs.create(migrate_engine)

def downgrade(migrate_engine):
    # 绑定引擎到元数据
    meta.bind = migrate_engine
    vm_drs.drop(migrate_engine)
    host_drs.drop(migrate_engine)
