from migrate import *
from sqlalchemy import Table, MetaData, String, Column

from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime

meta = MetaData()

def upgrade(migrate_engine):
    meta = MetaData(bind=migrate_engine)
    desk_template = Table('desk_template', meta, autoload=True)
    glance_name = Column('glance_name', String(64), server_default="")
    glance_name.create(desk_template)
    network_name = Column('network_name', String(64), server_default="")
    network_name.create(desk_template)

def downgrade(migrate_engine):
    meta = MetaData(bind=migrate_engine)
    desk_template = Table('desk_template', meta, autoload=True)
    desk_template.c.glance_name.drop()
    desk_template.c.network_name.drop()