from sqlalchemy import *
from migrate import *

from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime

meta = MetaData()

images_task = Table(
    'images_create_tasks', meta,
    Column('id', Integer, primary_key=True),
    <PERSON>umn('image_id', String(64)),
    <PERSON>umn('filename', String(255)),
    <PERSON>umn('status', String(16)),
    <PERSON>umn('create_at', DateTime()) 
)




def upgrade(migrate_engine):
    meta.bind = migrate_engine
    images_task.create()


def downgrade(migrate_engine):
    meta.bind = migrate_engine
    images_task.drop()

