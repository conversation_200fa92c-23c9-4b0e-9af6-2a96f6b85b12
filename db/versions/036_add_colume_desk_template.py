from sqlalchemy import *
from migrate import *

from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime

meta = MetaData()



def upgrade(migrate_engine):
    meta.bind = migrate_engine
    desk_template = Table('desk_template', meta, autoload=True)
    os_type = Column('os_type', String(32), server_default="")
    os_type.create(desk_template)
    ex_volume_size = Column('ex_volume_size', Integer, server_default='0')
    ex_volume_size.create(desk_template)
    
    
def downgrade(migrate_engine):
    meta.bind = migrate_engine
    desk_template = Table('desk_template', meta, autoload=True)
    desk_template.c.os_type.drop()
    desk_template.c.ex_volume_size.drop()
    

