from sqlalchemy import *
from migrate import *

from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime

meta = MetaData()



remote_help = Table(
    'remote_help', meta,
    Column('id', Integer, primary_key=True),
    <PERSON>umn('device_id', Integer),
    <PERSON>umn('device_name', String(64)),
    <PERSON>umn('title', String(128)),
    <PERSON>umn('desc', String(255)),
    <PERSON>umn('ip', String(32)),
    <PERSON><PERSON><PERSON>('device_username', String(32)),
    <PERSON><PERSON><PERSON>('device_password', String(32)),
    <PERSON>umn('status', String(32)),
    <PERSON>um<PERSON>('feedback', String(255)),
    <PERSON>umn('created_at', DateTime()),
    <PERSON>umn('updated_at', DateTime()),
)

def upgrade(migrate_engine):
    meta.bind = migrate_engine
    remote_help.create()

    
    
def downgrade(migrate_engine):
    meta.bind = migrate_engine
    remote_help.drop()

    

