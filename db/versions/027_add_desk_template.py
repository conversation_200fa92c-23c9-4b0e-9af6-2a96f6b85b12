from sqlalchemy import *
from migrate import *

from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime

meta = MetaData()

desktemplate = Table(
    'desk_template', meta,
    Column('id', Integer, primary_key=True),
    Column('glance_id', String(64)),
    Column('flavor_id', String(64)),
    Column('network_id', String(64)),
    Column('availability_zone', String(32)),
    Column('name', String(64)),
    Column('vcpu', Integer),
    Column('vmem_gb', Integer),
    Column('vdisk_gb', Integer),
    Column('status', String(64)),
    Column('create_at', DateTime()),
    Column('updated_at', DateTime()),
)


desk_desktemplate_r = Table(
    "deskpool_desktemplate_r", meta,
    Column("deskpool_id", Integer),
    Column("desktemplate_id",Integer),
    PrimaryKeyConstraint("deskpool_id", "desktemplate_id")
)



global_config = Table(
    'global_config', meta,
    Column('id', Integer, primary_key=True),
    Column('key', String(64)),
    Column('value', String(255)),
    Column('config_type', String(64)),
    Column('create_at', DateTime()),
    Column('updated_at', DateTime()),
)




desk_manual_allocation_deskuser_r = Table(
    'desk_manual_allocation_deskuser_r', meta,
    Column('desk_id', Integer),
    Column('deskuser_id', Integer),
    PrimaryKeyConstraint("desk_id", "deskuser_id")
)

desk_auto_allocation_deskuser_r = Table(
    'desk_auto_allocation_deskuser_r', meta,
    Column('desk_id', Integer),
    Column('deskuser_id', Integer),
    PrimaryKeyConstraint("desk_id", "deskuser_id")
)

desk_allocation_deskuser_r = Table(
    'desk_allocation_deskuser_r', meta,
    Column('desk_allocation_id', Integer),
    Column('deskuser_id', Integer),
    PrimaryKeyConstraint("desk_allocation_id", "deskuser_id")
)

"""
desk_allocation: 说明一个桌面池的分配方式 和桌面池是一对一关系
#config_type 分为预授权: auto 和手工分配　manual  默认: 手工分配
预授权: 
    对应多个预授权用户 中间表为 desk_auto_allocation_deskuser_r
手工分配:
    直接选择用户对应的桌面 中间表为 desk_manual_allocation_deskuser_r  

"""
desk_allocation = Table(
    'desk_allocation', meta,
    Column('id', Integer, primary_key=True),
    Column('deskpool_id', Integer),
    Column('name', String(64)),
    Column('config_type', String(64)),
    Column('create_at', DateTime()),
    Column('updated_at', DateTime()),
)



desk_ins = Table(
    'desk_create_tasks', meta,
    Column('id', Integer, primary_key=True),
    Column('name', String(32)),
    Column('imageRef', String(64)),
    Column('flavorRef', String(64)),
    Column('networkRef', String(64)),
    Column('volume_id', String(64)),
    Column('iso', String(16), server_default="false"),
    Column('availability_zone', String(64), server_default=""),
    Column('driver_volume_id', String(64), server_default=""),
    Column('ipv4', String(64), server_default=""),
    Column('user_id', Integer),
    Column('desk_id', Integer),
    Column('status', String(16)),
    Column('create_at', DateTime())
)


user_usb_blacklist = Table(
    'user_usb_blacklist', meta,
    Column('id', Integer, primary_key=True),
    Column('user_id', Integer),
    Column('pid', String(32)),
    Column('vid', String(32)),
    Column('name', String(64))
)

user_usb_whitelist = Table(
    'user_usb_whitelist', meta,
    Column('id', Integer, primary_key=True),
    Column('user_id', Integer),
    Column('pid', String(32)),
    Column('vid', String(32)),
    Column('name', String(64))
)

def upgrade(migrate_engine):
    meta.bind = migrate_engine
    desktemplate.create()
    desk_desktemplate_r.create()
    desk_allocation.create()
    desk_allocation_deskuser_r.create()
    desk_auto_allocation_deskuser_r.create()
    desk_manual_allocation_deskuser_r.create()
    global_config.create()
    user_usb_whitelist.create()
    user_usb_blacklist.create()
    desk_ins.create()
    desk = Table('desk', meta, autoload=True)
    in_recycle = Column('in_recycle', String(16), server_default="false")
    in_recycle.create(desk)

    
    
    
def downgrade(migrate_engine):
    meta.bind = migrate_engine
    desktemplate.drop()
    desk_desktemplate_r.drop()
    desk_allocation.drop()
    desk_allocation_deskuser_r.drop()
    desk_auto_allocation_deskuser_r.drop()
    desk_manual_allocation_deskuser_r.drop()
    global_config.drop()
    user_usb_whitelist.drop()
    user_usb_blacklist.drop()
    desk_ins.drop()
    desk = Table('desk', meta, autoload=True)
    desk.c.in_recycle.drop()

