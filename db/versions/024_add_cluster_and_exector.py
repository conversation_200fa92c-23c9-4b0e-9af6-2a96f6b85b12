from sqlalchemy import *
from migrate import *

from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime

meta = MetaData()

clusterhost = Table(
    'cluster_host', meta,
    Column('ip', String(32), primary_key=True),
    Column('cluster_id', Integer, primary_key=True),
    <PERSON>um<PERSON>('cluster_name', String(32)),
    <PERSON><PERSON><PERSON>('hostname', String(64)),
    <PERSON>umn('cpu_usage_upper', String(32)),
    <PERSON>umn('memory_usage_upper', String(32)),
    <PERSON>umn('host_horizon_usage', String(32)),
    <PERSON>umn('migrate_type', String(64)),
)

migratecmd = Table(
    'migrate_cmd', meta,
    Column('id', Integer, primary_key=True),
    Column('domain', String(64)),
    <PERSON>umn('from_ip', String(32)),
    <PERSON>umn('to_ip', String(32)),
    <PERSON>umn('status', String(32)),
    <PERSON>umn('create_at', DateTime()),
    Column('updated_at', DateTime()),
    Column('expired_at', DateTime()), 
)


def upgrade(migrate_engine):
    meta.bind = migrate_engine
    clusterhost.create()
    migratecmd.create()

def downgrade(migrate_engine):
    meta.bind = migrate_engine
    clusterhost.drop()
    migratecmd.drop()

