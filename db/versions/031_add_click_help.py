from sqlalchemy import *
from migrate import *

from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime

meta = MetaData()


click_help = Table(
    'click_help', meta,
    Column('id', Integer, primary_key=True),
    <PERSON>umn('desk_id', Integer),
    <PERSON>umn('user_id', Integer),
    <PERSON>umn('device_id', Integer),
    <PERSON>umn('title', String(32)),
    Column('desc', String(255)),
    <PERSON>umn('status', String(32)),
    <PERSON>umn('create_at', DateTime()),
    Column('updated_at', DateTime()),
)


def upgrade(migrate_engine):
    meta.bind = migrate_engine
    click_help.create()

    
    
def downgrade(migrate_engine):
    meta.bind = migrate_engine
    click_help.drop()

    

