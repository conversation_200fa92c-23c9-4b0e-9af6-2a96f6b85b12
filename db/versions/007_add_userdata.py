from sqlalchemy import Table, Column, Integer, String, MetaData, create_engine
from sqlalchemy import DateTime

meta = MetaData()

users = Table(
    'users', meta,
    Column('id', Integer, primary_key=True),
    <PERSON>umn('username', String(32), unique=True),
    <PERSON><PERSON><PERSON>('name', String(32)),
    <PERSON><PERSON><PERSON>('role_name', String(32)),
    <PERSON><PERSON><PERSON>('password', String(128)),
    <PERSON>umn('session_id', String(128)),
    <PERSON><PERSON><PERSON>('create_at', DateTime()),
    <PERSON>umn('updated_at', DateTime()),
    <PERSON>umn('status', String(16)),
    Column('expiredday', DateTime()), 
)

role = Table(
    'role', meta,
    Column('id', Integer, primary_key=True),
    <PERSON>umn('name', String(32), unique=True),
    <PERSON>umn('display_name', String(32), unique=True),
)


def upgrade(migrate_engine):
    meta.bind = migrate_engine

    # 插入数据到 'role' 表
    migrate_engine.execute(role.insert(), [
        {'id': 1, 'name': 'sysadm', 'description': '系统管理员'},
        {'id': 2, 'name': 'adtadm', 'description': '审计管理员'},
        {'id': 3, 'name': 'secadm', 'description': '安全管理员'},
        {'id': 4, 'name': 'operator', 'description': '操作员'}
    ])

    # 插入数据到 'users' 表
    migrate_engine.execute(users.insert(), [
        {'username': 'sysadm', 'name': 'sysadm', 'password': '$2b$12$fyUmcVjFihb/ZV0i8/yoUO97m0jbiFr15FBjBJKOpKzXZYaVpNyLW', 'role_name': 'sysadm', 'status': 'on'},
        {'username': 'adtadm', 'name': 'adtadm', 'password': '$2b$12$fyUmcVjFihb/ZV0i8/yoUO97m0jbiFr15FBjBJKOpKzXZYaVpNyLW', 'role_name': 'adtadm', 'status': 'on'},
        {'username': 'secadm', 'name': 'secadm', 'password': '$2b$12$fyUmcVjFihb/ZV0i8/yoUO97m0jbiFr15FBjBJKOpKzXZYaVpNyLW', 'role_name': 'secadm', 'status': 'on'}
    ])

def downgrade(migrate_engine):
    meta.bind = migrate_engine
    
    # 删除 'role' 表中指定的记录
    migrate_engine.execute(role.delete().where(role.c.id.in_([1, 2, 3, 4])))

    # 删除 'users' 表中指定的记录
    migrate_engine.execute(users.delete().where(users.c.username.in_(['sysadm', 'adtadm', 'secadm'])))