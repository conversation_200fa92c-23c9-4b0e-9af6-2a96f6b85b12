from sqlalchemy import *
from migrate import *


from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime

meta = MetaData()

ws = Table(
    'wsclients', meta,
    Column('id', Integer, primary_key=True),
    <PERSON>umn('session_id', String(64)),
    <PERSON>umn('username', String(32)),
    <PERSON><PERSON>n('protocol', String(32)),
    <PERSON><PERSON>n('host', String(32)),
    <PERSON>umn('path', String(64)),
    <PERSON>umn('callback_path', String(64))
)

def upgrade(migrate_engine):
    meta.bind = migrate_engine
    ws.create()

def downgrade(migrate_engine):
    meta.bind = migrate_engine
    ws.drop()