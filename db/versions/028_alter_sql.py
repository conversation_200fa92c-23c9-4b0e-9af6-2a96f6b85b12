from sqlalchemy import Table, Column, Integer, String, MetaData, create_engine

meta = MetaData()

def upgrade(migrate_engine):
    meta.bind = migrate_engine

    # 添加 'deskpool_group' 表的唯一约束 'uq_deskpool_group_name'
    migrate_engine.execute("ALTER TABLE deskpool_group ADD CONSTRAINT uq_deskpool_group_name UNIQUE (name)")

    # 添加 'desk_user_group' 表的唯一约束 'uq_desk_user_group_name'
    migrate_engine.execute("ALTER TABLE desk_user_group ADD CONSTRAINT uq_desk_user_group_name UNIQUE (name)")

    # 添加 'device_group' 表的唯一约束 'uq_device_group_name'
    migrate_engine.execute("ALTER TABLE device_group ADD CONSTRAINT uq_device_group_name UNIQUE (name)")

    # 添加 'deskuser' 表的唯一约束 'uq_deskuser_username'
    migrate_engine.execute("ALTER TABLE deskuser ADD CONSTRAINT uq_deskuser_username UNIQUE (username)")

    # 添加 'deskpool' 表的唯一约束 'uq_deskpool_name'
    migrate_engine.execute("ALTER TABLE deskpool ADD CONSTRAINT uq_deskpool_name UNIQUE (name)")

    # 添加 'desk' 表的唯一约束 'uq_desk_vm_id'
    migrate_engine.execute("ALTER TABLE desk ADD CONSTRAINT uq_desk_vm_id UNIQUE (vm_id)")

    # 添加 'desk' 表的唯一约束 'uq_desk_vm_name'
    migrate_engine.execute("ALTER TABLE desk ADD CONSTRAINT uq_desk_vm_name UNIQUE (vm_name)")

    # 添加 'desk' 表的唯一约束 'uq_desk_name'
    migrate_engine.execute("ALTER TABLE desk ADD CONSTRAINT uq_desk_name UNIQUE (name)")

    # 添加 'desk_template' 表的唯一约束 'uq_desk_template_name'
    migrate_engine.execute("ALTER TABLE desk_template ADD CONSTRAINT uq_desk_template_name UNIQUE (name)")


def downgrade(migrate_engine):
    meta.bind = migrate_engine

    # 删除 'deskpool_group' 表的唯一约束 'uq_deskpool_group_name'
    migrate_engine.execute("ALTER TABLE deskpool_group DROP CONSTRAINT uq_deskpool_group_name")

    # 删除 'desk_user_group' 表的唯一约束 'uq_desk_user_group_name'
    migrate_engine.execute("ALTER TABLE desk_user_group DROP CONSTRAINT uq_desk_user_group_name")

    # 删除 'device_group' 表的唯一约束 'uq_device_group_name'
    migrate_engine.execute("ALTER TABLE device_group DROP CONSTRAINT uq_device_group_name")

    # 删除 'deskuser' 表的唯一约束 'uq_deskuser_username'
    migrate_engine.execute("ALTER TABLE deskuser DROP CONSTRAINT uq_deskuser_username")

    # 删除 'deskpool' 表的唯一约束 'uq_deskpool_name'
    migrate_engine.execute("ALTER TABLE deskpool DROP CONSTRAINT uq_deskpool_name")

    # 删除 'desk' 表的唯一约束 'uq_desk_vm_id'
    migrate_engine.execute("ALTER TABLE desk DROP CONSTRAINT uq_desk_vm_id")

    # 删除 'desk' 表的唯一约束 'uq_desk_vm_name'
    migrate_engine.execute("ALTER TABLE desk DROP CONSTRAINT uq_desk_vm_name")

    # 删除 'desk' 表的唯一约束 'uq_desk_name'
    migrate_engine.execute("ALTER TABLE desk DROP CONSTRAINT uq_desk_name")

    # 删除 'desk_template' 表的唯一约束 'uq_desk_template_name'
    migrate_engine.execute("ALTER TABLE desk_template DROP CONSTRAINT uq_desk_template_name")