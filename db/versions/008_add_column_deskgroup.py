from migrate import *
from sqlalchemy import Table, MetaData, String, Column

from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime

meta = MetaData()

def upgrade(migrate_engine):
    meta = MetaData(bind=migrate_engine)
    deskpool = Table('deskpool', meta, autoload=True)
    createtype = Column('createtype', String(16), server_default="clone")
    createtype.create(deskpool)

def downgrade(migrate_engine):
    meta = MetaData(bind=migrate_engine)
    deskpool = Table('deskpool', meta, autoload=True)
    deskpool.c.createtype.drop()