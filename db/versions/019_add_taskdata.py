from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime, create_engine

meta = MetaData()

tasks = Table(
    'tasks', meta,
    Column('id', Integer, primary_key=True),
    <PERSON><PERSON><PERSON>('method', String(32)),
    <PERSON><PERSON><PERSON>('param', Integer),
    <PERSON><PERSON><PERSON>('complete', String(16)),
    <PERSON><PERSON><PERSON>('status', String(16)),
    <PERSON><PERSON><PERSON>('scheduled', DateTime()),
    <PERSON>umn('create_at', DateTime())
)
def upgrade(migrate_engine):
    meta.bind = migrate_engine

    # 插入数据到 'tasks' 表
    migrate_engine.execute(tasks.insert(), [
        {'method': 'imagescreatefrom', 'param': 1}
    ])

def downgrade(migrate_engine):
    meta.bind = migrate_engine
    
    # 删除 'tasks' 表中指定的记录
    migrate_engine.execute(tasks.delete().where(tasks.c.method == 'imagescreatefrom'))