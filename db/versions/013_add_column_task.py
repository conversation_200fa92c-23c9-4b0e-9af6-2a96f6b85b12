from migrate import *
from sqlalchemy import Table, MetaData, String, Column

from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime

meta = MetaData()

def upgrade(migrate_engine):
    meta = MetaData(bind=migrate_engine)
    task = Table('instance_create_tasks', meta, autoload=True)
    iso = Column('iso', String(16), server_default="false")
    iso.create(task)

def downgrade(migrate_engine):
    meta = MetaData(bind=migrate_engine)
    task = Table('instance_create_tasks', meta, autoload=True)
    task.c.iso.drop()