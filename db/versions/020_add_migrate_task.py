from sqlalchemy import *
from migrate import *

from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime

meta = MetaData()

migrate_task = Table(
    'vmware_migrate_tasks', meta,
    Column('id', Integer, primary_key=True),
    <PERSON>umn('vmware_name', String(64)),
    <PERSON>umn('vm_name', String(64)),
    <PERSON>umn('network_uuid', String(64)),
    <PERSON>umn('vm_cpu', Integer),
    <PERSON>umn('vm_ram', Integer),
    <PERSON>umn('vm_disks', String(64)),
    <PERSON>umn('cloudy_id', String(64)),
    <PERSON>um<PERSON>('cloudy_host', String(64)),
    <PERSON>umn('vm_os', String(64)),
    <PERSON><PERSON><PERSON>('vm_os_version', String(64)),
    <PERSON><PERSON><PERSON>('source_datacenter', String(32)),
    <PERSON>umn('source_cluster', String(32)),
    <PERSON>umn('source_host', String(32)),
    <PERSON>umn('target_zone', String(32)),
    <PERSON>umn('error_reason', String(255)),
    <PERSON>umn('error_step', String(32)),
    <PERSON>umn('status', String(16)),
    <PERSON><PERSON><PERSON>('task_status', String(16)),
    Column('create_at', DateTime()),
    Column('complete_at', DateTime()) 
)




def upgrade(migrate_engine):
    meta.bind = migrate_engine
    migrate_task.create()


def downgrade(migrate_engine):
    meta.bind = migrate_engine
    migrate_task.drop()

