from sqlalchemy import Table, Column, Integer, String, MetaData, create_engine
from sqlalchemy import DateTime

meta = MetaData()


def upgrade(migrate_engine):
    meta = MetaData(bind=migrate_engine)
    users = Table('users', meta, autoload=True)

    # 插入数据到 'users' 表
    migrate_engine.execute(users.insert(), [
        {'username': 'supadm', 'name': 'supadm', 'password': '$2b$12$fyUmcVjFihb/ZV0i8/yoUO97m0jbiFr15FBjBJKOpKzXZYaVpNyLW', 'role_name': 'sysadm', 'status': 'on'}
    ])
    

    

def downgrade(migrate_engine):
    meta.bind = migrate_engine
    users = Table('users', meta, autoload=True)
    # 删除 'users' 表中指定的记录
    migrate_engine.execute(users.delete().where(users.c.username.in_(['supadm'])))