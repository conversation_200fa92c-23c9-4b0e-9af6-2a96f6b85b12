from migrate import *
from sqlalchemy import Table, MetaData, String, Column

from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime

meta = MetaData()

def upgrade(migrate_engine):
    meta = MetaData(bind=migrate_engine)
    cluster_host = Table('cluster_host', meta, autoload=True)
    state = Column('state', String(64), server_default="")
    state.create(cluster_host)
    state_updated = Column('state_updated', DateTime(), server_default=None)
    state_updated.create(cluster_host)
    status = Column('status', String(64), server_default="")
    status.create(cluster_host)
    weihu_state = Column('weihu_state', String(64), server_default="on")
    weihu_state.create(cluster_host)
    weihu_state_updated = Column('weihu_state_updated', DateTime(), server_default=None)
    weihu_state_updated.create(cluster_host)
    enabled_auto = Column('enabled_auto', String(16), server_default="on")
    enabled_auto.create(cluster_host)
    

    
def downgrade(migrate_engine):
    meta = MetaData(bind=migrate_engine)
    task = Table('cluster_host', meta, autoload=True)
    task.c.state.drop()
    task.c.state_updated.drop()
    task.c.status.drop()
    task.c.weihu_state.drop()
    task.c.weihu_state_updated.drop()
    task.c.enabled_auto.drop()
