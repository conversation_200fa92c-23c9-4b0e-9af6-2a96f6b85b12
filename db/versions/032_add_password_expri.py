from sqlalchemy import *
from migrate import *

from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime

meta = MetaData()


password_updated_record = Table(
    'password_updated_record', meta,
    Column('id', Integer, primary_key=True),
    <PERSON>umn('user_id', Integer),
    <PERSON>umn('status', String(32)),
    <PERSON>umn('create_at', DateTime()),
)


password_expired_config = Table(
    'password_expired_config', meta,
    Column('id', Integer, primary_key=True),
    <PERSON>umn('pwd_length', Integer),
    <PERSON>umn('pwd_class', String(32)),
    <PERSON>umn('pwd_expired_day', Integer),
    <PERSON>umn('status', String(32)),
    <PERSON>umn('create_at', DateTime()),
    Column('updated_at', DateTime()),
)

def upgrade(migrate_engine):
    meta.bind = migrate_engine
    password_updated_record.create()
    password_expired_config.create()

    
    
def downgrade(migrate_engine):
    meta.bind = migrate_engine
    password_updated_record.drop()
    password_expired_config.drop()

    

