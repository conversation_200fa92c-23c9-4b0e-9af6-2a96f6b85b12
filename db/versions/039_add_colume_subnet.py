from sqlalchemy import *
from migrate import *

from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime

meta = MetaData()



def upgrade(migrate_engine):
    meta.bind = migrate_engine
    desk_template = Table('desk_template', meta, autoload=True)
    subnet_id = Column('subnet_id', String(64), server_default="")
    subnet_id.create(desk_template)

    
    
def downgrade(migrate_engine):
    meta.bind = migrate_engine
    desk_template = Table('desk_template', meta, autoload=True)
    desk_template.c.subnet_id.drop()

    
