from sqlalchemy import Table, Column, String, MetaData

meta = MetaData()


def upgrade(migrate_engine):
    meta.bind = migrate_engine
    # 定义表结构，以便于绑定元数据
    deskpool_group = Table('deskpool_group', meta, autoload_with=migrate_engine)
    deskpool = Table('deskpool', meta, autoload_with=migrate_engine)

    # 添加 'deskpool_group' 表的列 'group_type'，默认值为 "many"
    migrate_engine.execute("ALTER TABLE deskpool_group ADD COLUMN group_type VARCHAR(32) DEFAULT 'many'")

    # 添加 'deskpool' 表的列 'status'，默认值为 "enable"
    migrate_engine.execute("ALTER TABLE deskpool ADD COLUMN status VARCHAR(32) DEFAULT 'enable'")


def downgrade(migrate_engine):
    meta.bind = migrate_engine
    # 定义表结构，以便于绑定元数据
    deskpool_group = Table('deskpool_group', meta, autoload_with=migrate_engine)
    deskpool = Table('deskpool', meta, autoload_with=migrate_engine)

    # 删除 'deskpool_group' 表的列 'group_type'
    migrate_engine.execute("ALTER TABLE deskpool_group DROP COLUMN group_type")

    # 删除 'deskpool' 表的列 'status'
    migrate_engine.execute("ALTER TABLE deskpool DROP COLUMN status")