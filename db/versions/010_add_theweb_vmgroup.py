from sqlalchemy import *
from migrate import *

from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime

meta = MetaData()

group_vm_relationship = Table(
    "group_vm_relationship", meta,
    Column("vmgroup_id", Integer),
    Column("vms_id",Integer),
    PrimaryKeyConstraint("vmgroup_id", "vms_id")
)


vmgroup = Table(
    'vmgroup', meta,
    Column('id', Integer, primary_key=True),
    <PERSON>umn('name', String(32)),
    <PERSON>umn('pid', Integer),
    <PERSON>umn('user_id', Integer),
    Column('pp', String(128)),
)

vms_r = Table(
    "vms", meta,
    Column('id', Integer, primary_key=True),
    <PERSON>umn("vmid", String(64)),
    <PERSON><PERSON><PERSON>("user_id",Integer),
    <PERSON>umn("vmgroup_id",Integer),
)

volumes = Table(
    "volumes", meta,
    Column('id', Integer, primary_key=True),
    <PERSON>umn("volume_id", String(64)),
    <PERSON>umn("vm_id", String(64)),
    <PERSON>umn("user_id",Integer),
)

def upgrade(migrate_engine):
    meta.bind = migrate_engine
    vmgroup.create()
    vms_r.create()
    volumes.create()
    group_vm_relationship.create()


def downgrade(migrate_engine):
    meta.bind = migrate_engine
    vmgroup.drop()
    vms_r.drop()
    volumes.drop()
    group_vm_relationship.drop()
