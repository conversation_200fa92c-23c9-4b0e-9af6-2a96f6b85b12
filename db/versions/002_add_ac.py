from sqlalchemy import *
from migrate import *


from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime

meta = MetaData()

acglobal = Table(
    'ac_global', meta,
    Column('id', Integer, primary_key=True),
    Column('k', String(32)),
    <PERSON>umn('v', String(64)),
    <PERSON>umn('t', String(16)),
    <PERSON>umn('name', String(64)),

)

def upgrade(migrate_engine):
    meta.bind = migrate_engine
    acglobal.create()

def downgrade(migrate_engine):
    meta.bind = migrate_engine
    acglobal.drop()
