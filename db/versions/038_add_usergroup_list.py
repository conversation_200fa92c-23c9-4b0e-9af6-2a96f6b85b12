from sqlalchemy import *
from migrate import *

from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime

meta = MetaData()


usergroup_usb_blacklist = Table(
    'usergroup_usb_blacklist', meta,
    Column('id', Integer, primary_key=True),
    <PERSON><PERSON><PERSON>('usergroup_id', Integer),
    <PERSON><PERSON><PERSON>('pid', String(32)),
    <PERSON><PERSON><PERSON>('vid', String(32)),
    <PERSON><PERSON><PERSON>('name', String(64))
)

usergroup_usb_whitelist = Table(
    'usergroup_usb_whitelist', meta,
    Column('id', Integer, primary_key=True),
    <PERSON>umn('usergroup_id', Integer),
    <PERSON><PERSON><PERSON>('pid', String(32)),
    <PERSON><PERSON><PERSON>('vid', String(32)),
    <PERSON>umn('name', String(64))
)



def upgrade(migrate_engine):
    meta.bind = migrate_engine
    usergroup_usb_whitelist.create()
    usergroup_usb_blacklist.create()
    
    
def downgrade(migrate_engine):
    meta.bind = migrate_engine
    usergroup_usb_whitelist.drop()
    usergroup_usb_blacklist.drop()

