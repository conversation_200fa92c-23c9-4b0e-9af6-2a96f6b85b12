from sqlalchemy import *
from migrate import *

from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime

meta = MetaData()

cloudy = Table(
    'cloudy', meta,
    Column('id', Integer, primary_key=True),
    <PERSON><PERSON><PERSON>('username', String(64)),
    <PERSON><PERSON><PERSON>('password', String(64)),
    <PERSON>umn('ip', String(32)),
    <PERSON>umn('create_at', DateTime()),
    
)




def upgrade(migrate_engine):
    meta.bind = migrate_engine
    cloudy.create()


def downgrade(migrate_engine):
    meta.bind = migrate_engine
    cloudy.drop()

