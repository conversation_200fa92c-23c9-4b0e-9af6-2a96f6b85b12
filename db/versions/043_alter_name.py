from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime, Text

meta = MetaData()

deskuser = Table(
    'deskuser', meta,
    Column('id', Integer, primary_key=True),
    <PERSON><PERSON>n('username', String(32)),
    <PERSON><PERSON><PERSON>('name', String(32)),
    <PERSON><PERSON><PERSON>('password', String(64)),
    <PERSON><PERSON><PERSON>('domain', String(64)),
    <PERSON><PERSON><PERSON>('organize', String(64)),
    <PERSON><PERSON><PERSON>('email', String(64)),
    <PERSON><PERSON><PERSON>('phone', String(64)),
    <PERSON><PERSON><PERSON>('session_id', String(64)),
    <PERSON><PERSON><PERSON>('user_source_id', Integer),
    <PERSON>umn('status', String(16)),
    <PERSON>umn('create_at', DateTime()),
)



def upgrade(migrate_engine):
    meta.bind = migrate_engine

    # 重命名 'deskuser' 表中的列 'create_at' 为 'created_at'
    # migrate_engine.execute("ALTER TABLE deskuser RENAME COLUMN create_at TO created_at")
    migrate_engine.execute("ALTER TABLE deskuser CHANGE COLUMN create_at created_at DATETIME")

def downgrade(migrate_engine):
    meta.bind = migrate_engine

    # 将 'deskuser' 表中的列 'created_at' 重命名回 'create_at'
    # migrate_engine.execute("ALTER TABLE deskuser RENAME COLUMN created_at TO create_at")
    migrate_engine.execute("ALTER TABLE deskuser CHANGE COLUMN created_at create_at DATETIME")
    
