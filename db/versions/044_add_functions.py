from sqlalchemy import Table, Column, Integer, String, MetaData, create_engine

# 初始化元数据
meta = MetaData()

# 创建 'functions' 表 这个表不需要 只是设计表结构 用于参考 数据在中间件中存府
functions = Table(
    'functions', meta,
    Column('id', Integer, primary_key=True, autoincrement=True),
    <PERSON>umn('module', String(100)),
    <PERSON>umn('submodule', String(100)),
    <PERSON>umn('function', String(100)),
    <PERSON>umn('module_code', String(32)),
    <PERSON>umn('submodule_code', String(32)),
    Column('function_code', String(32)),
    <PERSON>umn('display', String(32)), # 是否显示
    Column('url', String(255))
)

# 创建 'users_functions' 表
users_functions = Table(
    'users_functions', meta,
    Column('user_id', Integer),
    Column('function_ids', String(255))
)

def upgrade(migrate_engine):
    # 绑定引擎到元数据
    meta.bind = migrate_engine
    # 创建 'functions' 表
    functions.create(migrate_engine)
    # 创建 'users_functions' 表
    users_functions.create(migrate_engine)

def downgrade(migrate_engine):
    # 绑定引擎到元数据
    meta.bind = migrate_engine
    # 删除 'users_functions' 表
    users_functions.drop(migrate_engine)
    # 删除 'functions' 表
    functions.drop(migrate_engine)