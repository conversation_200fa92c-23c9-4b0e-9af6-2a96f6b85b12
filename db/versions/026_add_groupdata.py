from sqlalchemy import Table, Column, Integer, String, MetaData

meta = MetaData()

# 定义 'deskpool_group' 表结构
deskpool_group = Table(
    'deskpool_group', meta,
    Column('id', Integer, primary_key=True),
    <PERSON>umn('name', String(32)),
    <PERSON><PERSON><PERSON>('pid', Integer),
    <PERSON><PERSON><PERSON>('user_id', Integer),
    <PERSON><PERSON><PERSON>('pp', String(128)),
)



device_group = Table(
    'device_group', meta,
    Column('id', Integer, primary_key=True),
    <PERSON><PERSON>n('name', String(64)),
    <PERSON><PERSON>n('pid', Integer),
    <PERSON><PERSON>n('user_id', Integer),
    <PERSON>umn('pp', String(128)),
)





desk_user_group = Table(
    'desk_user_group', meta,
    Column('id', Integer, primary_key=True),
    <PERSON><PERSON><PERSON>('name', String(64)),
    <PERSON>umn('info', String(64)),
    <PERSON><PERSON><PERSON>('pid', Integer),
    <PERSON><PERSON><PERSON>('user_id', Integer),
    <PERSON>umn('pp', String(128)),
)

def upgrade(migrate_engine):
    meta.bind = migrate_engine

    # 插入数据到 'deskpool_group' 表
    migrate_engine.execute(deskpool_group.insert(), [
        {'id': 1, 'name': '默认', 'parent_id': -1, 'is_active': 1, 'extra_info': '-1'}
    ])

    # 插入数据到 'device_group' 表
    migrate_engine.execute(device_group.insert(), [
        {'id': 1, 'name': '默认', 'parent_id': -1, 'is_active': 1, 'extra_info': '-1'}
    ])

    # 插入数据到 'desk_user_group' 表
    migrate_engine.execute(desk_user_group.insert(), [
        {'id': 1, 'name': '默认', 'description': '', 'parent_id': -1, 'is_active': 1, 'extra_info': '-1'}
    ])

def downgrade(migrate_engine):
    meta.bind = migrate_engine
    
    # 删除 'deskpool_group' 表中指定的记录
    migrate_engine.execute(deskpool_group.delete().where(deskpool_group.c.id == 1))

    # 删除 'device_group' 表中指定的记录
    migrate_engine.execute(device_group.delete().where(device_group.c.id == 1))

    # 删除 'desk_user_group' 表中指定的记录
    migrate_engine.execute(desk_user_group.delete().where(desk_user_group.c.id == 1))