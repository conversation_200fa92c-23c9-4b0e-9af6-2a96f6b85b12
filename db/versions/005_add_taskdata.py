from sqlalchemy import *
from migrate import *


from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime

meta = MetaData()

tasks = Table(
    'tasks', meta,
    Column('id', Integer, primary_key=True),
    <PERSON>um<PERSON>('method', String(32)),
    <PERSON><PERSON><PERSON>('param', Integer),
    <PERSON><PERSON><PERSON>('complete', String(16)),
    <PERSON><PERSON><PERSON>('status', String(16)),
    <PERSON><PERSON><PERSON>('scheduled', DateTime()),
    <PERSON>umn('create_at', DateTime())
)

acglobal = Table(
    'ac_global', meta,
    Column('id', Integer, primary_key=True),
    <PERSON>umn('k', String(32)),
    <PERSON>umn('v', String(64)),
    <PERSON><PERSON><PERSON>('t', String(16)),
    <PERSON>umn('name', String(64)),

)

def upgrade(migrate_engine):
    meta.bind = migrate_engine
    # 插入数据到 'ac_global' 表
    migrate_engine.execute(acglobal.insert(), [
        {'id': 1, 'k': 'usb_device', 'v': 'enabled', 't': 'switch', 'name': 'USB外设'},
        {'id': 2, 'k': 'spice_ssl', 'v': 'enabled', 't': 'switch', 'name': '传输加密'},
        {'id': 3, 'k': 'web', 'v': 'enabled', 't': 'switch', 'name': 'web访问桌面'},
        {'id': 4, 'k': 'user_connect_timeout', 'v': '30', 't': 'minute', 'name': '用户连接超时'},
        {'id': 5, 'k': 'session_timeout', 'v': '300', 't': 'minute', 'name': '强制断开用户会话'},
        {'id': 6, 'k': 'watermark', 'v': 'enabled', 't': 'switch', 'name': '防泄露水印'},
        {'id': 7, 'k': 'multiprotocol', 'v': 'enabled', 't': 'switch', 'name': '用户选择访问协议'},
        {'id': 8, 'k': 'video_acceleration', 'v': 'enabled', 't': 'switch', 'name': '视频加速'},
        {'id': 9, 'k': 'clipboard', 'v': 'enabled', 't': 'switch', 'name': '剪切板'}
    ])

    # 插入数据到 'tasks' 表
    migrate_engine.execute(tasks.insert(), [
        {'method': 'instancecreatefrom', 'param': 1},
        {'method': 'ex_volume', 'param': 1}
    ])


def downgrade(migrate_engine):
    pass