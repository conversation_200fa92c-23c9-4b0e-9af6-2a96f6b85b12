from sqlalchemy import *
from migrate import *


from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime

meta = MetaData()

wssession = Table(
    'wssession', meta,
    Column('id', Integer, primary_key=True),
    <PERSON>umn('session_id', String(64)),
    <PERSON><PERSON><PERSON>('username', String(32)),
    <PERSON><PERSON><PERSON>('vm_id', String(64)),
    <PERSON>um<PERSON>('vm_name', String(64)),
    <PERSON>umn('vm_ip', String(32)),
    <PERSON>umn('client_ip', String(32)),
    <PERSON>umn("desk_id", Integer),
    Column('create_at', DateTime())
)

def upgrade(migrate_engine):
    meta.bind = migrate_engine
    wssession.create()

def downgrade(migrate_engine):
    meta.bind = migrate_engine
    wssession.drop()