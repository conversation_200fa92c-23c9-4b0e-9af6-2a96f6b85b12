from sqlalchemy import *
from migrate import *

from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime, Text

meta = MetaData()


ha_vm_whitelist = Table(
    'ha_vm_whitelist', meta,
    Column('id', Integer, primary_key=True, autoincrement=True),
    Column('vm_id', String(255), nullable=False),
    Column('created_at', DateTime(timezone=True), server_default=func.now())
)


def upgrade(migrate_engine):
    meta.bind = migrate_engine
    ha_vm_whitelist.create()


def downgrade(migrate_engine):
    meta.bind = migrate_engine
    ha_vm_whitelist.drop()
