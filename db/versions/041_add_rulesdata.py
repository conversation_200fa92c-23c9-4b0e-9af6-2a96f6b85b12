from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime, Text

meta = MetaData()



def upgrade(migrate_engine):
    meta.bind = migrate_engine
    # 假设 'alert_rules' 表已经存在，并使用元数据进行自动加载
    alert_rules = Table('alert_rules', meta, autoload_with=migrate_engine)
    # 插入数据到 'alert_rules' 表
    migrate_engine.execute(alert_rules.insert(), [
        {'id': 1, 'name': '服务器离线', 'expr': '(up{job="node-exporter"}) == 0', 'expr_code': '0', 'value': 0, 'unit': None,
         'for_interval': '0m', 'report_interval': '10m', 'critical_value': -1, 'major_value': 0, 'warning_value': 0,
         'info_value': 0, 'description': '服务器离线: VALUE = {{ $value }};  LABELS = {{ $labels }}',
         'summary': '服务器离线 (instance {{ $labels.instance }})', 'job': None, 'alert_type': None, 'created_at': None,
         'updated_at': None, 'is_default': 1, 'status': 'enabled', 'order_id': 0},

        {'id': 2, 'name': '节点失联', 'expr': 'up{job="openstacklinux"} == 0 or up{job="openstackwindows"} == 0',
         'expr_code': '0', 'value': 0, 'unit': None, 'for_interval': '3m', 'report_interval': '10m', 'critical_value': -1,
         'major_value': 0, 'warning_value': 0, 'info_value': 0,
         'description': '节点失联: VALUE = {{ $value }};  LABELS = {{ $labels }}', 
         'summary': '节点失联 (instance {{ $labels.instance }})', 'job': None, 'alert_type': None, 'created_at': None,
         'updated_at': None, 'is_default': 1, 'status': 'enabled', 'order_id': 0},

        {'id': 3, 'name': '任务失联', 'expr': 'absent(up{job="prometheus"})', 'expr_code': '0', 'value': 0, 'unit': None,
         'for_interval': '3m', 'report_interval': '10m', 'critical_value': -1, 'major_value': 0, 'warning_value': 0,
         'info_value': 0, 'description': '任务失联: VALUE = {{ $value }};  LABELS = {{ $labels }}',
         'summary': '任务失联 (instance {{ $labels.instance }})', 'job': None, 'alert_type': None, 'created_at': None,
         'updated_at': None, 'is_default': 1, 'status': 'enabled', 'order_id': 0},

        {'id': 4, 'name': '节点CPU负载过高', 'expr': '100 - (avg by(instance) (rate(node_cpu_seconds_total{mode="idle"}[2m])) * 100) > 90',
         'expr_code': '1', 'value': 0, 'unit': None, 'for_interval': '0m', 'report_interval': '10m', 'critical_value': 90,
         'major_value': 0, 'warning_value': 0, 'info_value': 0, 'description': '节点CPU负载过高: VALUE = {{ $value }};  LABELS = {{ $labels }}',
         'summary': '节点CPU负载过高 (instance {{ $labels.instance }})', 'job': None, 'alert_type': None, 'created_at': None,
         'updated_at': None, 'is_default': 1, 'status': 'enabled', 'order_id': 0},

        {'id': 5, 'name': '节点内存占用过高', 'expr': 'node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes * 100 < 10',
         'expr_code': '1', 'value': 0, 'unit': None, 'for_interval': '2m', 'report_interval': '10m', 'critical_value': 10,
         'major_value': 0, 'warning_value': 0, 'info_value': 0, 'description': '节点内存占用过高: VALUE = {{ $value }};  LABELS = {{ $labels }}',
         'summary': '节点内存占用过高 (instance {{ $labels.instance }})', 'job': None, 'alert_type': None, 'created_at': None,
         'updated_at': None, 'is_default': 1, 'status': 'enabled', 'order_id': 0},

        {'id': 6, 'name': '磁盘空间不足', 'expr': 'node_filesystem_files_free{mountpoint ="/rootfs"} / node_filesystem_files{mountpoint="/rootfs"} * 100 < 10 and ON (instance, device, mountpoint) node_filesystem_readonly{mountpoint="/rootfs"} == 90',
         'expr_code': '2', 'value': 0, 'unit': None, 'for_interval': '5m', 'report_interval': '10m', 'critical_value': 90,
         'major_value': 0, 'warning_value': 0, 'info_value': 0, 'description': '磁盘空间不足: VALUE = {{ $value }};  LABELS = {{ $labels }}',
         'summary': '磁盘空间不足 (instance {{ $labels.instance }})', 'job': None, 'alert_type': None, 'created_at': None,
         'updated_at': None, 'is_default': 1, 'status': 'enabled', 'order_id': 0},

        {'id': 7, 'name': '网络带库占用过高', 'expr': 'sum by (instance) (rate(node_network_transmit_bytes_total[2m])) / 1024 / 1024 > 100',
         'expr_code': '1', 'value': 0, 'unit': None, 'for_interval': '5m', 'report_interval': '10m', 'critical_value': 100,
         'major_value': 0, 'warning_value': 0, 'info_value': 0, 'description': '网络带库占用过高: VALUE = {{ $value }};  LABELS = {{ $labels }}',
         'summary': '网络带库占用过高 (instance {{ $labels.instance }})', 'job': None, 'alert_type': None, 'created_at': None,
         'updated_at': None, 'is_default': 1, 'status': 'enabled', 'order_id': 0},

        {'id': 8, 'name': '磁盘使用率过高', 'expr': 'sum by (instance) (rate(node_disk_read_bytes_total[2m])) / 1024 / 1024 > 90',
         'expr_code': '1', 'value': 0, 'unit': None, 'for_interval': '5m', 'report_interval': '10m', 'critical_value': 90,
         'major_value': 0, 'warning_value': 0, 'info_value': 0, 'description': '磁盘使用率过高: VALUE = {{ $value }};  LABELS = {{ $labels }}',
         'summary': '磁盘使用率过高 (instance {{ $labels.instance }})', 'job': None, 'alert_type': None, 'created_at': None,
         'updated_at': None, 'is_default': 1, 'status': 'enabled', 'order_id': 0},

        {'id': 9, 'name': '磁盘可用空间不足', 'expr': '(node_filesystem_avail_bytes * 100) / node_filesystem_size_bytes < 10 and ON (instance, device, mountpoint) node_filesystem_readonly == 0',
         'expr_code': '2', 'value': 0, 'unit': None, 'for_interval': '2m', 'report_interval': '10m', 'critical_value': 10,
         'major_value': 0, 'warning_value': 0, 'info_value': 0, 'description': '磁盘可用空间不足: VALUE = {{ $value }};  LABELS = {{ $labels }}',
         'summary': '磁盘可用空间不足 (instance {{ $labels.instance }})', 'job': None, 'alert_type': None, 'created_at': None,
         'updated_at': None, 'is_default': 1, 'status': 'enabled', 'order_id': 0},

        {'id': 10, 'name': '分布式存储健康状态告警', 'expr': 'ceph_health_status == 1', 'expr_code': '0', 'value': 0, 'unit': None,
         'for_interval': '1m', 'report_interval': '10m', 'critical_value': -1, 'major_value': 0, 'warning_value': 0,
         'info_value': 0, 'description': '分布式存储健康状态告警', 'summary': '分布式存储健康状态告警', 'job': 'ceph', 'alert_type': None,
         'created_at': None, 'updated_at': None, 'is_default': 1, 'status': 'enabled', 'order_id': 0},

        {'id': 11, 'name': '分布式存储健康状态异常', 'expr': 'ceph_health_status > 1', 'expr_code': '0', 'value': 0, 'unit': None,
         'for_interval': '1m', 'report_interval': '10m', 'critical_value': -1, 'major_value': 0, 'warning_value': 0,
         'info_value': 0, 'description': '分布式存储健康状态异常', 'summary': '分布式存储健康状态异常', 'job': 'ceph', 'alert_type': None,
         'created_at': None, 'updated_at': None, 'is_default': 1, 'status': 'enabled', 'order_id': 0},

        {'id': 12, 'name': '存储单元可用空间不足', 'expr': '(ceph_osd_stat_bytes_used / ceph_osd_stat_bytes) * 100 > 90',
         'expr_code': '1', 'value': 0, 'unit': None, 'for_interval': '1m', 'report_interval': '10m', 'critical_value': 90,
         'major_value': 0, 'warning_value': 0, 'info_value': 0, 'description': '存储单元可用空间不足: VALUE = {{ $value }};  LABELS = {{ $labels }}',
         'summary': '存储单元可用空间不足', 'job': 'ceph', 'alert_type': None, 'created_at': None, 'updated_at': None,
         'is_default': 1, 'status': 'enabled', 'order_id': 0},

        {'id': 13, 'name': '对象存储守护进程已停止运行', 'expr': '(ceph_osd_up) * 100 < 50', 'expr_code': '0', 'value': 0, 'unit': None,
         'for_interval': '1m', 'report_interval': '10m', 'critical_value': -1, 'major_value': 0, 'warning_value': 0,
         'info_value': 0, 'description': '对象存储守护进程已停止运行: VALUE = {{ $value }};  LABELS = {{ $labels }}',
         'summary': '对象存储守护进程已停止运行', 'job': 'ceph', 'alert_type': None, 'created_at': None, 'updated_at': None,
         'is_default': 1, 'status': 'enabled', 'order_id': 0},

        {'id': 14, 'name': '对象存储守护进程所在主机已停止运行', 'expr': ' count by(instance) (ceph_disk_occupation * on(ceph_daemon) group_right(instance) ceph_osd_up == 0) - count by(instance) (ceph_disk_occupation) == 0',
         'expr_code': '0', 'value': 0, 'unit': None, 'for_interval': '1m', 'report_interval': '10m', 'critical_value': -1,
         'major_value': 0, 'warning_value': 0, 'info_value': 0, 'description': '对象存储守护进程所在主机已停止运行: VALUE = {{ $value }};  LABELS = {{ $labels }}',
         'summary': '对象存储守护进程所在主机已停止运行', 'job': 'ceph', 'alert_type': None, 'created_at': None, 'updated_at': None,
         'is_default': 1, 'status': 'enabled', 'order_id': 0},

        {'id': 15, 'name': '分布式存储放置组阻塞', 'expr': 'max(ceph_osd_numpg) > scalar(ceph_pg_active)', 'expr_code': '0', 'value': 0,
         'unit': None, 'for_interval': '1m', 'report_interval': '10m', 'critical_value': -1, 'major_value': 0, 'warning_value': 0,
         'info_value': 0, 'description': '分布式存储放置组阻塞', 'summary': '分布式存储放置组阻塞: VALUE = {{ $value }};  LABELS = {{ $labels }}',
         'job': 'ceph', 'alert_type': None, 'created_at': None, 'updated_at': None, 'is_default': 1, 'status': 'enabled', 'order_id': 0},

        {'id': 16, 'name': '储单元响应缓慢', 'expr': '((irate(node_disk_read_time_seconds_total[5m]) / clamp_min(irate(node_disk_reads_completed_total[5m]), 1) + irate(node_disk_write_time_seconds_total[5m]) / clamp_min(irate(node_disk_writes_completed_total[5m]), 1)) and on (instance, device) ceph_disk_occupation) > 1',
         'expr_code': '0', 'value': 0, 'unit': None, 'for_interval': '1m', 'report_interval': '10m', 'critical_value': -1,
         'major_value': 0, 'warning_value': 0, 'info_value': 0, 'description': '存储单元响应缓慢', 'summary': '存储单元响应缓慢: VALUE = {{ $value }};  LABELS = {{ $labels }}',
         'job': 'ceph', 'alert_type': None, 'created_at': None, 'updated_at': None, 'is_default': 1, 'status': 'enabled', 'order_id': 0},

        {'id': 17, 'name': '网络错误', 'expr': 'sum by (instance, device) (irate(node_network_receive_drop_total{device=~"(eth|en|bond|ib|mlx|p).*"}[5m]) + irate(node_network_receive_errs_total{device=~"(eth|en|bond|ib|mlx|p).*"}[5m]) + irate(node_network_transmit_drop_total{device=~"(eth|en|bond|ib|mlx|p).*"}[5m]) + irate(node_network_transmit_errs_total{device=~"(eth|en|bond|ib|mlx|p).*"}[5m])) > 30',
         'expr_code': '1', 'value': 0, 'unit': '次', 'for_interval': '5m', 'report_interval': '10m', 'critical_value': 30,
         'major_value': 0, 'warning_value': 0, 'info_value': 0, 'description': '网络错误', 'summary': '网络错误，丢包率过高: VALUE = {{ $value }};  LABELS = {{ $labels }}',
         'job': 'ceph', 'alert_type': None, 'created_at': None, 'updated_at': None, 'is_default': 1, 'status': 'enabled', 'order_id': 0},

        {'id': 18, 'name': '存储池可用空间不足', 'expr': '(ceph_pool_stored / (ceph_pool_stored + ceph_pool_max_avail) * 100 + on (pool_id) group_left (name) (ceph_pool_metadata*0)) > 90',
         'expr_code': '1', 'value': 0, 'unit': None, 'for_interval': '1m', 'report_interval': '10m', 'critical_value': 90,
         'major_value': 0, 'warning_value': 0, 'info_value': 0, 'description': '存储池可用空间不足: VALUE = {{ $value }};  LABELS = {{ $labels }}',
         'summary': '存储池可用空间不足', 'job': 'ceph', 'alert_type': None, 'created_at': None, 'updated_at': None, 'is_default': 1,
         'status': 'enabled', 'order_id': 0},
        
        {'id': 19, 'name': '分布式存储监控服务已停止运行', 'expr': 'ceph_mon_quorum_status != 1', 'expr_code': '0', 'value': 0, 'unit': None,
         'for_interval': '1m', 'report_interval': '10m', 'critical_value': -1, 'major_value': 0,
         'major_value': 0, 'warning_value': 0, 'info_value': 0, 'description': '分布式存储监控服务已停止运行',
         'summary': '分布式存储监控服务已停止运行: VALUE = {{ $value }};  LABELS = {{ $labels }}', 'job': 'ceph',
         'alert_type': None, 'created_at': None, 'updated_at': None, 'is_default': 1, 'status': 'enabled', 'order_id': 0},

        {'id': 20, 'name': '存储集群可用空间不足', 'expr': '(sum(ceph_osd_stat_bytes_used) / sum(ceph_osd_stat_bytes)) * 100 > 90',
         'expr_code': '1', 'value': 0, 'unit': None, 'for_interval': '1m', 'report_interval': '10m', 'critical_value': 90,
         'major_value': 0, 'warning_value': 0, 'info_value': 0, 'description': '存储集群可用空间过低: VALUE = {{ $value }};  LABELS = {{ $labels }}',
         'summary': '存储集群可用空间不足', 'job': 'ceph', 'alert_type': None, 'created_at': None, 'updated_at': None,
         'is_default': 1, 'status': 'enabled', 'order_id': 0}
    ])

def downgrade(migrate_engine):
    meta.bind = migrate_engine

    # 删除 'alert_rules' 表中的记录
    migrate_engine.execute(alert_rules.delete().where(alert_rules.c.id.in_([
        1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20
    ])))