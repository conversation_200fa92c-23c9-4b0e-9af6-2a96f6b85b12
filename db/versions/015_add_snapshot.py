from sqlalchemy import *
from migrate import *

from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime

meta = MetaData()

snapshot = Table(
    'snapshots', meta,
    Column('id', Integer, primary_key=True),
    Column('snapshot_id', String(64)),
    Column('user_id', Integer),
    
)




def upgrade(migrate_engine):
    meta.bind = migrate_engine
    snapshot.create()


def downgrade(migrate_engine):
    meta.bind = migrate_engine
    snapshot.drop()

