from sqlalchemy import *
from migrate import *

from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime

meta = MetaData()

ex_desk_volume_task = Table(
    'ex_desk_volume_task', meta,
    Column('id', Integer, primary_key=True),
    <PERSON>umn('volume_id', String(255)),
    <PERSON><PERSON><PERSON>('size', Integer()),
    <PERSON><PERSON><PERSON>('volume_name', String(64)),
    <PERSON>umn('create_desk_task_id', Integer()),
    <PERSON><PERSON><PERSON>('status', String(32)),
    <PERSON><PERSON><PERSON>('retry_count', Integer()),
    <PERSON>umn('created_at', DateTime()),
    <PERSON>umn('updated_at', DateTime())
)



def upgrade(migrate_engine):
    meta.bind = migrate_engine
    desk_create_tasks = Table('desk_create_tasks', meta, autoload=True)
    os_type = Column('os_type', String(32), server_default="")
    os_type.create(desk_create_tasks)
    
    
def downgrade(migrate_engine):
    meta.bind = migrate_engine
    desk_create_tasks = Table('desk_create_tasks', meta, autoload=True)
    desk_create_tasks.c.os_type.drop()
    

