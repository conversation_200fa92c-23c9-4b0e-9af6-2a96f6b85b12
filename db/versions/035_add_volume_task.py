from sqlalchemy import *
from migrate import *

from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime

meta = MetaData()



ex_volume_task = Table(
    'ex_volume_task', meta,
    Column('id', Integer, primary_key=True),
    Column('volume_id', String(255)),
    <PERSON>umn('size', Integer()),
    <PERSON>umn('volume_name', String(64)),
    <PERSON>umn('create_task_id', Integer()),
    <PERSON>umn('status', String(32)),
    <PERSON>umn('retry_count', Integer()),
    <PERSON>umn('created_at', DateTime()),
    Column('updated_at', DateTime())
)


ex_desk_volume_task = Table(
    'ex_desk_volume_task', meta,
    Column('id', Integer, primary_key=True),
    <PERSON>umn('volume_id', String(255)),
    Column('size', Integer()),
    Column('volume_name', String(64)),
    <PERSON>umn('create_desk_task_id', Integer()),
    Column('status', String(32)),
    Column('retry_count', Integer()),
    Column('created_at', DateTime()),
    Column('updated_at', DateTime())
)


password_expired_token = Table(
    'password_expired_token', meta,
    Column('id', Integer, primary_key=True),
    Column('username', String(128)),
    Column('token', String(128)),
    Column('state', String(16)),
    Column('created_at', DateTime()),
    Column('updated_at', DateTime())
)




def upgrade(migrate_engine):
    meta.bind = migrate_engine
    ex_volume_task.create()
    password_expired_token.create()
    ex_desk_volume_task.create()
    instance_create_tasks = Table('instance_create_tasks', meta, autoload=True)
    vmid = Column('vmid', String(64), server_default="")
    vmid.create(instance_create_tasks)
    os_type = Column('os_type', String(64), server_default="")
    os_type.create(instance_create_tasks)
    
    
def downgrade(migrate_engine):
    meta.bind = migrate_engine
    ex_volume_task.drop()
    ex_desk_volume_task.drop()
    password_expired_token.drop()
    instance_create_tasks = Table('instance_create_tasks', meta, autoload=True)
    instance_create_tasks.c.vmid.drop()
    instance_create_tasks.c.os_type.drop()
    

