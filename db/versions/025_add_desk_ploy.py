from sqlalchemy import *
from migrate import *

from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime

meta = MetaData()

defaultploy = Table(
    'default_ploy', meta,
    Column('id', Integer, primary_key=True),
    Column('name', String(64)),
    Column('key', String(32)),
    Column('value', String(255)),
    <PERSON>umn('button_type', String(32)),
    Column('create_at', DateTime()),
    Column('updated_at', DateTime()),
)

deskusergroup = Table(
    'desk_user_group', meta,
    Column('id', Integer, primary_key=True),
    Column('name', String(64)),
    Column('info', String(64)),
    Column('pid', Integer),
    Column('user_id', Integer),
    Column('pp', String(128)),
)

deskuser_deskusergroup_r = Table(
    "deskuser_deskusergroup_r", meta,
    Column("deskuser_id", Integer),
    Column("deskusergroup_id",Integer),
    PrimaryKeyConstraint("deskuser_id", "deskusergroup_id")
)


deskuserploy = Table(
    'deskuser_ploy', meta,
    Column('id', Integer, primary_key=True),
    Column('deskuser_id', Integer),
    Column('name', String(64)),
    Column('key', String(32)),
    Column('value', String(255)),
    Column('button_type', String(32)),
    Column('create_at', DateTime()),
    Column('updated_at', DateTime()),
)

deskploy = Table(
    'desk_ploy', meta,
    Column('id', Integer, primary_key=True),
    Column('desk_id', Integer),
    Column('name', String(64)),
    Column('key', String(32)),
    Column('value', String(255)),
    Column('button_type', String(32)),
    Column('create_at', DateTime()),
    Column('updated_at', DateTime()),
)


deviceploy = Table(
    'device_ploy', meta,
    Column('id', Integer, primary_key=True),
    Column('device_id', Integer),
    Column('name', String(64)),
    Column('key', String(32)),
    Column('value', String(255)),
    Column('button_type', String(32)),
    Column('create_at', DateTime()),
    Column('updated_at', DateTime()),
)


deskpoolploy = Table(
    'deskpool_ploy', meta,
    Column('id', Integer, primary_key=True),
    Column('deskpool_id', Integer),
    Column('name', String(64)),
    Column('key', String(32)),
    Column('value', String(255)),
    Column('button_type', String(32)),
    Column('create_at', DateTime()),
    Column('updated_at', DateTime()),
)

deskusergroupploy = Table(
    'deskusergroup_ploy', meta,
    Column('id', Integer, primary_key=True),
    Column('deskusergroup_id', Integer),
    Column('name', String(64)),
    Column('key', String(32)),
    Column('value', String(255)),
    Column('button_type', String(32)),
    Column('create_at', DateTime()),
    Column('updated_at', DateTime()),
)


deivcegroupploy = Table(
    'devicegroup_ploy', meta,
    Column('id', Integer, primary_key=True),
    Column('devicegroup_id', Integer),
    Column('name', String(64)),
    Column('key', String(32)),
    Column('value', String(255)),
    Column('button_type', String(32)),
    Column('create_at', DateTime()),
    Column('updated_at', DateTime()),
)


devicegroup = Table(
    'device_group', meta,
    Column('id', Integer, primary_key=True),
    Column('name', String(64)),
    Column('pid', Integer),
    Column('user_id', Integer),
    Column('pp', String(128)),
)

device_devicegroup_r = Table(
    "device_devicegroup_r", meta,
    Column("device_id", Integer),
    Column("devicegroup_id",Integer),
    PrimaryKeyConstraint("device_id", "devicegroup_id")
)

device_usergroup_r = Table(
    "device_usergroup_r", meta,
    Column("device_id", Integer),
    Column("deskusergroup_id",Integer),
    PrimaryKeyConstraint("device_id", "deskusergroup_id")
)

device_deskuser_r = Table(
    "device_deskuser_r", meta,
    Column("device_id", Integer),
    Column("deskuser_id",Integer),
    PrimaryKeyConstraint("device_id", "deskuser_id")
)


device = Table(
    'device', meta,
    Column('id', Integer, primary_key=True),
    Column('name', String(64)),
    Column('device_info', String(255)),
    Column('ip_addr', String(64)),
    Column('serial_number', String(64)),
    Column('client_version', String(64)),
    Column('mac_addr', String(32)),
    Column('status', String(32)),
    Column('create_at', DateTime()),
    Column('connect_at', DateTime()),
    Column('disconnect_at', DateTime()),
    Column('updated_at', DateTime()),
    Column('expired_at', DateTime()), 
)

device_whitelist = Table(
    'device_whitelist', meta,
    Column('id', Integer, primary_key=True),
    Column('device_name', Integer),
    Column('device_info', Integer),
    Column('create_at', DateTime()),
)


device_blacklist = Table(
    'device_blacklist', meta,
    Column('id', Integer, primary_key=True),
    Column('device_name', Integer),
    Column('device_info', Integer),
    Column('create_at', DateTime()),
)

deskuser_blacklist_r = Table(
    "deskuser_blacklist_r", meta,
    Column("deskuser_id", Integer),
    Column("blacklist_id",Integer),
    PrimaryKeyConstraint("deskuser_id", "blacklist_id")
)

deskusergroup_blacklist_r = Table(
    "deskusergroup_blacklist_r", meta,
    Column("deskusergroup_id", Integer),
    Column("blacklist_id",Integer),
    PrimaryKeyConstraint("deskusergroup_id", "blacklist_id")
)

deskuser_whitelist_r = Table(
    "deskuser_whitelist_r", meta,
    Column("deskuser_id", Integer),
    Column("whitelist_id",Integer),
    PrimaryKeyConstraint("deskuser_id", "whitelist_id")
)


deskusergroup_whitelist_r = Table(
    "deskusergroup_whitelist_r", meta,
    Column("deskusergroup_id", Integer),
    Column("whitelist_id",Integer),
    PrimaryKeyConstraint("deskusergroup_id", "whitelist_id")
)



def upgrade(migrate_engine):
    meta.bind = migrate_engine
    defaultploy.create()
    deskusergroup.create()
    deskuser_deskusergroup_r.create()
    deskuserploy.create()
    deskploy.create()
    deviceploy.create()
    deskpoolploy.create()
    deskusergroupploy.create()
    devicegroup.create()
    device_devicegroup_r.create()
    device_usergroup_r.create()
    device_deskuser_r.create()
    device.create()
    device_whitelist.create()
    deivcegroupploy.create()
    device_blacklist.create()
    deskuser_blacklist_r.create()
    deskusergroup_blacklist_r.create()
    deskuser_whitelist_r.create()
    deskusergroup_whitelist_r.create()
    
    
    
def downgrade(migrate_engine):
    meta.bind = migrate_engine
    defaultploy.drop()
    deskusergroup.drop()
    deskuser_deskusergroup_r.drop()
    deskuserploy.drop()
    deskploy.drop()
    deviceploy.drop()
    deskpoolploy.drop()
    deskusergroupploy.drop()
    devicegroup.drop()
    device_devicegroup_r.drop()
    device_usergroup_r.drop()
    device_deskuser_r.drop()
    device.drop()
    device_whitelist.drop()
    deivcegroupploy.drop()
    device_blacklist.drop()
    deskuser_blacklist_r.drop()
    deskusergroup_blacklist_r.drop()
    deskuser_whitelist_r.drop()
    deskusergroup_whitelist_r.drop()
    

