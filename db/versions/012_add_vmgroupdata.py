from sqlalchemy import Table, Column, Integer, String, MetaData

meta = MetaData()

# 定义 'vmgroup' 表结构
vmgroup = Table(
    'vmgroup', meta,
    Column('id', Integer, primary_key=True),
    <PERSON><PERSON><PERSON>('name', String(32)),
    <PERSON><PERSON><PERSON>('pid', Integer),
    <PERSON><PERSON><PERSON>('user_id', Integer),
    <PERSON><PERSON><PERSON>('pp', String(128)),
)

def upgrade(migrate_engine):
    meta.bind = migrate_engine

    # 插入数据到 'vmgroup' 表
    migrate_engine.execute(vmgroup.insert(), [
        {'id': 1, 'name': '默认', 'parent_id': -1, 'is_active': 1, 'extra_info': '-1'}
    ])

def downgrade(migrate_engine):
    meta.bind = migrate_engine
    
    # 删除 'vmgroup' 表中指定的记录
    migrate_engine.execute(vmgroup.delete().where(vmgroup.c.id == 1))