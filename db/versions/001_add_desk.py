from sqlalchemy import *
from migrate import *


from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime

meta = MetaData()

deskpool = Table(
    'deskpool', meta,
    Column('id', Integer, primary_key=True),
    <PERSON>umn('desksource', String(32)),
    <PERSON>umn('name', String(32)),
    <PERSON>umn('count', String(32)),
    <PERSON>umn('granttype', String(32)),
    Column('deskpoolclass', String(32)),
    <PERSON>umn('sharetype', String(32)),
    Column('create_at', DateTime()),
)


deskpool_group = Table(
    'deskpool_group', meta,
    Column('id', Integer, primary_key=True),
    Column('name', String(32)),
    <PERSON>umn('pid', Integer),
    <PERSON>umn('user_id', Integer),
    Column('pp', String(128)),
)


deskpool_deskpoolgroup_r = Table(
    "deskpool_deskpoolgroup_r", meta,
    Column("deskpool_id",Integer),
    Column("deskpool_group_id", Integer),
    PrimaryKeyConstraint("deskpool_id", "deskpool_group_id")
)



deskuser = Table(
    'deskuser', meta,
    Column('id', Integer, primary_key=True),
    Column('username', String(32)),
    Column('name', String(32)),
    Column('password', String(64)),
    Column('domain', String(64)),
    Column('organize', String(64)),
    Column('email', String(64)),
    Column('phone', String(64)),
    Column('session_id', String(64)),
    Column('user_source_id', Integer),
    Column('status', String(16)),
    Column('create_at', DateTime()),
)



acgroup = Table(
    'acgroup', meta,
    Column('id', Integer, primary_key=True),
    Column('usb', String(16)),
    Column('clipboard', String(16)),
    Column('deskpool_id', Integer),
)

whitelist = Table(
    'whitelist', meta,
    Column('id', Integer, primary_key=True),
    Column('device_id', String(64)),
)

usersource = Table(
    'usersource', meta,
    Column('id', Integer, primary_key=True),
    Column('ldap_server', String(64)),
    Column('autosync', String(64)),
    Column('domain', String(64)),
    Column('user', String(64)),
    Column('psw', String(64)),
)

#分配策略
desk_grantpolicy_r = Table(
    "desk_grantpolicy", meta,
    Column("desk_id", Integer),
    Column("grantpolicy_id",Integer),
)


grant = Table(
    'deskpool_grant_policy', meta,
    Column('id', Integer, primary_key=True),
    Column('deskpool_id', Integer),
    Column('domain', String(64)),
    Column('dnpath', String(64)),
    Column('username', String(64)),
    Column('name', String(64)),
    Column('objectguid', String(64)),
    Column('objecttype', String(64)),
)

desk = Table(
    'desk', meta,
    Column('id', Integer, primary_key=True),
    Column('vm_id', String(64)),
    Column('vm_name', String(64)),
    Column('objectguid', String(64)),
    Column('domain', String(64)),
    Column('username', String(64)),
    Column('dnpath', String(64)),
    Column('name', String(64)),
    Column('deskclass', String(64)),
    Column('status', String(64)),
    Column('enabled', String(16)),
    Column('single_grant', String(16)),
    Column('create_at',  DateTime()),
    Column('deskpool_id',  Integer),
)

usersession = Table(
    'usersession', meta,
    Column('id', Integer, primary_key=True),
    Column('session_id', String(64)),
    Column('username', String(64)),
    Column('create_at',  DateTime()),
)



def upgrade(migrate_engine):
    meta.bind = migrate_engine
    deskpool.create()
    deskpool_group.create()
    deskpool_deskpoolgroup_r.create()
    deskuser.create()
    desk_grantpolicy_r.create()
    usersource.create()
    grant.create()
    desk.create()
    acgroup.create()
    whitelist.create()
    usersession.create()


def downgrade(migrate_engine):
    meta.bind = migrate_engine
    deskpool.drop()
    deskpool_group.drop()
    deskpool_deskpoolgroup_r.drop()
    deskuser.drop()
    desk_grantpolicy_r.drop()
    usersource.drop()
    grant.drop()
    desk.drop()
    acgroup.drop()
    whitelist.drop()
    usersession.drop()

