from sqlalchemy import *
from migrate import *


from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime

meta = MetaData()

task = Table(
    'tasks', meta,
    Column('id', Integer, primary_key=True),
    <PERSON>um<PERSON>('method', String(32)),
    <PERSON><PERSON><PERSON>('param', Integer),
    <PERSON><PERSON><PERSON>('complete', String(16)),
    <PERSON><PERSON><PERSON>('status', String(16)),
    <PERSON>um<PERSON>('scheduled', DateTime()),
    <PERSON>umn('create_at', DateTime())
)

ins = Table(
    'instance_create_tasks', meta,
    Column('id', Integer, primary_key=True),
    <PERSON>umn('name', String(32)),
    <PERSON><PERSON><PERSON>('imageRef', String(64)),
    <PERSON><PERSON><PERSON>('flavorRef', String(64)),
    <PERSON><PERSON><PERSON>('networkRef', String(64)),
    <PERSON><PERSON><PERSON>('volume_id', String(64)),
    <PERSON><PERSON><PERSON>('user_id', Integer),
    <PERSON><PERSON><PERSON>('status', String(16)),
    <PERSON>umn('create_at', DateTime())
)

def upgrade(migrate_engine):
    meta.bind = migrate_engine
    task.create()
    ins.create()

def downgrade(migrate_engine):
    meta.bind = migrate_engine
    task.drop()
    ins.drop()