from sqlalchemy import *
from migrate import *

from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime

meta = MetaData()

cloudy_master = Table(
    'cloudy_master', meta,
    Column('id', Integer, primary_key=True),
    Column('cloudy_id', String(64)),
    <PERSON>umn('cloudy_ip', String(64)),
    <PERSON>umn('cloudy_domain', String(64)),
    <PERSON>umn('cloudy_port', String(64)),
    <PERSON>umn('create_at', DateTime()),
)




def upgrade(migrate_engine):
    meta.bind = migrate_engine
    cloudy_master.create()


def downgrade(migrate_engine):
    meta.bind = migrate_engine
    cloudy_master.drop()

