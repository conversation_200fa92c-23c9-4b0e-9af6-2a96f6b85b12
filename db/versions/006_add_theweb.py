from sqlalchemy import *
from migrate import *

from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime

meta = MetaData()

network = Table(
    'network', meta,
    Column('id', Integer, primary_key=True),
    <PERSON>umn('name', String(32)),
    <PERSON>umn('cidr', String(32)),
    <PERSON>umn('vlanid', String(8)),
    <PERSON>um<PERSON>('bridge', String(16)),
    <PERSON>umn('bonding', String(16)),
    <PERSON>umn('devinfo', String(128)),
    <PERSON>umn('speed', String(128)),
    Column('type', String(8)),
    
)

event = Table(
    'events', meta,
    Column('id', Integer, primary_key=True),
    <PERSON>umn('username', String(32)),
    <PERSON>umn('name', String(32)),
    <PERSON>umn('op', String(32)),
    <PERSON>umn('level', String(16)),
    Column('desc', String(128)),
    Column('create_at', DateTime()),
)



account = Table(
    'users', meta,
    Column('id', Integer, primary_key=True),
    <PERSON>umn('username', String(32), unique=True),
    <PERSON>um<PERSON>('name', String(32)),
    <PERSON>umn('role_name', String(32)),
    Column('password', String(128)),
    Column('session_id', String(128)),
    Column('create_at', DateTime()),
    Column('updated_at', DateTime()),
    Column('status', String(16)),
    Column('expiredday', DateTime()), 
)

role = Table(
    'role', meta,
    Column('id', Integer, primary_key=True),
    Column('name', String(32), unique=True),
    Column('display_name', String(32), unique=True),
)



def upgrade(migrate_engine):
    meta.bind = migrate_engine
    network.create()
    account.create()
    event.create()
    role.create()


def downgrade(migrate_engine):
    meta.bind = migrate_engine
    network.drop()
    account.drop()
    event.drop()
    role.drop()

