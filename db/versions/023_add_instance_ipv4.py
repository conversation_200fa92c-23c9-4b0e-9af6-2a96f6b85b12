from migrate import *
from sqlalchemy import Table, MetaData, String, Column

from sqlalchemy import Table, Column, Integer, String, MetaData, DateTime

meta = MetaData()

def upgrade(migrate_engine):
    meta = MetaData(bind=migrate_engine)
    task = Table('instance_create_tasks', meta, autoload=True)
    ipv4 = Column('ipv4', String(64), server_default="")
    ipv4.create(task)

def downgrade(migrate_engine):
    meta = MetaData(bind=migrate_engine)
    task = Table('instance_create_tasks', meta, autoload=True)
    task.c.ipv4.drop()