sudo docker build  -t tianwen1:5000/theasyn:v1 .

#ubuntu下 引用theapi thedb
export PYTHONPATH=/maojj/project/theapi:/maojj/project/thedb

#启动celery 定时任务
celery -A asynservices.tasks beat
#启动celery worker
celery -A asynservices.tasks worker --loglevel=INFO

#celery 监控
celery -A asynservices.tasks flower --address=0.0.0.0 --port=5555

http://*************:5555/

#启动云桌面 定时任务
source /maojj/myvenv/bin/activate
export PYTHONPATH=/maojj/project/theapi:/maojj/project/thedb
celery -A asynservices.cron beat
#启动云桌面 worker
source /maojj/myvenv/bin/activate
export PYTHONPATH=/maojj/project/theapi:/maojj/project/thedb
celery -A asynservices.cron worker --loglevel=INFO



测试提交
