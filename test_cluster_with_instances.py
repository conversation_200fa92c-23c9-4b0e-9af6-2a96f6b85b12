#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试主机聚合更新接口 - 处理有实例运行的情况
"""

import requests
import json

# 配置
BASE_URL = "http://localhost:8080"  # 根据实际情况修改
HEADERS = {
    "Content-Type": "application/json",
    "Accept": "application/json"
}

def test_update_with_running_instances():
    """测试在有运行实例的情况下更新集群"""
    print("=== 测试有运行实例时的更新 ===")
    
    url = f"{BASE_URL}/v1/update/clusters/full"
    
    # 尝试更新有实例运行的集群的可用区
    data = {
        "id": 2,  # 假设这个集群有运行的实例
        "name": "updated-cluster-name",
        "availability_zone": "new-zone",
        "metadata": {
            "environment": "production",
            "updated": "2024-01-01"
        }
    }
    
    try:
        response = requests.put(url, headers=HEADERS, data=json.dumps(data))
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get("msg") == "ok":
                print("✅ 所有更新都成功")
            elif result.get("msg") == "partial_success":
                print("⚠️ 部分更新成功")
                print(f"成功更新: {', '.join(result.get('updated', []))}")
                print(f"更新失败: {', '.join(result.get('failed', []))}")
                print(f"错误信息: {result.get('error_message', '')}")
                
                if "instance_info" in result:
                    instance_info = result["instance_info"]
                    print(f"实例信息:")
                    print(f"  - 总实例数: {instance_info.get('instance_count', 0)}")
                    print(f"  - 有实例的主机: {', '.join(instance_info.get('hosts_with_instances', []))}")
                    print(f"  - 总主机数: {instance_info.get('total_hosts', 0)}")
            else:
                print(f"❌ 更新失败: {result}")
        else:
            print(f"❌ 请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def test_update_only_name_and_metadata():
    """测试只更新名称和元数据（避免可用区冲突）"""
    print("\n=== 测试只更新名称和元数据 ===")
    
    url = f"{BASE_URL}/v1/update/clusters/full"
    data = {
        "id": 2,
        "name": "safe-update-cluster",
        "metadata": {
            "environment": "production",
            "team": "infrastructure",
            "last_update": "2024-01-01T12:00:00Z",
            "notes": "只更新名称和元数据，避免可用区冲突"
        }
    }
    
    try:
        response = requests.put(url, headers=HEADERS, data=json.dumps(data))
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("msg") == "ok":
                print("✅ 名称和元数据更新成功")
            else:
                print(f"❌ 更新失败: {result}")
        else:
            print(f"❌ 请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def check_cluster_instances(cluster_id):
    """检查集群中的实例（如果有相应的API）"""
    print(f"\n=== 检查集群 {cluster_id} 的实例信息 ===")
    
    # 这里可以调用检查实例的API（如果存在）
    # 目前只是示例
    print("注意：要更新可用区，需要确保集群中没有运行的实例")
    print("建议的解决方案：")
    print("1. 迁移所有虚拟机到其他主机")
    print("2. 或者停止所有虚拟机")
    print("3. 然后再更新可用区")
    print("4. 最后重新启动虚拟机")

def demonstrate_workaround():
    """演示解决方案"""
    print("\n=== 解决方案演示 ===")
    
    cluster_id = 2
    
    print("步骤1: 先更新名称和元数据（这些不会受实例影响）")
    test_update_only_name_and_metadata()
    
    print("\n步骤2: 如果需要更新可用区，需要先处理实例")
    print("选项A: 迁移实例到其他主机")
    print("选项B: 停止实例，更新可用区，然后重启实例")
    print("选项C: 创建新的主机聚合，迁移主机，然后删除旧聚合")
    
    print("\n步骤3: 实例处理完成后，再更新可用区")
    url = f"{BASE_URL}/v1/update/clusters/full"
    data = {
        "id": cluster_id,
        "availability_zone": "new-production-zone"
    }
    
    print(f"更新可用区的请求: {json.dumps(data, indent=2)}")
    print("注意：只有在没有实例运行时才能成功")

def main():
    """主函数"""
    print("开始测试主机聚合更新接口 - 处理实例冲突...")
    
    # 测试有实例运行时的更新
    test_update_with_running_instances()
    
    # 测试安全的更新（只更新名称和元数据）
    test_update_only_name_and_metadata()
    
    # 检查集群实例
    check_cluster_instances(2)
    
    # 演示解决方案
    demonstrate_workaround()
    
    print("\n测试完成!")
    print("\n总结:")
    print("- 名称和元数据可以随时更新")
    print("- 可用区更新需要确保没有运行的实例")
    print("- 接口会提供详细的错误信息和部分成功的结果")
    print("- 建议先迁移或停止实例，然后更新可用区")

if __name__ == "__main__":
    main()
