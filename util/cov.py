# -*- coding: utf-8 -*-

from sqlalchemy.orm import class_mapper
import datetime
from db.model.event import Event
from sqlalchemy.sql.operators import op
import ipaddress

def todict(obj, classkey=None):
    if isinstance(obj, dict):
        data = {}
        for (k, v) in obj.items():
            data[k] = todict(v, classkey)
        return data
    elif hasattr(obj, "_ast"):
        return todict(obj._ast())
    elif hasattr(obj, "__iter__"):
        return [todict(v, classkey) for v in obj]
    elif hasattr(obj, "__dict__"):
        data = dict([(key, todict(value, classkey))
                     for key, value in obj.__dict__.iteritems()
                     if not callable(value) and not key.startswith('_') and key not in ['name']])
        if classkey is not None and hasattr(obj, "__class__"):
            data[classkey] = obj.__class__.__name__
        return data
    else:
        return obj
    
def serialize(model):
    """Transforms a model into a dictionary which can be dumped to JSON."""
    # first we get the names of all the columns on your model
    columns = [c.key for c in class_mapper(model.__class__).columns]
    # then we return their values in a dict
    res = {}
    for c in columns:
        if type(getattr(model, c)) == datetime.datetime:
            res[c] = getattr(model, c).strftime("%m/%d/%Y, %H:%M:%S")
        else:
            res[c] = getattr(model, c)
    return res
    #return dict((c, getattr(model, c)) for c in columns)

def theevent(level, op, desc):
    def outer(func):
        def inner(*args,**kwargs):
            r = func(*args,**kwargs)
            handler = args[0]
            username = handler.get_cookie('username', None)
            if username:
                event = Event()
                event.username = username
                event.level = level
                event.op = op
                event.desc = desc
                with handler.session_scope() as session:
                    session.add(event)
                    session.commit()
            return r
        return inner
    return outer

def is_valid_ip(ip_str):
    try:
        ipaddress.ip_address(ip_str)
        return True
    except:
        return False
