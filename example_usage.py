#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
主机聚合更新接口使用示例
"""

import requests
import json

# 配置
API_BASE_URL = "http://your-server:8080"
HEADERS = {
    "Content-Type": "application/json",
    "Accept": "application/json"
}

def update_cluster_name_only(cluster_id, new_name):
    """
    只更新集群名称（使用原有接口）
    """
    url = f"{API_BASE_URL}/v1/update/clusters/name"
    data = {
        "id": cluster_id,
        "name": new_name
    }
    
    response = requests.put(url, headers=HEADERS, data=json.dumps(data))
    return response.json()

def update_cluster_full(cluster_id, name=None, availability_zone=None, metadata=None):
    """
    更新集群完整信息（使用新接口）
    """
    url = f"{API_BASE_URL}/v1/update/clusters/full"
    data = {"id": cluster_id}
    
    if name:
        data["name"] = name
    if availability_zone:
        data["availability_zone"] = availability_zone
    if metadata:
        data["metadata"] = metadata
    
    response = requests.put(url, headers=HEADERS, data=json.dumps(data))
    return response.json()

# 使用示例
if __name__ == "__main__":
    cluster_id = 1
    
    # 示例1: 只更新名称
    print("示例1: 只更新集群名称")
    result = update_cluster_name_only(cluster_id, "production-cluster")
    print(f"结果: {result}")
    
    # 示例2: 更新名称和可用区
    print("\n示例2: 更新名称和可用区")
    result = update_cluster_full(
        cluster_id=cluster_id,
        name="production-cluster-v2",
        availability_zone="zone-east-1"
    )
    print(f"结果: {result}")
    
    # 示例3: 只更新元数据
    print("\n示例3: 只更新元数据")
    metadata = {
        "environment": "production",
        "team": "infrastructure",
        "cost_center": "IT-001",
        "backup_policy": "daily",
        "monitoring": "enabled"
    }
    result = update_cluster_full(
        cluster_id=cluster_id,
        metadata=metadata
    )
    print(f"结果: {result}")
    
    # 示例4: 同时更新所有字段
    print("\n示例4: 同时更新所有字段")
    result = update_cluster_full(
        cluster_id=cluster_id,
        name="production-cluster-final",
        availability_zone="zone-east-1a",
        metadata={
            "environment": "production",
            "version": "3.0",
            "owner": "<EMAIL>",
            "created_by": "automation",
            "last_updated": "2024-01-01T00:00:00Z"
        }
    )
    print(f"结果: {result}")
