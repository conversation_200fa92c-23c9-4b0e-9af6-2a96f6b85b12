#!/bin/bash

#启动云平台 定时任务
function run_the_beat() {
	source /maojj/myvenv/bin/activate
	export PYTHONPATH=/maojj/project/theapi:/maojj/project/thedb
	celery -A asynservices.tasks beat
}

#启动云平台 worker
function run_the_work() {
	source /maojj/myvenv/bin/activate
	export PYTHONPATH=/maojj/project/theapi:/maojj/project/thedb
	celery -A asynservices.tasks worker --loglevel=INFO
}

#启动云桌面 定时任务
function run_desk_beat() {
	source /maojj/myvenv/bin/activate
	export PYTHONPATH=/maojj/project/theapi:/maojj/project/thedb
	celery -A asynservices.cron beat
}


#启动云桌面 定时任务
function run_desk_work() {
	source /maojj/myvenv/bin/activate
	export PYTHONPATH=/maojj/project/theapi:/maojj/project/thedb
	celery -A asynservices.cron beat
}


# 根据参数运行不同的函数
if [ "$1" == "beat" ]; then
  run_the_beat
elif [ "$1" == "worker" ]; then
  run_the_work
elif [ "$1" == "deskbeat" ]; then
  run_desk_beat
elif [ "$1" == "deskworker" ]; then
  run_desk_work 
else
  echo "Invalid argument"
fi







