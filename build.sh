
version=`git  log -n 1 | grep commit | awk '{ print $2 }' | cut -c 1-10`
argtag=`git describe --abbrev=1 --tags`
argdate=`git show -s --date=format:'%Y-%m-%d-%H:%M:%S' --format=%cd`
docker build -t tianwen1:5000/theasyn:$argtag --build-arg ARG_VERSION=$version --build-arg ARG_TAG=$argtag  --build-arg ARG_COMMIT_DATE=$argdate .
docker push tianwen1:5000/theasyn:$argtag
docker tag tianwen1:5000/theasyn:$argtag tianwen1:5000/theasyn:latest
docker push tianwen1:5000/theasyn:latest
