import socket

import pyrestful.rest
from pyrestful import mediatypes
from pyrestful.rest import get, post

class Ping<PERSON>andler(pyrestful.rest.RestHandler):
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, GET, DELETE, PUT, OPTIONS')

    @get(_path="/v1/ping", _produces=mediatypes.APPLICATION_JSON)
    def ping_ip_port(self):
        proto   = self.get_query_argument("proto", default=None)
        addr    = self.get_query_argument("ip", default=None)
        port    = self.get_query_argument("port", default=None)
        timeout = self.get_query_argument("timeout", default=None)

        if not proto in ["http", "tcp"]:
            return {"msg": "错误协议: %s" % proto}

        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
            if timeout:
                sock.settimeout(timeout)
            else:
                sock.settimeout(5)

            try:
                sock.connect((addr, int(port)))
                return {"msg": "检测成功"}
            except (socket.timeout, socket.error):
                return {"msg": "检测失败"}
