# -*- coding: utf-8 -*-
import copy
import threading

import tornado.ioloop
import pyrestful.rest
import time
import traceback

from api.log.log import CustomLogger
from db.model.vm_drs import VmDrs
from db.model.vm_whitelist import Vm<PERSON>hitelist
from pyrestful import mediatypes
from pyrestful.rest import get, post, put, delete

from model.instances import *
from model.volumes import VolumeCreateFrom
from model.flavors import FlavorCreateFrom

from util.cov import todict
from api.openstack.client import Client
from mydoc.instances import *
from Page.Pagenation import  Pagenation
from db.model.task import InstanceCreateTask, ExVolumeCreateTask
from db.model.vm import VmGroup , Vms
from db.model.user import User
from model.util import Role
from db.model.vm import VmGroup , Vms,Volumes
from sqlalchemy import and_
from api.model.instances import InstanceCreateFrom,Ports, InstanceCreateFromV2
from model.volumes import  VolumeActionFrom
from dacite import from_dict
from api.thesc.client import Client as SClient
from api.model.instances import  InstanceIS<PERSON>reate<PERSON>rom

from asynservices.tasks import create_volume_for_fromsnapshot_vm


import logging
from sqlalchemy_pagination import Page
logger = logging.getLogger(__name__)
new_logger = CustomLogger()
class InstancesHandler(pyrestful.rest.RestHandler):

    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        #self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, GET, DELETE, PUT, OPTIONS')
        #self.set_header("Access-Control-Allow-Headers", "access-control-allow-origin,authorization,content-type")

    @post(_path="/v1/instances", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_all_instances(self, form):
        """
        ---
        tags:
          - 虚拟机相关接口
        summary: 查询虚拟机列表
        requestBody:
          description: 查询条件
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeskListFormModel'
          required: true
        responses:
          '200':
            description: 成功
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ArrayDeskModel'
        """

        role = self.get_cookie("role")
        page = form.get("page")
        group_id = form.get("id")
        pagecount = form.get("pagecount")
        order_by = form.get("order_by", "")
        order_type = form.get("order_type", "desc")
        search_str = form.get("search_str", "")

        data = []
        vms = []
        client = Client()

        if role == Role.sysadm:
            vms = self.sysadm_query_vms(form)

        if role == Role.operator:
            vms = self.operator_query_vms(form)

        for vmid in vms:
            ins_detail = client.NovaClient.openstack_get_server_detail(client, vmid)
            if ins_detail["volumeid"]:
                volume_detail = client.VolumeClient.openstack_get_volume_detail(client, ins_detail["volumeid"])
                ins_detail["imagename"] = volume_detail["volume_image_metadata"].get("image_name", "")
            if ins_detail["flavorid"]:
                flavor_detail = client.FlavorClient.openstack_get_flavor_detail(client, ins_detail["flavorid"])
                ins_detail["vcpus"] = flavor_detail["vcpus"]
                ins_detail["ram"] = int(flavor_detail["ram"] / 1024)

            if search_str:
                if search_str in ins_detail["name"]:
                    data.append(ins_detail)
            else:
                data.append(ins_detail)

        count = len(data)
        pages = count // pagecount + 1
        if order_type == "desc" and order_by:
            #data = sorted(data, key = lambda x:-x[order_by])
            data = sorted(data, key = lambda x:x[order_by], reverse=True)
        if order_type == "asc" and order_by:
            data = sorted(data, key = lambda x:x[order_by])

        r = {
            "total": count,
            "pages": pages,
            "data": data
            }
        return r


    @get(_path="/v1/instances/dayu/all", _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_all_instances_for_dayu(self):

        client = Client()
        ins_detail = client.NovaClient.openstack_get_all_instance_detail_dayu(client)
        return ins_detail


    @put(_path="/v1/instance/update/name",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_update_instance_name(self, form):
        vmid = form["vmid"]
        name = form["name"]
        role = self.get_cookie('role', "")
        client = Client()

        try:
            ins_detail = client.NovaClient.openstack_update_server_name(client, vmid, name)
            new_logger.log(
                self.username, "虚机操作", "重命名虚拟机", "成功", role, "{}:{},成功".format("重命名虚拟机", name)
            )
        except Exception as e:
            new_logger.log(
                self.username, "虚机操作", "重命名虚拟机", "失败", role, "{}:{},失败".format("重命名虚拟机", name)
            )
        return ins_detail

    @post(_path="/v1/instances/dayu/setconfig",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_server_dayu_setconfig(self, form):



        c = SClient()
        run = c.post_instance_dayu_setconfig(form)


        client = Client()
        vmid = form["vmid"]
        enable = form["enable"]
        if enable == "true":
            tag = "enable_auto_migrate"
        elif enable == "false":
            tag = "disable_auto_migrate"
        deleteall = client.NovaClient.openstack_delete_server_all_tag(client,vmid)
        setvm = client.NovaClient.openstack_add_server_tag(client,vmid,tag)



        return setvm



    @get(_path="/v1/instances/hosts",  _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_all_instances_hosts(self):
        """
        ---
        tags:
          - 虚拟机相关接口
        summary: 查询宿主机列表
        responses:
          '200':
            description: 成功
            content: {}
        """
        client = Client()
        host = client.HostClient.openstack_get_all_list(client)
        return host

    @post(_path="/v1/instances/detail",_types=[InstancesDetailFrom],_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_all_instances_detail(self,form):
        """
        ---
        tags:
          - 虚拟机相关接口
        summary: 查询虚拟机列表
        requestBody:
          description: 查询条件
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InstanceListFormModel'
          required: true
        responses:
          '200':
            description: 成功
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ArrayInstanceDetailModel'
        """
        client = Client()
        ins_detail = client.NovaClient.openstack_get_all_instance_detail(client)
        obj = Pagenation(ins_detail,form.page,form.pagecount)
        ins_list = obj.show()

        r = {
            "total": len(ins_detail),
            "pages": obj.total(),
            "data": ins_list
            }

        return r

    @post(_path="/v1/recycle/list",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_recycle_instances_list(self,form):
        """
        ---
        tags:
          - 虚拟机相关接口
        summary: 查询虚拟机列表
        requestBody:
          description: 查询条件
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InstanceListFormModel'
          required: true
        responses:
          '200':
            description: 成功
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ArrayInstanceDetailModel'
        """
        client = Client()
        search = form.get("search_str")
        page = form.get("page")
        pagecount = form.get("pagecount")
        ins_detail = client.NovaClient.openstack_get_softdeleted_instance_detail(client,search)
        obj = Pagenation(ins_detail,page,pagecount)
        ins_list = obj.show()

        r = {
            "total": len(ins_detail),
            "pages": obj.total(),
            "data": ins_list
            }

        return r

    @post(_path="/v1/recycle/with/user/list",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_recycle_instances_list_with_user(self,form):
        """
        ---
        tags:
          - 虚拟机相关接口
        summary: 查询虚拟机列表
        requestBody:
          description: 查询条件
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InstanceListFormModel'
          required: true
        responses:
          '200':
            description: 成功
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ArrayInstanceDetailModel'
        """
        client = Client()
        search = form.get("search_str")
        page = form.get("page")
        pagecount = form.get("pagecount")
        ins_detail = client.NovaClient.openstack_get_softdeleted_instance_detail(client,search)
        obj = Pagenation(ins_detail,page,pagecount)
        ins_list = obj.show()

        r = {
            "total": len(ins_detail),
            "pages": obj.total(),
            "data": ins_list
            }

        return r

    def get_vmport(self, client, ipv4):
        port_list= client.NeutronClient.openstack_get_all_ports(client)
        for port  in port_list:
            port_detail = from_dict(data_class=Ports, data=port)
            if port_detail.__dict__["fixed_ips"][0]["ip_address"] == ipv4 :
                if port_detail.__dict__["status"] == "DOWN":
                    vmport = port_detail.__dict__["id"]
                    return vmport
        return None

    @post(_path="/v1/instance/create",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_create_instances(self, instancecreatefrom):
        """
        ---
        tags:
          - 虚拟机相关接口
        summary: 创建虚拟机
        requestBody:
          description: 创建
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RequestCreateInstanceModel'
          required: true
        responses:
          '200':
            description: 创建成功
            content: {}
        """
        with self.application.session_scope() as session:
            username = self.get_cookie("username", "")
            user = session.query(User).filter(User.username == username).first()
            client = Client()
            ipv4s = instancecreatefrom["ipv4"]
            ipv4 = ""
            if instancecreatefrom["availability_zone"] == "" :
                clusters = client.HypervisorsClient.openstack_get_all_aggregates_list(client)
                if len(clusters) > 0 :
                    instancecreatefrom["availability_zone"] = clusters[0]["name"]
                else:
                    instancecreatefrom["availability_zone"] = "nova"
            try:
                flavor_detail = client.FlavorClient.openstack_get_flavor_detail(client, instancecreatefrom["flavorRef"])

                create_form = VolumeCreateFrom()
                create_form.name = instancecreatefrom["name"]
                create_form.size = flavor_detail["disk"]
                create_form.imageRef = instancecreatefrom["imageRef"]
                create_form.description = ""


                if not ipv4s:
                    create_vm_count = instancecreatefrom["count"]
                else:
                    ipv4_list = ipv4s.split(",")
                    create_vm_count = len(ipv4_list)

                for i in range(create_vm_count):
                    if not ipv4s:
                        vmport = ""
                    else:
                        ipv4 = ipv4_list[i]

                    if create_vm_count > 1:
                        create_form.name = "%s_%s" % (instancecreatefrom["name"],str(i+1))
                    elif create_vm_count == 1:
                        create_form.name = instancecreatefrom["name"]
                    volume = client.VolumeClient.openstack_create_volume(client,create_form)
                    instancecreatefrom["uuid"] = volume["id"]


                    vmport = self.get_vmport(client, ipv4)

                    if not vmport:
                        subnet_id = client.NeutronClient.openstack_get_subnet_from_network(client,instancecreatefrom["networkRef"] )
                        vmport = client.NeutronClient.openstack_create_port(client,instancecreatefrom["networkRef"],ipv4,subnet_id)

                    task = InstanceCreateTask()
                    task.name  = create_form.name
                    task.flavorRef  = instancecreatefrom["flavorRef"]
                    task.imageRef  = instancecreatefrom["imageRef"]
                    task.volume_id  = instancecreatefrom["uuid"]
                    task.iso  = "镜像"
                    task.availability_zone = instancecreatefrom["availability_zone"]
                    task.user_id = user.id
                    task.networkRef  = instancecreatefrom["networkRef"]
                    task.ipv4 = vmport
                    task.os_type = instancecreatefrom["os_type"]

                    session.add(task)
                session.commit()

                logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                                {"username": self.get_cookie('username', ""),
                                 "op": "创建虚拟机",
                                 "object": create_form.name,
                                 "role": self.get_cookie('role', ""),
                                 "result": "成功",
                                 })
            except Exception as e:
                    traceback.print_exc()
                    logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                                {"username": self.get_cookie('username', ""),
                                 "op": "创建虚拟机",
                                 "object": create_form.name,
                                 "role": self.get_cookie('role', ""),
                                 "result": "失败",
                                 })
                    return {"msg":"error"}
        return {"msg":"ok"}


    @post(_path="/v2/instance/create",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_create_instances_v2(self, instancecreatefrom):
        role = self.get_cookie('role', "")

        with self.application.session_scope() as session:
            username = self.get_cookie("username", "")
            user = session.query(User).filter(User.username == username).first()
            client = Client()
            ipv4s = instancecreatefrom["ipv4"]
            ipv4 = ""
            os_type = instancecreatefrom["os_type"]
            if instancecreatefrom["availability_zone"] == "" :
                clusters = client.HypervisorsClient.openstack_get_all_aggregates_list(client)
                if len(clusters) > 0 :
                    instancecreatefrom["availability_zone"] = clusters[0]["name"]
                else:
                    instancecreatefrom["availability_zone"] = "nova"
            try:
                flavor_detail = client.FlavorClient.openstack_get_flavor_detail(client, instancecreatefrom["flavorRef"])

                create_form = VolumeCreateFrom()
                create_form.name = instancecreatefrom["name"]
                create_form.size = flavor_detail["disk"]
                create_form.imageRef = instancecreatefrom["imageRef"]
                create_form.description = ""


                if not ipv4s:
                    create_vm_count = instancecreatefrom["count"]
                else:
                    ipv4_list = ipv4s.split(",")
                    create_vm_count = len(ipv4_list)

                for i in range(create_vm_count):
                    if not ipv4s:
                        vmport = ""
                    else:
                        ipv4 = ipv4_list[i]

                    if create_vm_count > 1:
                        create_form.name = "%s_%s" % (instancecreatefrom["name"],str(i+1))
                    elif create_vm_count == 1:
                        create_form.name = instancecreatefrom["name"]
                    volume = client.VolumeClient.openstack_create_volume(client,create_form)
                    instancecreatefrom["uuid"] = volume["id"]


                    vmport = self.get_vmport(client, ipv4)

                    if not vmport:
                        subnet_id = client.NeutronClient.openstack_get_subnet_from_network(client,instancecreatefrom["networkRef"] )
                        vmport = client.NeutronClient.openstack_create_port(client,instancecreatefrom["networkRef"],ipv4,subnet_id)

                    task = InstanceCreateTask()
                    task.name  = create_form.name
                    task.flavorRef  = instancecreatefrom["flavorRef"]
                    task.imageRef  = instancecreatefrom["imageRef"]
                    task.volume_id  = instancecreatefrom["uuid"]
                    task.iso  = "镜像"
                    task.availability_zone = instancecreatefrom["availability_zone"]
                    task.user_id = user.id
                    task.os_type = os_type
                    task.networkRef  = instancecreatefrom["networkRef"]
                    task.ipv4 = vmport

                    session.add(task)
                    session.commit()

                    has_ex_volume = instancecreatefrom.get("ex_volume_size", "")
                    if has_ex_volume:
                        exv = VolumeCreateFrom()
                        exv.name = "%s_%s_数据盘" % (instancecreatefrom["name"], str(i+1))
                        exv.size = instancecreatefrom["ex_volume_size"]
                        exv.description = "%s_%s_数据盘" % (instancecreatefrom["name"], str(i+1))

                        res = client.VolumeClient.openstack_create_blank_volume(client,exv)
                        client.VolumeClient.openstack_update_volume_bootable(client, res["id"])

                        ex_task = ExVolumeCreateTask()
                        ex_task.volume_id = res["id"]
                        ex_task.size = instancecreatefrom["ex_volume_size"]
                        ex_task.create_task_id = task.id

                        session.add(ex_task)


                logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                                {"username": self.get_cookie('username', ""),
                                 "op": "创建虚拟机",
                                 "object": create_form.name,
                                 "role": self.get_cookie('role', ""),
                                 "result": "成功",
                                 })
                # new_logger.log(
                #     "虚机操作", "创建虚拟机", "成功", role, "{} 创建云主机: {},成功".format(role, create_form.name)
                # )
            except Exception as e:
                    traceback.print_exc()
                    logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                                {"username": self.get_cookie('username', ""),
                                 "op": "创建虚拟机",
                                 "object": create_form.name,
                                 "role": self.get_cookie('role', ""),
                                 "result": "失败",
                                 })
                    # new_logger.log(
                    #     "虚机操作", "创建虚拟机", "失败", role, "{} 创建云主机: {},失败".format(role, create_form.name)
                    # )
                    return {"msg":"error"}
        return {"msg":"ok"}

    @post(_path="/v3/instance/create", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_create_instances_v3(self, instancecreatefrom):
        role = self.get_cookie('role', "")
        username = self.get_cookie("username", "")

        with self.application.session_scope() as session:
            user = session.query(User).filter(User.username == username).first()
            client = Client()
            ipv4s = instancecreatefrom["ipv4"]
            ipv4 = ""
            os_type = instancecreatefrom["os_type"]
            if instancecreatefrom["availability_zone"] == "":
                clusters = client.HypervisorsClient.openstack_get_all_aggregates_list(client)
                if len(clusters) > 0:
                    instancecreatefrom["availability_zone"] = clusters[0]["name"]
                else:
                    instancecreatefrom["availability_zone"] = "nova"
            try:
                flavor_detail = client.FlavorClient.openstack_get_flavor_detail(client, instancecreatefrom["flavorRef"])

                create_form = VolumeCreateFrom()
                create_form.name = instancecreatefrom["name"]
                create_form.size = flavor_detail["disk"]
                create_form.imageRef = instancecreatefrom["imageRef"]
                create_form.description = ""

                if not ipv4s:
                    create_vm_count = instancecreatefrom["count"]
                else:
                    ipv4_list = ipv4s.split(",")
                    create_vm_count = len(ipv4_list)

                for i in range(create_vm_count):
                    if not ipv4s:
                        vmport = ""
                    else:
                        ipv4 = ipv4_list[i]

                    if create_vm_count > 1:
                        create_form.name = "%s_%s" % (instancecreatefrom["name"], str(i + 1))
                    elif create_vm_count == 1:
                        create_form.name = instancecreatefrom["name"]
                    volume = client.VolumeClient.openstack_create_volume_with_err(client, create_form)
                    if isinstance(volume,dict) and volume.get("msg", "") != "":
                        return {"msg": volume["msg"]}
                    instancecreatefrom["uuid"] = volume["id"]

                    vmport = self.get_vmport(client, ipv4)

                    if not vmport:
                        # subnet_id = client.NeutronClient.openstack_get_subnet_from_network_with_subnet(client, instancecreatefrom[
                        #     "networkRef"])
                        subnet_id = instancecreatefrom.get("subnet_id", "")
                        if ipv4:
                            vmport = client.NeutronClient.openstack_create_port(client, instancecreatefrom["networkRef"],
                                                                                ipv4, subnet_id)
                        else:
                            # 处理双子网问题
                            vmport = client.NeutronClient.openstack_create_port_withoutip(client, instancecreatefrom["networkRef"],
                                                                                          subnet_id, create_form.name)
                    task = InstanceCreateTask()
                    task.name = create_form.name
                    task.flavorRef = instancecreatefrom["flavorRef"]
                    task.imageRef = instancecreatefrom["imageRef"]
                    task.volume_id = instancecreatefrom["uuid"]
                    task.iso = "镜像"
                    task.availability_zone = instancecreatefrom["availability_zone"]
                    task.user_id = user.id
                    task.os_type = os_type
                    task.networkRef = instancecreatefrom["networkRef"]
                    task.ipv4 = vmport

                    session.add(task)
                    session.commit()

                    has_ex_volume = instancecreatefrom.get("ex_volume_size", "")
                    if has_ex_volume:
                        exv = VolumeCreateFrom()
                        exv.name = "%s_%s_数据盘" % (instancecreatefrom["name"], str(i + 1))
                        exv.size = instancecreatefrom["ex_volume_size"]
                        exv.description = "%s_%s_数据盘" % (instancecreatefrom["name"], str(i + 1))

                        res = client.VolumeClient.openstack_create_blank_volume(client, exv)
                        client.VolumeClient.openstack_update_volume_bootable(client, res["id"])

                        ex_task = ExVolumeCreateTask()
                        ex_task.volume_id = res["id"]
                        ex_task.size = instancecreatefrom["ex_volume_size"]
                        ex_task.create_task_id = task.id

                        session.add(ex_task)

                logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                            {"username": self.get_cookie('username', ""),
                             "op": "创建虚拟机",
                             "object": create_form.name,
                             "role": self.get_cookie('role', ""),
                             "result": "成功",
                             })
                # new_logger.log(
                #     "虚机操作", "创建虚拟机", "成功", role, "{} 创建云主机: {},成功".format(role, create_form.name)
                # )
            except Exception as e:
                traceback.print_exc()
                logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                             {"username": self.get_cookie('username', ""),
                              "op": "创建虚拟机",
                              "object": create_form.name,
                              "role": self.get_cookie('role', ""),
                              "result": "失败",
                              })
                # new_logger.log(
                #     "虚机操作", "创建虚拟机", "失败", role, "{} 创建云主机: {},失败".format(role, create_form.name)
                # )
                return {"msg": "error"}
        return {"msg": "ok"}

    @post(_path="/v4/instance/create", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_create_instances_v4(self, instancecreatefrom):
        role = self.get_cookie('role', "")

        with self.application.session_scope() as session:
            username = self.get_cookie("username", "")
            user = session.query(User).filter(User.username == username).first()
            client = Client()
            ipv4s = instancecreatefrom["ipv4"]
            ipv4 = ""
            os_type = instancecreatefrom["os_type"]
            if instancecreatefrom["availability_zone"] == "":
                clusters = client.HypervisorsClient.openstack_get_all_aggregates_list(client)
                if len(clusters) > 0:
                    instancecreatefrom["availability_zone"] = clusters[0]["name"]
                else:
                    instancecreatefrom["availability_zone"] = "nova"
            try:
                flavor_detail = client.FlavorClient.openstack_get_flavor_detail(client, instancecreatefrom["flavorRef"])

                create_form = VolumeCreateFrom()
                create_form.name = instancecreatefrom["name"]
                create_form.size = flavor_detail["disk"]
                create_form.imageRef = instancecreatefrom["imageRef"]
                create_form.description = ""

                if not ipv4s:
                    create_vm_count = instancecreatefrom["count"]
                else:
                    ipv4_list = ipv4s.split(",")
                    create_vm_count = len(ipv4_list)

                new_form = copy.deepcopy(instancecreatefrom)

                for i in range(create_vm_count):
                    if not ipv4s:
                        vmport = ""
                    else:
                        ipv4 = ipv4_list[i]

                    if create_vm_count > 1:
                        create_form.name = "%s_%s" % (instancecreatefrom["name"], str(i + 1))
                    elif create_vm_count == 1:
                        create_form.name = instancecreatefrom["name"]
                    volume = client.VolumeClient.openstack_create_volume(client, create_form)
                    instancecreatefrom["uuid"] = volume["id"]

                    vmport = self.get_vmport(client, ipv4)

                    if not vmport:
                        # subnet_id = client.NeutronClient.openstack_get_subnet_from_network_with_subnet(client, instancecreatefrom[
                        #     "networkRef"])
                        subnet_id = instancecreatefrom.get("subnet_id", "")
                        vmport = client.NeutronClient.openstack_create_port(client, instancecreatefrom["networkRef"],
                                                                            ipv4, subnet_id)

                    #TODO 创建一个空的云主机 并且把vm_id关联到任务表中'
                    # if vmport != "":
                    #     # instancecreatefrom["ipv4"] = vmport
                    #     new_form["ipv4"] = vmport
                    new_form["name"] = create_form.name
                    instance = client.NovaClient.openstack_fast_create_server(client, new_form)


                    task = InstanceCreateTask()
                    task.name = create_form.name
                    task.flavorRef = instancecreatefrom["flavorRef"]
                    task.imageRef = instancecreatefrom["imageRef"]
                    task.volume_id = instancecreatefrom["uuid"]
                    task.iso = "镜像"
                    task.availability_zone = instancecreatefrom["availability_zone"]
                    task.user_id = user.id
                    task.os_type = os_type
                    task.networkRef = instancecreatefrom["networkRef"]
                    task.ipv4 = vmport
                    task.vmid = instance["id"]

                    session.add(task)
                    session.commit()

                    has_ex_volume = instancecreatefrom.get("ex_volume_size", "")
                    if has_ex_volume:
                        exv = VolumeCreateFrom()
                        exv.name = "%s_%s_数据盘" % (instancecreatefrom["name"], str(i + 1))
                        exv.size = instancecreatefrom["ex_volume_size"]
                        exv.description = "%s_%s_数据盘" % (instancecreatefrom["name"], str(i + 1))

                        res = client.VolumeClient.openstack_create_blank_volume(client, exv)
                        client.VolumeClient.openstack_update_volume_bootable(client, res["id"])

                        ex_task = ExVolumeCreateTask()
                        ex_task.volume_id = res["id"]
                        ex_task.size = instancecreatefrom["ex_volume_size"]
                        ex_task.create_task_id = task.id

                        session.add(ex_task)

                logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                            {"username": self.get_cookie('username', ""),
                             "op": "创建虚拟机",
                             "object": create_form.name,
                             "role": self.get_cookie('role', ""),
                             "result": "成功",
                             })
                # new_logger.log(
                #     "虚机操作", "创建虚拟机", "成功", role, "{} 创建云主机: {},成功".format(role, create_form.name)
                # )
            except Exception as e:
                traceback.print_exc()
                logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                             {"username": self.get_cookie('username', ""),
                              "op": "创建虚拟机",
                              "object": create_form.name,
                              "role": self.get_cookie('role', ""),
                              "result": "失败",
                              })
                # new_logger.log(
                #     "虚机操作", "创建虚拟机", "失败", role, "{} 创建云主机: {},失败".format(role, create_form.name)
                # )
                return {"msg": "error"}
        return {"msg": "ok"}

    @post(_path="/v1/instance/fromvolumecreate",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_create_instance_fromvolume(self, instancecreatefrom):
        role = self.get_cookie('role', "")
        username = self.get_cookie("username", "")
        with self.session_scope() as session:
            user = session.query(User).filter(User.username == username).first()
        vmport = ""

        client = Client()
        if instancecreatefrom["availability_zone"] == "" :
            clusters = client.HypervisorsClient.openstack_get_all_aggregates_list(client)
            if len(clusters) > 0 :
                instancecreatefrom["availability_zone"] = clusters[0]["name"]
            else:
                instancecreatefrom["availability_zone"] = "nova"

        try:
            # volume 列表做出 availiby 筛选

            update_bootable = client.VolumeClient.openstack_update_volume_bootable(client, instancecreatefrom["volumeRef"])
            if update_bootable != 200:
                logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                            {"username": self.get_cookie('username', ""),
                             "op": "创建虚拟机",
                             "object": instancecreatefrom["name"],
                             "role": self.get_cookie('role', ""),
                             "result": "失败",
                             })
                # new_logger.log(
                #     "虚机操作", "创建虚拟机", "失败", role, "{} 云硬盘创建云主机: {},失败".format(role, instancecreatefrom["name"])
                # )

                return {"msg":"error"}

            port_list= client.NeutronClient.openstack_get_all_ports(client)
            for port  in port_list:
                port_detail = from_dict(data_class=Ports, data=port)
                if port_detail.__dict__["fixed_ips"][0]["ip_address"] == instancecreatefrom["ipv4"] :
                    if port_detail.__dict__["status"] == "DOWN":
                        vmport = port_detail.__dict__["id"]
                        break

            if not vmport:
                subnet_id = client.NeutronClient.openstack_get_subnet_from_network(client,instancecreatefrom["networkRef"] )
                vmport = client.NeutronClient.openstack_create_port(client,instancecreatefrom["networkRef"],instancecreatefrom["ipv4"],subnet_id)


            form = InstanceCreateFrom()
            form.flavorRef = instancecreatefrom["flavorRef"]
            form.name = instancecreatefrom["name"]
            form.imageRef = ""
            form.uuid = instancecreatefrom["volumeRef"]
            form.availability_zone = instancecreatefrom["availability_zone"]
            form.networkRef = instancecreatefrom["networkRef"]
            form.ipv4 = vmport

            instance = client.NovaClient.openstack_create_server(client, form  )
            print(instance)
            if instance["id"]:
                print("msg:ok")
            if user.role_name == "operator":
                with self.session_scope() as session:
                    group = session.query(VmGroup).filter(and_( VmGroup.user_id==user.id,VmGroup.pid== -1 ) ).first()

                    vm = Vms()
                    vm.vmid = instance["id"]
                    vm.user_id = user.id
                    vm.vmgroup_id = group.id
                    session.add(vm)
                    print("2222")
                    my_volume = Volumes()
                    my_volume.vm_id = instance["id"]
                    my_volume.user_id = user.id
                    my_volume.volume_id = form.uuid
                    session.add(my_volume)
                    print("33333")

                    session.commit()

            logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                            {"username": self.get_cookie('username', ""),
                             "op": "创建虚拟机",
                             "object": form.name,
                             "role": self.get_cookie('role', ""),
                             "result": "成功",
                             })
            new_logger.log(
                self.username, "虚机操作", "创建虚拟机", "成功", role, "云硬盘创建云主机: {},成功".format(form.name)
            )
        except Exception as e:
                logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                            {"username": self.get_cookie('username', ""),
                             "op": "创建虚拟机",
                             "object": form.name,
                             "role": self.get_cookie('role', ""),
                             "result": "失败",
                             })
                new_logger.log(
                    self.username, "虚机操作", "创建虚拟机", "失败", role, "云硬盘创建云主机: {},失败".format(form.name)
                )
                return {"msg": "error"}
        return {"msg": "ok"}

    @post(_path="/v2/instance/fromvolumecreate",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_create_instance_fromvolume_v2(self, instancecreatefrom):
        role = self.get_cookie('role', "")
        os_type = instancecreatefrom["os_type"]
        username = self.get_cookie("username", "")
        with self.session_scope() as session:
            user = session.query(User).filter(User.username == username).first()
        vmport = ""

        client = Client()
        if instancecreatefrom["availability_zone"] == "" :
            clusters = client.HypervisorsClient.openstack_get_all_aggregates_list(client)
            if len(clusters) > 0 :
                instancecreatefrom["availability_zone"] = clusters[0]["name"]
            else:
                instancecreatefrom["availability_zone"] = "nova"

        try:
            # volume 列表做出 availiby 筛选

            update_bootable = client.VolumeClient.openstack_update_volume_bootable(client, instancecreatefrom["volumeRef"])
            if update_bootable != 200:
                logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                            {"username": self.get_cookie('username', ""),
                             "op": "创建虚拟机",
                             "object": instancecreatefrom["name"],
                             "role": self.get_cookie('role', ""),
                             "result": "失败",
                             })
                new_logger.log(
                    self.username, "虚机操作", "创建虚拟机", "失败", role, "云硬盘创建虚拟机: {},失败".format(instancecreatefrom["name"])
                )
                return {"msg": "error"}

            port_list= client.NeutronClient.openstack_get_all_ports(client)
            for port  in port_list:
                port_detail = from_dict(data_class=Ports, data=port)
                if port_detail.__dict__["fixed_ips"][0]["ip_address"] == instancecreatefrom["ipv4"] :
                    if port_detail.__dict__["status"] == "DOWN":
                        vmport = port_detail.__dict__["id"]
                        break

            if not vmport:
                subnet_id = client.NeutronClient.openstack_get_subnet_from_network(client,instancecreatefrom["networkRef"] )
                vmport = client.NeutronClient.openstack_create_port(client,instancecreatefrom["networkRef"],instancecreatefrom["ipv4"],subnet_id)


            form = InstanceCreateFromV2()
            form.flavorRef = instancecreatefrom["flavorRef"]
            form.name = instancecreatefrom["name"]
            form.imageRef = ""
            form.uuid = instancecreatefrom["volumeRef"]
            form.availability_zone = instancecreatefrom["availability_zone"]
            form.networkRef = instancecreatefrom["networkRef"]
            form.ipv4 = vmport
            form.os_type = os_type

            instance = client.NovaClient.openstack_create_server(client, form  )
            print(instance)
            if instance["id"]:
                print("msg:ok")
            if user.role_name == "operator":
                with self.session_scope() as session:
                    group = session.query(VmGroup).filter(and_( VmGroup.user_id==user.id,VmGroup.pid== -1 ) ).first()

                    vm = Vms()
                    vm.vmid = instance["id"]
                    vm.user_id = user.id
                    vm.vmgroup_id = group.id
                    session.add(vm)
                    print("2222")
                    my_volume = Volumes()
                    my_volume.vm_id = instance["id"]
                    my_volume.user_id = user.id
                    my_volume.volume_id = form.uuid
                    session.add(my_volume)
                    print("33333")

                    session.commit()
            with self.session_scope() as session:
                task = InstanceCreateTask()
                task.name = instancecreatefrom["name"]
                task.flavorRef = instancecreatefrom["flavorRef"]
                # task.imageRef = instancecreatefrom["imageRef"]
                task.volume_id = instancecreatefrom["volumeRef"]
                task.iso = "false"
                task.status= "complete"
                task.availability_zone = instancecreatefrom["availability_zone"]
                task.user_id = user.id
                task.networkRef = instancecreatefrom["networkRef"]
                task.ipv4 = vmport
                task.os_type = os_type
                session.add(task)
                session.commit()
            logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                            {"username": self.get_cookie('username', ""),
                             "op": "创建虚拟机",
                             "object": form.name,
                             "role": self.get_cookie('role', ""),
                             "result": "成功",
                             })
            new_logger.log(
                self.username, "虚机操作", "创建虚拟机", "成功", role, "云硬盘创建虚拟机: {},成功".format(form.name)
            )
        except Exception as e:
            with self.session_scope() as session:
                task = InstanceCreateTask()
                task.name = instancecreatefrom["name"]
                task.flavorRef = instancecreatefrom["flavorRef"]
                # task.imageRef = instancecreatefrom["imageRef"]
                task.volume_id = instancecreatefrom["volumeRef"]
                task.iso = "false"
                task.status = "complete"
                task.availability_zone = instancecreatefrom["availability_zone"]
                task.user_id = user.id
                task.networkRef = instancecreatefrom["networkRef"]
                task.ipv4 = vmport
                task.os_type = os_type
                session.add(task)
                session.commit()
            logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                        {"username": self.get_cookie('username', ""),
                         "op": "创建虚拟机",
                         "object": form.name,
                         "role": self.get_cookie('role', ""),
                         "result": "失败",
                         })
            new_logger.log(
                self.username, "虚机操作", "创建虚拟机", "失败", role, "云硬盘创建虚拟机: {},失败".format(form.name)
            )
            return {"msg": "error"}
        return {"msg": "ok"}

    @post(_path="/v3/instance/fromvolumecreate", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_create_instance_fromvolume_v3(self, instancecreatefrom):
        role = self.get_cookie('role', "")
        os_type = instancecreatefrom["os_type"]
        ipv4 = instancecreatefrom["ipv4"]
        name = instancecreatefrom["name"]
        username = self.get_cookie("username", "")

        with self.session_scope() as session:
            user = session.query(User).filter(User.username == username).first()
        vmport = ""

        client = Client()
        if instancecreatefrom["availability_zone"] == "":
            clusters = client.HypervisorsClient.openstack_get_all_aggregates_list(client)
            if len(clusters) > 0:
                instancecreatefrom["availability_zone"] = clusters[0]["name"]
            else:
                instancecreatefrom["availability_zone"] = "nova"

        try:
            # volume 列表做出 availiby 筛选

            update_bootable = client.VolumeClient.openstack_update_volume_bootable(client,
                                                                                   instancecreatefrom["volumeRef"])
            if update_bootable != 200:
                logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                             {"username": self.get_cookie('username', ""),
                              "op": "创建虚拟机",
                              "object": instancecreatefrom["name"],
                              "role": self.get_cookie('role', ""),
                              "result": "失败",
                              })
                new_logger.log(
                    self.username, "虚机操作", "创建虚拟机", "失败", role,
                    "云硬盘创建虚拟机: {},失败".format(instancecreatefrom["name"])
                )
                return {"msg": "error"}

            port_list = client.NeutronClient.openstack_get_all_ports(client)
            for port in port_list:
                port_detail = from_dict(data_class=Ports, data=port)
                if port_detail.__dict__["fixed_ips"][0]["ip_address"] == instancecreatefrom["ipv4"]:
                    if port_detail.__dict__["status"] == "DOWN":
                        vmport = port_detail.__dict__["id"]
                        break

            # if not vmport:
            #     # subnet_id = client.NeutronClient.openstack_get_subnet_from_network(client,
            #     #                                                                    instancecreatefrom["networkRef"])
            #     subnet_id = instancecreatefrom.get("subnet_id", "")
            #     vmport = client.NeutronClient.openstack_create_port(client, instancecreatefrom["networkRef"],
            #                                                         instancecreatefrom["ipv4"], subnet_id)
                
            if not vmport:
                # subnet_id = client.NeutronClient.openstack_get_subnet_from_network_with_subnet(client, instancecreatefrom[
                #     "networkRef"])
                subnet_id = instancecreatefrom.get("subnet_id", "")
                if ipv4:
                    vmport = client.NeutronClient.openstack_create_port(client, instancecreatefrom["networkRef"],
                                                                        ipv4, subnet_id)
                else:
                    # 处理双子网问题
                    vmport = client.NeutronClient.openstack_create_port_withoutip(client, instancecreatefrom["networkRef"],
                                                                                    subnet_id, name)

            form = InstanceCreateFromV2()
            form.flavorRef = instancecreatefrom["flavorRef"]
            form.name = instancecreatefrom["name"]
            form.imageRef = ""
            form.uuid = instancecreatefrom["volumeRef"]
            form.availability_zone = instancecreatefrom["availability_zone"]
            form.networkRef = instancecreatefrom["networkRef"]
            form.ipv4 = vmport
            form.os_type = os_type

            instance = client.NovaClient.openstack_create_server(client, form)
            print(instance)
            if instance["id"]:
                print("msg:ok")
            if user.role_name == "operator":
                with self.session_scope() as session:
                    group = session.query(VmGroup).filter(and_(VmGroup.user_id == user.id, VmGroup.pid == -1)).first()

                    vm = Vms()
                    vm.vmid = instance["id"]
                    vm.user_id = user.id
                    vm.vmgroup_id = group.id
                    session.add(vm)
                    print("2222")
                    my_volume = Volumes()
                    my_volume.vm_id = instance["id"]
                    my_volume.user_id = user.id
                    my_volume.volume_id = form.uuid
                    session.add(my_volume)
                    print("33333")

                    session.commit()
            with self.session_scope() as session:
                task = InstanceCreateTask()
                task.name = instancecreatefrom["name"]
                task.flavorRef = instancecreatefrom["flavorRef"]
                # task.imageRef = instancecreatefrom["imageRef"]
                task.volume_id = instancecreatefrom["volumeRef"]
                task.iso = "云硬盘"
                task.status = "complete"
                task.availability_zone = instancecreatefrom["availability_zone"]
                task.user_id = user.id
                task.networkRef = instancecreatefrom["networkRef"]
                task.ipv4 = vmport
                task.os_type = os_type
                session.add(task)
                session.commit()
            logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                        {"username": self.get_cookie('username', ""),
                         "op": "创建虚拟机",
                         "object": form.name,
                         "role": self.get_cookie('role', ""),
                         "result": "成功",
                         })
            new_logger.log(
                self.username, "虚机操作", "创建虚拟机", "成功", role, "云硬盘创建虚拟机: {},成功".format(form.name)
            )
        except Exception as e:
            with self.session_scope() as session:
                task = InstanceCreateTask()
                task.name = instancecreatefrom["name"]
                task.flavorRef = instancecreatefrom["flavorRef"]
                # task.imageRef = instancecreatefrom["imageRef"]
                task.volume_id = instancecreatefrom["volumeRef"]
                task.iso = "云硬盘"
                task.status = "complete"
                task.availability_zone = instancecreatefrom["availability_zone"]
                task.user_id = user.id
                task.networkRef = instancecreatefrom["networkRef"]
                task.ipv4 = vmport
                task.os_type = os_type
                session.add(task)
                session.commit()
            logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                         {"username": self.get_cookie('username', ""),
                          "op": "创建虚拟机",
                          "object": form.name,
                          "role": self.get_cookie('role', ""),
                          "result": "失败",
                          })
            new_logger.log(
                self.username, "虚机操作", "创建虚拟机", "失败", role, "云硬盘创建虚拟机: {},失败".format(form.name)
            )
            return {"msg": "error"}
        return {"msg": "ok"}


    @post(_path="/v1/instance/createfromsnapshot",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_create_instance_fromsnapshot(self, instancecreatefrom):
        role = self.get_cookie('role', "")
        result = {"msg":"ok"}
        username = self.get_cookie("username", "")
        with self.session_scope() as session:
            user = session.query(User).filter(User.username == username).first()
            session.expunge(user)
        vmport = ""
        client = Client()
        if instancecreatefrom["availability_zone"] == "" :
            clusters = client.HypervisorsClient.openstack_get_all_aggregates_list(client)
            if len(clusters) > 0 :
                instancecreatefrom["availability_zone"] = clusters[0]["name"]
            else:
                instancecreatefrom["availability_zone"] = "nova"
        try:
            # volume 列表做出 availiby 筛选

            port_list= client.NeutronClient.openstack_get_all_ports(client)
            for port  in port_list:
                port_detail = from_dict(data_class=Ports, data=port)
                if port_detail.__dict__["fixed_ips"][0]["ip_address"] == instancecreatefrom["ipv4"] :
                    if port_detail.__dict__["status"] == "DOWN":
                        vmport = port_detail.__dict__["id"]
                        break

            if not vmport:
                subnet_id = client.NeutronClient.openstack_get_subnet_from_network(client,instancecreatefrom["networkRef"] )
                vmport = client.NeutronClient.openstack_create_port(client,instancecreatefrom["networkRef"],instancecreatefrom["ipv4"],subnet_id)


            form = InstanceCreateFrom()
            form.flavorRef = instancecreatefrom["flavorRef"]
            form.name = instancecreatefrom["name"]
            form.imageRef = ""
            form.uuid = instancecreatefrom["snapshotRef"]
            form.availability_zone = instancecreatefrom["availability_zone"]
            form.networkRef = instancecreatefrom["networkRef"]
            form.ipv4 = vmport

            instance = client.NovaClient.openstack_create_server_from_snapshot(client, form  )
            if instance["id"]:
                is_operator = user.role_name == "operator"
                create_volume_form = {
                    "instance_id" : instance["id"],
                    "instance_name": instancecreatefrom["name"],
                    "snapshotRef" : form.uuid,
                    "user_id" : user.id,
                    "username" : user.name,
                    "user_role_name": user.role_name
                }
                task_id = create_volume_for_fromsnapshot_vm.delay(create_volume_form)
                print(task_id)
                result["task_id"] =  str(task_id)
                if is_operator:
                    with self.session_scope() as session:
                        group = session.query(VmGroup).filter(and_( VmGroup.user_id==user.id,VmGroup.pid== -1 ) ).first()
                        vm = Vms()
                        vm.vmid = instance["id"]
                        vm.user_id = user.id
                        vm.vmgroup_id = group.id
                        session.add(vm)
            """
            print(instance)
            if instance["id"]:
                print("msg:ok")
                detail = client.NovaClient.openstack_get_server_detail(client, instance["id"])
                print("11111")
                print(detail)
                volumename = "%s-volume" % detail["name"]
                print("222222")
                
                if detail["status"] == "BUILD" :
                    while detail["status"] != "ACTIVE" :
                        detail = client.NovaClient.openstack_get_server_detail(client, instance["id"])
                        
                        if detail["status"] == "error" :
                            break
               
                res1 = client.VolumeClient.openstack_edit_volume(client,detail["volumeid"],volumename)
                print("333333")
            if user.role_name:
                print("4444444")
                if user.role_name == "operator":
                    with self.session_scope() as session:
                        group = session.query(VmGroup).filter(and_( VmGroup.user_id==user.id,VmGroup.pid== -1 ) ).first()
    
                        vm = Vms()
                        vm.vmid = instance["id"]
                        vm.user_id = user.id
                        vm.vmgroup_id = group.id
                        session.add(vm)
                        print("2222")
                        my_volume = Volumes()
                        my_volume.vm_id = instance["id"]
                        my_volume.user_id = user.id
                        my_volume.volume_id = form.uuid
                        session.add(my_volume)
                        print("33333")
                        
                        session.commit()
            

            logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                            {"username": self.get_cookie('username', ""),
                             "op": "创建虚拟机",
                             "object": form.name,
                             "role": self.get_cookie('role', ""),
                             "result": "成功",
                             })
            """
        except Exception as e:
            traceback.print_exc()
            logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                         {"username": self.get_cookie('username', ""),
                          "op": "创建虚拟机",
                          "object": form.name,
                          "role": self.get_cookie('role', ""),
                          "result": "失败",
                          })
            new_logger.log(
                self.username, "虚机操作", "创建虚拟机", "失败", role, "快照创建云主机: {},失败".format(form.name)
            )
            return {"msg": "error"}
        return result

    @post(_path="/v2/instance/createfromsnapshot", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_create_instance_fromsnapshot_v2(self, instancecreatefrom):
        result = {"msg": "ok"}
        role = self.get_cookie('role', "")
        username = self.get_cookie("username", "")

        ipv4 = instancecreatefrom.get("ipv4", "")
        print("ipv4信息：",ipv4)
        ipv4_list = ipv4.split(",")

        if instancecreatefrom["count"] == 0:
            vm_count = 1
        else:
            vm_count = instancecreatefrom["count"]

        if len(ipv4_list) > 1:
            if len(ipv4_list) != vm_count:
                return {"msg":"预设ip和虚机数量不一样"}


        with self.session_scope() as session:
            user = session.query(User).filter(User.username == username).first()
            session.expunge(user)
        client = Client()
        if instancecreatefrom["availability_zone"] == "":
            clusters = client.HypervisorsClient.openstack_get_all_aggregates_list(client)
            if len(clusters) > 0:
                instancecreatefrom["availability_zone"] = clusters[0]["name"]
            else:
                instancecreatefrom["availability_zone"] = "nova"
        try:
            # volume 列表做出 availiby 筛选
            for i in range(vm_count):
                vmport = ""
                # print('ip:', ipv4_list[i])
                port_list = client.NeutronClient.openstack_get_all_ports(client)
                # print("port_list:", port_list)
                for port in port_list:
                    port_detail = from_dict(data_class=Ports, data=port)
                    if len(port_detail.__dict__["fixed_ips"]) > 0 and len(ipv4_list) == vm_count:
                        # print("有预设IP")
                        if port_detail.__dict__["fixed_ips"][0]["ip_address"] == ipv4_list[i]:
                            print("prot_list存在IP",ipv4_list[i], "状态：",port_detail.__dict__["status"])
                            # TODO port_detail.__dict__["status"] == "DOWN": ?不明含义
                            if port_detail.__dict__["status"] == "DOWN":
                                # 该port可用， 将port_detail.__dict__["id"]赋值给vmport
                                vmport = port_detail.__dict__["id"]
                                break

                if not vmport:
                    subnet_id = client.NeutronClient.openstack_get_subnet_from_network(client,
                                                                                       instancecreatefrom["networkRef"])
                    print("vmport为空的情况下，请求neutron接口获取subnet_id:",subnet_id)
                    if len(ipv4_list) == vm_count:
                        vmport = client.NeutronClient.openstack_create_port(client, instancecreatefrom["networkRef"],
                                                                        ipv4_list[i], subnet_id)
                        print("[自定义ip]使用subnet_id获取vmport：", vmport)
                    else:
                        vmport = client.NeutronClient.openstack_create_port(client, instancecreatefrom["networkRef"],
                                                                            instancecreatefrom["ipv4"], subnet_id)
                        print("[自动IP]使用subnet_id获取vmport：", vmport)

                print('vmport:', vmport)
                form = InstanceCreateFromV2()
                form.os_type = instancecreatefrom["os_type"]
                form.flavorRef = instancecreatefrom["flavorRef"]
                form.name = instancecreatefrom["name"] + "-" + str(i)
                form.imageRef = ""
                form.uuid = instancecreatefrom["snapshotRef"]
                form.availability_zone = instancecreatefrom["availability_zone"]
                form.networkRef = instancecreatefrom["networkRef"]
                form.ipv4 = vmport
                instance = client.NovaClient.openstack_create_server_from_snapshot(client, form)
                print("instance信息：",instance)
                if instance["id"]:
                    print("创建celery任务")
                    is_operator = user.role_name == "operator"
                    create_volume_form = {
                        "instance_id": instance["id"],
                        "instance_name": instancecreatefrom["name"],
                        "snapshotRef": form.uuid,
                        "user_id": user.id,
                        "username": user.name,
                        "user_role_name": user.role_name
                    }
                    task_id = create_volume_for_fromsnapshot_vm.delay(create_volume_form)
                    print(task_id)
                    result["task_id"] = str(task_id)
                    with self.session_scope() as session:
                        task = InstanceCreateTask()
                        task.name = instancecreatefrom["name"] + "-" + str(i)
                        task.flavorRef = instancecreatefrom["flavorRef"]
                        # task.imageRef = instancecreatefrom["imageRef"]
                        # task.volume_id = instancecreatefrom["volumeRef"]
                        task.iso = "快照"
                        task.status = "complete"
                        task.availability_zone = instancecreatefrom["availability_zone"]
                        task.user_id = user.id
                        task.networkRef = instancecreatefrom["networkRef"]
                        task.ipv4 = vmport
                        task.os_type = instancecreatefrom["os_type"]
                        session.add(task)
                        session.commit()
                    if is_operator:
                        with self.session_scope() as session:
                            group = session.query(VmGroup).filter(
                                and_(VmGroup.user_id == user.id, VmGroup.pid == -1)).first()
                            vm = Vms()
                            vm.vmid = instance["id"]
                            vm.user_id = user.id
                            vm.vmgroup_id = group.id
                            session.add(vm)
                    new_logger.log(
                        self.username, "虚机操作", "创建虚拟机", "成功", role, "快照创建云主机: {},成功".format(instancecreatefrom["name"])
                    )
                else:
                    new_logger.log(
                        self.username, "虚机操作", "创建虚拟机", "失败", role, "快照创建云主机: {},失败".format(instancecreatefrom["name"])
                    )
                    return {"msg": "请检测openstack接口是否正常"}
        except Exception as e:
                traceback.print_exc()
                with self.session_scope() as session:
                    task = InstanceCreateTask()
                    task.name = instancecreatefrom["name"] + "-" + str(i)
                    task.flavorRef = instancecreatefrom["flavorRef"]
                    # task.imageRef = instancecreatefrom["imageRef"]
                    # task.volume_id = instancecreatefrom["volumeRef"]
                    task.iso = "false"
                    task.status = "error"
                    task.availability_zone = instancecreatefrom["availability_zone"]
                    task.user_id = user.id
                    task.networkRef = instancecreatefrom["networkRef"]
                    task.ipv4 = vmport
                    task.os_type = instancecreatefrom["os_type"]
                    session.add(task)
                    session.commit()
                logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                            {"username": self.get_cookie('username', ""),
                             "op": "创建虚拟机",
                             "object": form.name,
                             "role": self.get_cookie('role', ""),
                             "result": "失败",
                             })
                new_logger.log(
                    self.username, "虚机操作", "创建虚拟机", "失败", role, "快照创建云主机: {},失败".format(form.name)
                )
                return {"msg": "error"}
        return result

    @post(_path="/v3/instance/createfromsnapshot", _consumes=mediatypes.APPLICATION_JSON,
          _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_create_instance_fromsnapshot_v3(self, instancecreatefrom):
        result = {"msg": "ok"}
        role = self.get_cookie('role', "")
        username = self.get_cookie("username", "")

        ipv4 = instancecreatefrom.get("ipv4", "")
        print("ipv4信息：", ipv4)
        ipv4_list = ipv4.split(",")

        if instancecreatefrom["count"] == 0:
            vm_count = 1
        else:
            vm_count = instancecreatefrom["count"]

        if len(ipv4_list) > 1:
            if len(ipv4_list) != vm_count:
                return {"msg": "预设ip和虚机数量不一样"}

        with self.session_scope() as session:
            user = session.query(User).filter(User.username == username).first()
            session.expunge(user)
        client = Client()
        if instancecreatefrom["availability_zone"] == "":
            clusters = client.HypervisorsClient.openstack_get_all_aggregates_list(client)
            if len(clusters) > 0:
                instancecreatefrom["availability_zone"] = clusters[0]["name"]
            else:
                instancecreatefrom["availability_zone"] = "nova"
        try:
            # volume 列表做出 availiby 筛选
            for i in range(vm_count):
                vmport = ""
                vm_name = instancecreatefrom["name"] + "-" + str(i)
                
                ipv4 = ipv4_list[i] if i < len(ipv4_list) else ''

                vmport = self.get_vmport(client, ipv4)

                if not vmport:
                    subnet_id = instancecreatefrom.get("subnet_id", "")
                    if ipv4:
                        vmport = client.NeutronClient.openstack_create_port(client, instancecreatefrom["networkRef"],
                                                                            ipv4, subnet_id)
                    else:
                        # 处理双子网问题
                        vmport = client.NeutronClient.openstack_create_port_withoutip(client, instancecreatefrom["networkRef"],
                                                                                        subnet_id, vm_name)

                print('vmport:', vmport)
                form = InstanceCreateFromV2()
                form.os_type = instancecreatefrom["os_type"]
                form.flavorRef = instancecreatefrom["flavorRef"]
                form.name = vm_name
                form.imageRef = ""
                form.uuid = instancecreatefrom["snapshotRef"]
                form.availability_zone = instancecreatefrom["availability_zone"]
                form.networkRef = instancecreatefrom["networkRef"]
                form.ipv4 = vmport
                instance = client.NovaClient.openstack_create_server_from_snapshot(client, form)
                print("instance信息：", instance)
                if instance["id"]:
                    print("创建celery任务")
                    is_operator = user.role_name == "operator"
                    create_volume_form = {
                        "instance_id": instance["id"],
                        "instance_name": instancecreatefrom["name"],
                        "snapshotRef": form.uuid,
                        "user_id": user.id,
                        "username": user.name,
                        "user_role_name": user.role_name
                    }
                    task_id = create_volume_for_fromsnapshot_vm.delay(create_volume_form)
                    print(task_id)
                    result["task_id"] = str(task_id)
                    with self.session_scope() as session:
                        task = InstanceCreateTask()
                        task.name = instancecreatefrom["name"] + "-" + str(i)
                        task.flavorRef = instancecreatefrom["flavorRef"]
                        # task.imageRef = instancecreatefrom["imageRef"]
                        # task.volume_id = instancecreatefrom["volumeRef"]
                        task.iso = "快照"
                        task.status = "complete"
                        task.availability_zone = instancecreatefrom["availability_zone"]
                        task.user_id = user.id
                        task.networkRef = instancecreatefrom["networkRef"]
                        task.ipv4 = vmport
                        task.os_type = instancecreatefrom["os_type"]
                        session.add(task)
                        session.commit()
                    if is_operator:
                        with self.session_scope() as session:
                            group = session.query(VmGroup).filter(
                                and_(VmGroup.user_id == user.id, VmGroup.pid == -1)).first()
                            vm = Vms()
                            vm.vmid = instance["id"]
                            vm.user_id = user.id
                            vm.vmgroup_id = group.id
                            session.add(vm)
                    new_logger.log(
                        self.username, "虚机操作", "创建虚拟机", "成功", role,
                        "快照创建云主机: {},成功".format(instancecreatefrom["name"])
                    )
                else:
                    new_logger.log(
                        self.username, "虚机操作", "创建虚拟机", "失败", role,
                        "快照创建云主机: {},失败".format(instancecreatefrom["name"])
                    )
                    return {"msg": "请检测openstack接口是否正常"}
        except Exception as e:
            traceback.print_exc()
            with self.session_scope() as session:
                task = InstanceCreateTask()
                task.name = instancecreatefrom["name"] + "-" + str(i)
                task.flavorRef = instancecreatefrom["flavorRef"]
                # task.imageRef = instancecreatefrom["imageRef"]
                # task.volume_id = instancecreatefrom["volumeRef"]
                task.iso = "false"
                task.status = "error"
                task.availability_zone = instancecreatefrom["availability_zone"]
                task.user_id = user.id
                task.networkRef = instancecreatefrom["networkRef"]
                task.ipv4 = vmport
                task.os_type = instancecreatefrom["os_type"]
                session.add(task)
                session.commit()
            logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                         {"username": self.get_cookie('username', ""),
                          "op": "创建虚拟机",
                          "object": form.name,
                          "role": self.get_cookie('role', ""),
                          "result": "失败",
                          })
            new_logger.log(
                self.username, "虚机操作", "创建虚拟机", "失败", role, "快照创建云主机: {},失败".format(form.name)
            )
            return {"msg": "error"}
        return result

    @post(_path="/v1/instance/isocreate",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_iso_create_instances(self, instancecreatefrom):
        """
        ---
        tags:
          - 虚拟机相关接口
        summary: 从iso创建虚拟机
        requestBody:
          description: 创建
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RequestCreateInstanceModel'
          required: true
        responses:
          '200':
            description: 创建成功
            content: {}
        """
        role = self.get_cookie('role', "")
        username = self.get_cookie("username", "")
        with self.session_scope() as session:
            user = session.query(User).filter(User.username == username).first()
            session.expunge(user)
        try:
            client = Client()
            vmport = ""
            flavor_detail = client.FlavorClient.openstack_get_flavor_detail(client, instancecreatefrom["flavorRef"])

            create_form = VolumeCreateFrom()
            create_form.name = "%s_%s" % (instancecreatefrom["name"],"iso")
            create_form.size = 20
            create_form.imageRef = instancecreatefrom["imageRef"]
            create_form.description = ""

            volume = client.VolumeClient.openstack_create_volume(client,create_form)
            instancecreatefrom["uuid"] = volume["id"]

            port_list= client.NeutronClient.openstack_get_all_ports(client)
            for port  in port_list:
                port_detail = from_dict(data_class=Ports, data=port)
                if port_detail.__dict__["fixed_ips"][0]["ip_address"] == instancecreatefrom["ipv4"] :
                    if port_detail.__dict__["status"] == "DOWN":
                        vmport = port_detail.__dict__["id"]
                        break

            if not vmport:
                subnet_id = client.NeutronClient.openstack_get_subnet_from_network(client,instancecreatefrom["networkRef"] )
                vmport = client.NeutronClient.openstack_create_port(client,instancecreatefrom["networkRef"],instancecreatefrom["ipv4"],subnet_id)


            task = InstanceCreateTask()
            task.name  = instancecreatefrom["name"]
            task.flavorRef  = instancecreatefrom["flavorRef"]
            task.imageRef  = instancecreatefrom["imageRef"]
            task.volume_id  = instancecreatefrom["uuid"]
            task.iso  = "ISO"
            task.user_id = user.id
            task.availability_zone = instancecreatefrom["availability_zone"]
            task.networkRef  = instancecreatefrom["networkRef"]
            task.ipv4 = vmport
            task.os_type = instancecreatefrom["os_type"]

            if instancecreatefrom["driveimageId"] :

                create_form_drive = VolumeCreateFrom()
                create_form_drive.name = "%s_%s" % (instancecreatefrom["name"],"drive")
                create_form_drive.size = 10
                create_form_drive.imageRef = instancecreatefrom["driveimageId"]
                create_form_drive.description = ""
                volume_drive = client.VolumeClient.openstack_create_volume(client,create_form_drive)
                task.driver_volume_id = volume_drive["id"]
                task.ipv4 = vmport

            else:
                task.driver_volume_id = ""
            with self.session_scope() as session:
                session.add(task)
                session.commit()
            logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                            {"username": self.get_cookie('username', ""),
                             "op": "从iso创建虚拟机",
                             "object": instancecreatefrom["name"],
                             "role": self.get_cookie('role', ""),
                             "result": "成功",
                             })
            # new_logger.log(
            #     "虚机操作", "创建虚拟机", "成功", role, "{} ISO创建云主机: {},成功".format(role, instancecreatefrom["name"])
            # )
        except Exception as e:

                logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                            {"username": self.get_cookie('username', ""),
                             "op": "从iso创建虚拟机",
                             "object": instancecreatefrom["name"],
                             "role": self.get_cookie('role', ""),
                             "result": "失败",
                             })
                # new_logger.log(
                #     "虚机操作", "创建虚拟机", "失败", role, "{} ISO创建云主机: {},失败".format(role, instancecreatefrom["name"])
                # )
                return {"msg": "error"}
        return {"msg": "ok"}

    @post(_path="/v2/instance/isocreate", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_iso_create_instances_v2(self, instancecreatefrom):
        """
        ---
        tags:
          - 虚拟机相关接口
        summary: 从iso创建虚拟机
        requestBody:
          description: 创建
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RequestCreateInstanceModel'
          required: true
        responses:
          '200':
            description: 创建成功
            content: {}
        """
        role = self.get_cookie('role', "")
        username = self.get_cookie("username", "")
        with self.session_scope() as session:
            user = session.query(User).filter(User.username == username).first()
            session.expunge(user)
        try:
            client = Client()
            vmport = ""
            flavor_detail = client.FlavorClient.openstack_get_flavor_detail(client, instancecreatefrom["flavorRef"])

            create_form = VolumeCreateFrom()
            create_form.name = "%s_%s" % (instancecreatefrom["name"],"iso")
            create_form.size = 20
            create_form.imageRef = instancecreatefrom["imageRef"]
            create_form.description = ""

            volume = client.VolumeClient.openstack_create_volume(client, create_form)
            instancecreatefrom["uuid"] = volume["id"]

            port_list= client.NeutronClient.openstack_get_all_ports(client)
            for port  in port_list:
                port_detail = from_dict(data_class=Ports, data=port)
                if port_detail.__dict__["fixed_ips"][0]["ip_address"] == instancecreatefrom["ipv4"] :
                    if port_detail.__dict__["status"] == "DOWN":
                        vmport = port_detail.__dict__["id"]
                        break

            if not vmport:
                # subnet_id = client.NeutronClient.openstack_get_subnet_from_network(client,instancecreatefrom["networkRef"] )
                subnet_id = instancecreatefrom.get("subnet_id", "")
                vmport = client.NeutronClient.openstack_create_port(client,instancecreatefrom["networkRef"],instancecreatefrom["ipv4"],subnet_id)

            # TODO 创建一个空的云主机 并且把vm_id关联到任务表中'
            # if vmport != "":
            #     instancecreatefrom["ipv4"] = vmport
            # instancecreatefrom.setdefault("iso", True)
            # instance = client.NovaClient.openstack_fast_create_server(client,instancecreatefrom)

            task = InstanceCreateTask()
            task.name  = instancecreatefrom["name"]
            task.flavorRef  = instancecreatefrom["flavorRef"]
            task.imageRef  = instancecreatefrom["imageRef"]
            task.volume_id  = instancecreatefrom["uuid"]
            task.iso  = "ISO"
            task.user_id = user.id
            task.availability_zone = instancecreatefrom["availability_zone"]
            task.networkRef  = instancecreatefrom["networkRef"]
            task.ipv4 = vmport
            task.os_type = instancecreatefrom["os_type"]
            # task.vmid = instance["id"]

            if instancecreatefrom["driveimageId"] :

                create_form_drive = VolumeCreateFrom()
                create_form_drive.name = "%s_%s" % (instancecreatefrom["name"],"drive")
                create_form_drive.size = 10
                create_form_drive.imageRef = instancecreatefrom["driveimageId"]
                create_form_drive.description = ""
                volume_drive = client.VolumeClient.openstack_create_volume(client,create_form_drive)
                task.driver_volume_id = volume_drive["id"]
                task.ipv4 = vmport

            else:
                task.driver_volume_id = ""
            with self.session_scope() as session:
                session.add(task)
                session.commit()
            # logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
            #                 {"username": self.get_cookie('username', ""),
            #                  "op": "从iso创建虚拟机",
            #                  "object": instancecreatefrom["name"],
            #                  "role": self.get_cookie('role', ""),
            #                  "result": "成功",
            #                  })
            # new_logger.log(
            #     "虚机操作", "创建虚拟机", "成功", role, "{} ISO创建云主机: {},成功".format(role, instancecreatefrom["name"])
            # )
        except Exception as e:

                logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                            {"username": self.get_cookie('username', ""),
                             "op": "从iso创建虚拟机",
                             "object": instancecreatefrom["name"],
                             "role": self.get_cookie('role', ""),
                             "result": "失败",
                             })
                # new_logger.log(
                #     "虚机操作", "创建虚拟机", "失败", role, "{} ISO创建云主机: {},失败".format(role, instancecreatefrom["name"])
                # )
                traceback.print_exc()
                self.set_status(502)
                return {"msg":"error"}
        return {"msg":"ok"}

    @post(_path="/v3/instance/isocreate", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_iso_create_instances_v3(self, instancecreatefrom):
        """
        ---
        tags:
          - 虚拟机相关接口
        summary: 从iso创建虚拟机
        requestBody:
          description: 创建
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RequestCreateInstanceModel'
          required: true
        responses:
          '200':
            description: 创建成功
            content: {}
        """
        role = self.get_cookie('role', "")
        username = self.get_cookie("username", "")
        with self.session_scope() as session:
            user = session.query(User).filter(User.username == username).first()
            session.expunge(user)
        try:
            client = Client()
            vmport = ""
            flavor_detail = client.FlavorClient.openstack_get_flavor_detail(client, instancecreatefrom["flavorRef"])

            create_form = VolumeCreateFrom()
            create_form.name = "%s_%s" % (instancecreatefrom["name"], "iso")
            create_form.size = 20
            create_form.imageRef = instancecreatefrom["imageRef"]
            create_form.description = ""

            volume = client.VolumeClient.openstack_create_volume(client, create_form)
            instancecreatefrom["uuid"] = volume["id"]

            port_list = client.NeutronClient.openstack_get_all_ports(client)
            for port in port_list:
                port_detail = from_dict(data_class=Ports, data=port)
                if port_detail.__dict__["fixed_ips"][0]["ip_address"] == instancecreatefrom["ipv4"]:
                    if port_detail.__dict__["status"] == "DOWN":
                        vmport = port_detail.__dict__["id"]
                        break

            if not vmport:
                # subnet_id = client.NeutronClient.openstack_get_subnet_from_network(client,instancecreatefrom["networkRef"] )
                subnet_id = instancecreatefrom.get("subnet_id", "")
                vmport = client.NeutronClient.openstack_create_port(client, instancecreatefrom["networkRef"],
                                                                    instancecreatefrom["ipv4"], subnet_id)

            # TODO 创建一个空的云主机 并且把vm_id关联到任务表中'
            # if vmport != "":
            #     instancecreatefrom["ipv4"] = vmport
            # instancecreatefrom.setdefault("iso", True)
            instance = client.NovaClient.openstack_fast_create_server(client, instancecreatefrom)

            task = InstanceCreateTask()
            task.name = instancecreatefrom["name"]
            task.flavorRef = instancecreatefrom["flavorRef"]
            task.imageRef = instancecreatefrom["imageRef"]
            task.volume_id = instancecreatefrom["uuid"]
            task.iso = "ISO"
            task.user_id = user.id
            task.availability_zone = instancecreatefrom["availability_zone"]
            task.networkRef = instancecreatefrom["networkRef"]
            task.ipv4 = vmport
            task.os_type = instancecreatefrom["os_type"]
            task.vmid = instance["id"]

            if instancecreatefrom["driveimageId"]:

                create_form_drive = VolumeCreateFrom()
                create_form_drive.name = "%s_%s" % (instancecreatefrom["name"], "drive")
                create_form_drive.size = 10
                create_form_drive.imageRef = instancecreatefrom["driveimageId"]
                create_form_drive.description = ""
                volume_drive = client.VolumeClient.openstack_create_volume(client, create_form_drive)
                task.driver_volume_id = volume_drive["id"]
                task.ipv4 = vmport

            else:
                task.driver_volume_id = ""
            with self.session_scope() as session:
                session.add(task)
                session.commit()
            # logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
            #                 {"username": self.get_cookie('username', ""),
            #                  "op": "从iso创建虚拟机",
            #                  "object": instancecreatefrom["name"],
            #                  "role": self.get_cookie('role', ""),
            #                  "result": "成功",
            #                  })
            # new_logger.log(
            #     "虚机操作", "创建虚拟机", "成功", role, "{} ISO创建云主机: {},成功".format(role, instancecreatefrom["name"])
            # )
        except Exception as e:

            logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                         {"username": self.get_cookie('username', ""),
                          "op": "从iso创建虚拟机",
                          "object": instancecreatefrom["name"],
                          "role": self.get_cookie('role', ""),
                          "result": "失败",
                          })
            # new_logger.log(
            #     "虚机操作", "创建虚拟机", "失败", role, "{} ISO创建云主机: {},失败".format(role, instancecreatefrom["name"])
            # )
            traceback.print_exc()
            self.set_status(502)
            return {"msg": "error"}
        return {"msg": "ok"}

    @post(_path="/v4/instance/isocreate", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_iso_create_instances_v4(self, instancecreatefrom):
        """
        ---
        tags:
          - 虚拟机相关接口
        summary: 从iso创建虚拟机
        requestBody:
          description: 创建
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RequestCreateInstanceModel'
          required: true
        responses:
          '200':
            description: 创建成功
            content: {}
        """
        role = self.get_cookie('role', "")
        username = self.get_cookie("username", "")
        with self.session_scope() as session:
            user = session.query(User).filter(User.username == username).first()
            session.expunge(user)
        try:
            client = Client()
            vmport = ""
            flavor_detail = client.FlavorClient.openstack_get_flavor_detail(client, instancecreatefrom["flavorRef"])

            create_form = VolumeCreateFrom()
            create_form.name = "%s_%s" % (instancecreatefrom["name"], "iso")
            create_form.size = 20
            create_form.imageRef = instancecreatefrom["imageRef"]
            create_form.description = ""

            volume = client.VolumeClient.openstack_create_volume(client, create_form)
            instancecreatefrom["uuid"] = volume["id"]

            port_list = client.NeutronClient.openstack_get_all_ports(client)
            for port in port_list:
                port_detail = from_dict(data_class=Ports, data=port)
                if port_detail.__dict__["fixed_ips"][0]["ip_address"] == instancecreatefrom["ipv4"]:
                    if port_detail.__dict__["status"] == "DOWN":
                        vmport = port_detail.__dict__["id"]
                        break

            if not vmport:
                # subnet_id = client.NeutronClient.openstack_get_subnet_from_network(client,instancecreatefrom["networkRef"] )
                subnet_id = instancecreatefrom.get("subnet_id", "")
                vmport = client.NeutronClient.openstack_create_port(client, instancecreatefrom["networkRef"],
                                                                    instancecreatefrom["ipv4"], subnet_id)

            # TODO 创建一个空的云主机 并且把vm_id关联到任务表中'
            if vmport != "":
                instancecreatefrom["ipv4"] = vmport
            instancecreatefrom.setdefault("iso", True)
            instance = client.NovaClient.openstack_fast_create_server_v2(client, instancecreatefrom)

            # task = InstanceCreateTask()
            # task.name = instancecreatefrom["name"]
            # task.flavorRef = instancecreatefrom["flavorRef"]
            # task.imageRef = instancecreatefrom["imageRef"]
            # task.volume_id = instancecreatefrom["uuid"]
            # task.iso = "ISO"
            # task.user_id = user.id
            # task.availability_zone = instancecreatefrom["availability_zone"]
            # task.networkRef = instancecreatefrom["networkRef"]
            # task.ipv4 = vmport
            # task.os_type = instancecreatefrom["os_type"]
            # task.vmid = instance["id"]
            #
            # if instancecreatefrom["driveimageId"]:
            #
            #     create_form_drive = VolumeCreateFrom()
            #     create_form_drive.name = "%s_%s" % (instancecreatefrom["name"], "drive")
            #     create_form_drive.size = 10
            #     create_form_drive.imageRef = instancecreatefrom["driveimageId"]
            #     create_form_drive.description = ""
            #     volume_drive = client.VolumeClient.openstack_create_volume(client, create_form_drive)
            #     task.driver_volume_id = volume_drive["id"]
            #     task.ipv4 = vmport
            #
            # else:
            #     task.driver_volume_id = ""
            # with self.session_scope() as session:
            #     session.add(task)
            #     session.commit()

        except Exception as e:

            traceback.print_exc()
            self.set_status(502)
            return {"msg": "error"}
        return {"msg": "ok"}


    @delete(_path="/v1/instance/delete",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_delete_server(self, instancedeleteform):
        """
        ---
        tags:
          - 虚拟机相关接口
        summary: 删除虚拟机
        requestBody:
          description: 删除
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RequestDeleteInstanceModel'
          required: true
        responses:
          '200':
            description: 删除成功
            content: {}
        """
        try:
            role = self.get_cookie('role', "")

            names = instancedeleteform.get("names", [])
            ids = instancedeleteform.get("ids", [])
            for i, id in enumerate(ids):
                try:
                    client = Client()
                    res = client.NovaClient.openstack_delete_server(client, id)
                    with self.session_scope() as session:
                        session.query(Vms).filter(Vms.vmid==id).delete()
                        session.query(Volumes).filter(Volumes.vm_id==id).delete()
                        session.commit()

                # logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                #                 {"username": self.get_cookie('username', ""),
                #                  "op": "删除虚拟机",
                #                  "object": names[i],
                #                  "role": self.get_cookie('role', ""),
                #                  "result": "成功",
                #              })
                    new_logger.log(
                        self.username, "虚机操作", "删除虚拟机", "成功", role, "删除云主机: {},成功".format(names[i])
                    )
                except Exception as e:
                    traceback.print_exc()
                # logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                #             {"username": self.get_cookie('username', ""),
                #              "op": "删除虚拟机",
                #              "object": id,
                #              "role": self.get_cookie('role', ""),
                #              "result": "失败",
                #              })
                    new_logger.log(
                        self.username, "虚机操作", "删除虚拟机", "失败", role, "删除云主机: {},失败".format(names[i])
                    )
        except Exception as e:
            traceback.print_exc()
            self.set_status(502)
            return {"msg": "error"}
        return res

    @delete(_path="/v2/instance/delete", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_delete_server_v2(self, instancedeleteform):
        try:
            role = self.get_cookie('role', "")

            names = instancedeleteform.get("names", [])
            ids = instancedeleteform.get("ids", [])
            for i, id in enumerate(ids):
                try:
                    client = Client()
                    res = client.NovaClient.openstack_delete_server(client, id)
                    with self.session_scope() as session:
                        session.query(Vms).filter(Vms.vmid==id).delete()
                        session.query(Volumes).filter(Volumes.vm_id==id).delete()

                        Whitelist = session.query(VmWhitelist).filter(VmWhitelist.vmid==id).all()
                        if Whitelist:
                            session.query(VmWhitelist).filter(VmWhitelist.vmid==id).delete()

                        session.commit()

                    new_logger.log(
                        self.username, "虚机操作", "删除虚拟机", "成功", role, "删除云主机: {},成功".format(names[i])
                    )
                except Exception as e:
                    traceback.print_exc()
                    new_logger.log(
                        self.username, "虚机操作", "删除虚拟机", "失败", role, "删除云主机: {},失败".format(names[i])
                    )
        except Exception as e:
            traceback.print_exc()
            self.set_status(502)
            return {"msg": "error"}
        return res

    # @put(_path="/v1/instance/edit",_types=[InstanceEditFrom],_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    @put(_path="/v1/instance/edit",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_put_edit_server(self, instanceeditform):
        """
        ---
        tags:
          - 虚拟机相关接口
        summary: 编辑虚拟机
        requestBody:
          description: 编辑
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RequestEditInstanceModel'
          required: true
        responses:
          '200':
            description: 编辑成功
            content: {}
        """
        role = self.get_cookie('role', "")
        id = instanceeditform.get("id", "")
        source_name = instanceeditform.get("source_name", "")
        name = instanceeditform.get("name", "")
        try:
            client = Client()
            res = client.NovaClient.openstack_edit_server_v2(client, instanceeditform)
            # logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
            #                 {"username": self.get_cookie('username', ""),
            #                  "op": "重命名虚拟机",
            #                  "object": "%s %s" % (instanceeditform.id   ,  instanceeditform.name),
            #                  "role": self.get_cookie('role', ""),
            #                  "result": "成功",
            #                  })
            new_logger.log(
                self.username, "虚机操作", "重命名虚拟机", "成功", role,
                "重命名虚拟机{}: {},成功".format(source_name, name)
            )
        except Exception as e:

                # logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                #             {"username": self.get_cookie('username', ""),
                #              "op": "重命名虚拟机",
                #              "object": "%s %s" % (instanceeditform.id   ,  instanceeditform.name),
                #              "role": self.get_cookie('role', ""),
                #              "result": "失败",
                #              })
                new_logger.log(
                    self.username, "虚机操作", "重命名虚拟机", "失败", role,
                    "重命名虚拟机{}: {},失败".format(source_name, name)
                )
                return {"msg": "error"}
        return res

    @post(_path="/v1/instance/snapshot",_types=[InstanceDeleteFrom],_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_server_snapshot(self, instanceform):
        """
        ---
        tags:
          - 虚拟机相关接口
        summary: 虚拟机创建快照
        requestBody:
          description: 快照
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RequestSnapshotInstanceModel'
          required: true
        responses:
          '202':
            description: 创建成功
            content: {}
        """
        role = self.get_cookie('role', "")
        try:
            client = Client()
            res = client.NovaClient.openstack_server_snapshot(client, instanceform)
            logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                            {"username": self.get_cookie('username', ""),
                             "op": "创建虚拟机快照",
                             "object": instanceform.id,
                             "role": self.get_cookie('role', ""),
                             "result": "成功",
                             })
            new_logger.log(
                self.username, "快照管理", "创建虚拟机快照", "成功", role, "创建虚拟机快照: {},成功".format(instanceform.id)
            )
        except Exception as e:

                logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                            {"username": self.get_cookie('username', ""),
                             "op": "创建虚拟机快照",
                             "object": instanceform.id,
                             "role": self.get_cookie('role', ""),
                             "result": "失败",
                             })
                new_logger.log(
                    self.username, "快照管理", "创建虚拟机快照", "失败", role, "创建虚拟机快照: {},失败".format(instanceform.id)
                )
                return {"msg": "error"}
        return res

    # @post(_path="/v1/instance/action",_types=[InstanceActionFrom],_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    @post(_path="/v1/instance/action",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_server_action(self, instanceform):
        role = self.get_cookie('role', "")
        """
        ---
        tags:
          - 虚拟机相关接口
        summary: 虚拟机操作
        requestBody:
          description: 开机 关机 重启 重建
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RequestActionInstanceModel'
          required: true
        responses:
          '200':
            description: 操作成功
            content: {}
        """

        id = instanceform.get("id", "")
        name = instanceform.get("name", "")
        action = instanceform.get("action", "")
        client = Client()
        if action == "start":
            op = "虚拟机开机"
        elif action == "stop":
            op = "虚拟机关机"
        elif action == "reboot":
            op = "重启虚拟机"
        elif action == "resize":
            op = "虚拟机调整配置"

        elif action == "migrate":
            op = "虚拟机迁移"
        elif action == "live_migrate":
            op = "虚拟机热迁移"
        elif action == "confirm":
            op = "确认调整配置"
            detail = client.NovaClient.openstack_getpost_server_detail(client, id)

            flavor = client.FlavorClient.openstack_get_flavor_detail(client,detail["flavor"]["id"])

            extend_form = VolumeActionFrom()
            extend_form.action = "extend"
            extend_form.id = detail["volume_id"][0]
            extend_form.data = flavor["disk"]

            res = client.VolumeClient.openstack_volume_action(client,extend_form)

            print(flavor)
        elif action == "revert":
            op = "放弃调整配置"
        elif action == "restore":
            op = "还原虚拟机"
        elif action == "forceDelete":
            op = "强制删除虚拟机"
        elif action == "suspend":
            op = "挂起虚拟机"
        elif action == "resume":
            op = "取消挂起虚拟机"

        try:
            if action == "forceDelete":
                interface_list = client.NovaClient.openstack_get_server_ports(client, id)



            res = client.NovaClient.openstack_server_action_v2(client, instanceform)

            if action == "forceDelete":
                # 删除虚拟机后，删除这个虚机的全部网络端口
                for interface in interface_list:
                    try:
                        client.NeutronClient.openstack_delete_port(client, interface.get("port_id", ""))
                    except Exception as e:
                        print(e)
                        pass


            if action == "getVNCConsole" :
                if res["msg"] == "error":
                    action_form = VolumeActionFrom()
                    action_form.action = "getSPICEConsole"
                    action_form.id = id
                    action_form.data = ""
                    res = client.NovaClient.openstack_server_action(client, action_form)
                    return res
                else:
                    return res
            if action == "restore":
                is_operator = role == "operator"
                if is_operator:
                    with self.session_scope() as session:
                        user = session.query(User).filter_by(username=self.username).first()
                        if user:
                            session.expunge(user)

                            group = session.query(VmGroup).filter(
                                and_(VmGroup.user_id == user.id, VmGroup.pid == -1)).first()

                            vm = Vms()
                            vm.vmid = id
                            vm.user_id = user.id
                            vm.vmgroup_id = group.id
                            session.add(vm)

            if not op == "虚拟机调整配置":
                if not op == "确认调整配置":
                    new_logger.log(
                        self.username, "虚机操作", op, "成功", role, "{}: {},成功".format(op, name)
                    )
        except Exception as e:
                traceback.print_exc()
                if not op == "虚拟机调整配置":
                    if not op == "确认调整配置":
                        new_logger.log(
                            self.username, "虚机操作", op, "成功", role, "{}: {},成功".format(op, name)
                        )
                traceback.print_exc()
                self.set_status(502)
                return {"msg":"error"}
        return res


    @post(_path="/v2/instance/list/migrate", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_server_list_v2_migrate(self, form):
        """
        {
            "page": 1,
            "pagecount": 10,
            "search_str": "",
            "order_type": "desc",
            "order_by": ""
        }"""
        role = self.get_cookie('role', "")
        page = form.get("page", 10)
        pagecount = form.get("pagecount", 10)
        total = 0
        pages = 0
        try:
            client = Client()
            task_list = client.NovaClient.openstack_get_all_migrations_list(client)

            total = len(task_list)
            pages = total // pagecount

            client = Client()
            vm_list = client.NovaClient.openstack_get_all_instance_list_v2(client)
            ins_detail = client.NovaClient.openstack_get_softdeleted_instance_detail(client,"")


            for task in task_list:
                for vm in vm_list:
                    if task["instance_uuid"] == vm["id"]:
                        task["name"] = vm["name"]
                        break

                if not task.get("name"):
                    for vm in ins_detail:
                        if task["instance_uuid"] == vm["id"]:
                            task["name"] = vm["name"]
                            break

            #加入分页和搜索功能
            if form.get("search_str"):
                task_list = [task for task in task_list if form.get("search_str") in task["name"]]

            # if form.get("order_by"):
            #     task_list = sorted(task_list, key=lambda x: x[form.get("order_by")], reverse=True)
            # else:
            #     task_list = sorted(task_list, key=lambda x: x["created_at"], reverse=True)

            task_list = task_list[(page - 1) * pagecount: page * pagecount]

            return {
                "total": total,
                "pages": pages,
                "data": task_list
            }
        except Exception as e:
            traceback.print_exc()
            return {"msg":"error"}


    @post(_path="/v2/instance/action", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_server_action_v2(self, instanceform):
        role = self.get_cookie('role', "")
        fail_ids = []
        action = instanceform.get("action")
        data = instanceform.get("data")
        vm_ids = instanceform.get("ids", [])
        vm_names = instanceform.get("names", [])

        for i in range(len(vm_ids)):
            vm_id = vm_ids[i]
            vm_name = vm_names[i]

            client = Client()
            if action == "start":
                op = "虚拟机开机"
            elif action == "stop":
                op = "虚拟机关机"
            elif action == "reboot":
                op = "重启虚拟机"
            elif action == "resize":
                op = "虚拟机调整配置"

            elif action == "migrate":
                op = "虚拟机迁移"
            elif action == "live_migrate":
                op = "虚拟机热迁移"
            elif action == "confirm":
                op = "确认调整配置"
                detail = client.NovaClient.openstack_getpost_server_detail(client, vm_id)

                flavor = client.FlavorClient.openstack_get_flavor_detail(client,detail["flavor"]["id"])

                extend_form = VolumeActionFrom()
                extend_form.action = "extend"
                extend_form.id = detail["volume_id"][0]
                extend_form.data = flavor["disk"]

                res = client.VolumeClient.openstack_volume_action(client,extend_form)

                print(flavor)
            elif action == "revert":
                op = "放弃调整配置"
            elif action == "restore":
                op = "还原虚拟机"
            elif action == "forceDelete":
                op = "强制删除虚拟机"
            elif action == "suspend":
                op = "挂起虚拟机"
            elif action == "resume":
                op = "取消挂起虚拟机"
            elif action == "shelve":
                op = "废弃虚拟机"  # 搁置
            elif action == "unshelve":
                op = "取消废弃虚拟机"  #

            try:

                insform = InstanceActionFrom()
                insform.id = vm_id
                insform.action = action
                insform.data = data

                res = client.NovaClient.openstack_server_action(client, insform)
                if action == "getVNCConsole":
                    if res["msg"] == "error":
                        action_form = VolumeActionFrom()
                        action_form.action = "getSPICEConsole"
                        action_form.id = instanceform.id
                        action_form.data = ""
                        res = client.NovaClient.openstack_server_action(client, action_form)
                        return res
                    else:
                        return res
                new_logger.log(
                    self.username, "虚机操作", op, "成功", role, "{}: {},成功".format(op, vm_name)
                )
            except Exception as e:
                    fail_ids.append(vm_name)
                    new_logger.log(
                        self.username, "虚机操作", op, "失败", role, "{}: {},失败".format(op, vm_name)
                    )
                    traceback.print_exc()
                    self.set_status(502)

        if fail_ids:
            return {"msg":"异常的虚机名称: %s" % ",".join(fail_ids)}
        return {"msg": "ok"}

    @post(_path="/v3/instance/action", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_server_action_v3(self, form):
        """"
        tags:
          - 虚拟机批量配置接口
        summary: 虚拟机批量操作
        requestBody:
          description: resize
          content: application/json
          required: true
        responses:
          '200':
            description: 操作成功
            content: {}
        """
        role           = self.get_cookie('role', "")
        actions        = form.get("actions")
        admin_pass     = form.get("admin_pass")
        always_startup = form.get("always_startup")
        cpu_quota      = form.get("cpu_quota")
        cpu_shares     = form.get("cpu_shares")
        ids            = form.get("ids")
        names          = form.get("names")
        numa_nodes     = form.get("numa_nodes")
        vcpus          = form.get("vcpus")
        disk           = form.get("disk")
        ram            = form.get("ram")
        results        = []

        if not actions or not ids or not names:
            return [{"msg": "error", "err": "无效操作或虚机的ID和名称"}]
        if not vcpus and disk and ram:
            return [{"msg": "error", "err": "虚机类型缺省参数"}]
        if len(ids) != len(names):
            return [{"msg": "error", "err": "虚机ID和名称数量不一致"}]

        ser_action = ServerAction()
        ser_action.actions     = actions
        ser_action.always_startup = always_startup
        ser_action.cpu_quota = cpu_quota
        ser_action.cpu_shares = cpu_shares
        ser_action.disk        = disk
        ser_action.extdisk     = 0
        ser_action.fail_id     = ""
        ser_action.flavor_form = FlavorCreateFrom()
        ser_action.flavor_id   = ""
        ser_action.numa_nodes  = numa_nodes
        ser_action.ram         = ram
        ser_action.ser_form    = InstanceActionFrom()
        ser_action.vcpus       = vcpus
        ser_action.client      = Client()

        def op_log(res):
            res["op"] = ser_action.op
            results.append(res)
            if res["msg"] == "ok":
                new_logger.log(self.username, "虚机操作", ser_action.op, "成功", role,
                               "{}: {},成功".format(ser_action.op, ser_action.ser_name))
            else:
                new_logger.log(self.username, "虚机操作", ser_action.op, "失败", role,
                               "{}: {},失败".format(ser_action.op, ser_action.ser_name))

        for index, id in enumerate(ids):
            ser_action.ser_id   = id
            ser_action.ser_name = names[index]
            if "resize" in actions:
                ser_action.op = "虚拟机调整配置"
                ser_action.action = "resize"
                ser_action.get_flavor_id()
                op_log(ser_action.server_action())

            if "getSPICEConsole" in actions:
                ser_action.op = "虚拟机开启终端"
                ser_action.action = "getSPICEConsole"
                op_log(ser_action.server_action())

            if "changePassword" in actions:
                ser_action.op = "虚拟机重置密码"
                ser_action.admin_pass = admin_pass
                ser_action.action = "changePassword"

                op_log(ser_action.server_action())

            ser_action.update_extra_specs()

        return results

    @post(_path="/v3/instance/forcedelete", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def force_delete_instance(self, instanceform):
        msg = []
        msg.append("ok")
        role = self.get_cookie('role', "")
        fail_ids = []
        vm_ids = instanceform.get("ids", [])
        vm_names = instanceform.get("names", [])
        mac_addrs = instanceform.get("mac_addr", [])
        data = instanceform.get("data")

        for i in range(len(vm_ids)):
            vm_id = vm_ids[i]
            vm_name = vm_names[i]
            mac = mac_addrs[i]
            client = Client()
            try:
                for j in range(len(mac)):
                    mac_addr = mac[j]
                    interface = client.NeutronClient.openstack_get_all_filter_ports(client, f"mac_address={mac_addr}")
                    if not interface:
                        msg.append(",异常的网络端口：mac_addr：{}".format(mac_addr))
                    if interface and interface[0].id:
                        now_portid = interface[0].id
                        thread = threading.Thread(target=self.detach_and_delete, args=(client, instanceform.get("id", ""), now_portid, "", role))
                        thread.start()

                insform = InstanceActionFrom()
                insform.id = vm_id
                insform.action = "forceDelete"
                insform.data = data
                res = client.NovaClient.openstack_server_action(client, insform)
                with self.session_scope() as session:
                    session.query(VmDrs).filter(VmDrs.vm_id == vm_id).delete()
                    session.commit()

                log_msg = f"{self.get_cookie('username', '')};强制删除虚拟机;{vm_id};{role};成功"
                logger.info(log_msg)
                new_logger.log(self.username, "虚机操作", "强制删除虚拟机", "成功", role, f"强制删除虚拟机: {vm_name}, 成功")

            except Exception as e:
                fail_ids.append(vm_id)
                log_msg = f"{self.get_cookie('username', '')};强制删除虚拟机;{vm_id};{role};失败"
                logger.error(log_msg)
                new_logger.log(self.username, "虚机操作", "强制删除虚拟机", "失败", role, f"强制删除虚拟机: {vm_name}, 失败")

        if fail_ids:
            return {"msg": f"异常的虚机id: {','.join(fail_ids)}"}
        return {"msg": ",".join(msg)}

    @post(_path="/v1/server/iso", _consumes=mediatypes.APPLICATION_JSON , _produces=mediatypes.APPLICATION_JSON)
    def create_server_with_iso(self, instancecreatefrom):
        form = InstanceCreateFrom()
        form.flavorRef = instancecreatefrom["flavorRef"]
        form.name = instancecreatefrom["name"]
        form.imageRef = instancecreatefrom["imageRef"]
        form.count = 1
        form.availability_zone = instancecreatefrom["availability_zone"]
        form.networkRef = instancecreatefrom["networkRef"]

        form.uuid = instancecreatefrom["uuid"]
        client = Client()

        subnet_id = client.NeutronClient.openstack_get_subnet_from_network(client,instancecreatefrom["networkRef"] )
        port = client.NeutronClient.openstack_create_port(client,instancecreatefrom["networkRef"],instancecreatefrom["ipv4"],subnet_id)
        form.ipv4 = port

        ins = client.NovaClient.openstack_create_test(client, form)
        return ins

    @post(_path="/v2/server/iso", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def create_server_with_iso_v2(self, instancecreatefrom):
        form = InstanceCreateFrom()
        form.flavorRef = instancecreatefrom["flavorRef"]
        form.name = instancecreatefrom["name"]
        form.imageRef = instancecreatefrom["imageRef"]
        form.count = 1
        form.availability_zone = instancecreatefrom["availability_zone"]
        form.networkRef = instancecreatefrom["networkRef"]

        form.uuid = instancecreatefrom["uuid"]
        client = Client()

        # subnet_id = client.NeutronClient.openstack_get_subnet_from_network(client, instancecreatefrom["networkRef"])
        subnet_id = instancecreatefrom.get("subnet_id", "")
        port = client.NeutronClient.openstack_create_port(client, instancecreatefrom["networkRef"],
                                                          instancecreatefrom["ipv4"], subnet_id)
        form.ipv4 = port

        ins = client.NovaClient.openstack_create_test(client, form)
        return ins

    @post(_path="/v1/instance/detail",_types=[InstanceDeleteFrom],_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_server_detail(self, instanceform):
        """
        ---
        tags:
          - 虚拟机相关接口
        summary: 查询虚拟机详情
        requestBody:
          description: 编辑
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RequestEditInstanceModel'
          required: true
        responses:
          '200':
            description: 编辑成功
            content: {}
        """
        client = Client()
        res = client.NovaClient.openstack_getpost_server_detail(client, instanceform.id)

        if res["flavor"]:
            print(res["flavor"]["id"])
            flavor_res = client.FlavorClient.openstack_get_flavor_detail(client,res["flavor"]["id"])
            res["vcpus"] = flavor_res["vcpus"]
            res["ram"] = int(flavor_res["ram"]/1024)
            res["disk"] = flavor_res["disk"]
            #if hasattr(flavor_res, 'extra_specs'):
            res["extra_specs"] = flavor_res.get("extra_specs", {})
        if len(res["volume_id"])>0:
            res["attachment"] = []
            res["mountpoint"] = []
            res["mountname"] = []
            volume_detail = client.VolumeClient.openstack_get_volume_detail(client,res["volume_id"][0])
            if isinstance(res["image"], dict):
                imageIdbyres = res["image"].get("id", "")
            else:
                # 处理 res["image"] 是字符串的情况
                imageIdbyres = res["image"]
                print(imageIdbyres)
            #imageIdbyres = res["image"].get("id", "")
            imageId = volume_detail["volume_image_metadata"].get("image_id", imageIdbyres)
            if imageId == "":
                res["imagename"] = volume_detail["volume_image_metadata"].get("image_name", "")
            else:
                image_info = client.ImageClient.openstack_get_image_detail(client,imageId)
                res["imagename"] = image_info["name"]
            for volume in res["volume_id"]:
                volume_detail = client.VolumeClient.openstack_get_volume_detail(client,volume)
                if not volume_detail["name"]:
                    volume_detail["name"]  = volume_detail["id"][:8]
                res["attachment"].append( "%s 位于 %s 上" % (volume_detail["name"],volume_detail["mountpoint"]))
                res["mountname"].append( volume_detail["name"])
                res["mountpoint"].append(volume_detail["mountpoint"])
                res["volume_type"] = volume_detail["volume_type"]

        return res

    @post(_path="/v1/instance/unmountiso",_types=[InstanceDeleteFrom],_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_server_unmountiso(self, instanceform):
        """
        ---
        tags:
          - 虚拟机相关接口
        summary: 弹出虚拟机光盘
        requestBody:
          description: 弹出
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RequestEditInstanceModel'
          required: true
        responses:
          '200':
            description: 弹出成功
            content: {}
        """
        client = Client()

        #insnew = client.NovaClient.openstack_create_server_with_iso(client, instanceform  )

        detail = client.NovaClient.openstack_get_server_detail(client, instanceform.id)
        host = client.NovaClient.openstack_detach_volume_from_server(client,instanceform.id,detail["isovolumeid"])
        insnew = client.NovaClient.openstack_edit_iso_server(client,instanceform.id,"")

        return host

    @post(_path="/v2/instance/unmountiso",_types=[InstanceDeleteFrom],_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_server_unmountiso_v2(self, instanceform):

        try:
            client = Client()

            detail = client.NovaClient.openstack_get_server_detail_for_iso(client, instanceform.id)
            for iso_volume_id in detail["isovolumeids"]:
                client.NovaClient.openstack_detach_volume_from_server(client,instanceform.id,iso_volume_id)
            client.NovaClient.openstack_edit_iso_server(client,instanceform.id,"")
        except Exception as e:
            traceback.print_exc()
            return {"msg":"error"}

        return {"msg":"ok"}

    # @post(_path="/v1/instances/detach",_types=[InstanceDetachFrom],_consumes=mediatypes.APPLICATION_JSON ,  _produces=mediatypes.APPLICATION_JSON)
    @post(_path="/v1/instances/detach",_consumes=mediatypes.APPLICATION_JSON ,  _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_detach_volume_from_instance(self,form):
        """
        ---
        tags:
          - 虚拟机相关接口
        summary: 分离云硬盘
        responses:
          '200':
            description: 成功
            content: {}
        """
        role = self.get_cookie('role', "")
        vmid = form.get("vmid", "")
        volumeid = form.get("volumeid", "")
        vm_name = form.get("vm_name", "")
        volume_name = form.get("volume_name", "")
        try:
            client = Client()
            host = client.NovaClient.openstack_detach_volume_from_server(client, vmid, volumeid)

            result = client.VolumeClient.openstack_get_all_list(client)
            for volume in result:
                if volume["id"] == volumeid:
                    volume_name = volume["name"]
                    break

        # logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
            #                 {"username": self.get_cookie('username', ""),
            #                  "op": "分离虚拟机硬盘",
            #                  "object": "%s %s" % (form.vmid,form.volumeid),
            #                  "role": self.get_cookie('role', ""),
            #                  "result": "成功",
            #                  })
            new_logger.log(
                self.username,  "虚机管理", "分离云硬盘", "成功", role, "分离云硬盘: 虚拟机:{} 云硬盘:{}成功".format(vm_name, volume_name)
            )
        except Exception as e:

                # logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                #             {"username": self.get_cookie('username', ""),
                #              "op": "分离虚拟机硬盘",
                #              "object": "%s %s" % (form.vmid,form.volumeid),
                #              "role": self.get_cookie('role', ""),
                #              "result": "失败",
                #              })
                new_logger.log(
                    self.username, "虚机管理", "分离云硬盘", "失败", role, "分离云硬盘: 虚拟机:{} 云硬盘:{}失败".format(vm_name, volume_name)
                )
                traceback.print_exc()
                self.set_status(502)
                return {"msg": "error"}
        return host

    # @post(_path="/v1/instances/attach",_types=[InstanceDetachFrom],_consumes=mediatypes.APPLICATION_JSON ,  _produces=mediatypes.APPLICATION_JSON)
    @post(_path="/v1/instances/attach", _consumes=mediatypes.APPLICATION_JSON ,  _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_attach_volume_to_instance(self,form):
        """
        ---
        tags:
          - 虚拟机相关接口
        summary: 分离云硬盘
        responses:
          '200':
            description: 成功
            content: {}
        """
        role = self.get_cookie('role', "")
        vm_name = form.get("vm_name", "")
        volume_name = form.get("volume_name", "")
        try:
            client = Client()
            attach_form = InstanceAttachForm()
            attach_form.id = form.get("volumeid", "")
            attach_form.vmid = form.get("vmid", "")

            host = client.NovaClient.openstack_attach_volume_to_server(client,attach_form)
            # logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
            #                 {"username": self.get_cookie('username', ""),
            #                  "op": "挂载云硬盘到虚拟机",
            #                  "object": "%s %s" % (form.vmid,form.volumeid),
            #                  "role": self.get_cookie('role', ""),
            #                  "result": "成功",
            #                  })
            new_logger.log(
                self.username, "虚机管理", "挂载云硬盘到虚拟机", "成功", role, "挂载云硬盘: 虚拟机:{} 云硬盘:{}成功".format(vm_name, volume_name)
            )
        except Exception as e:

                # logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                #             {"username": self.get_cookie('username', ""),
                #              "op": "挂载云硬盘到虚拟机",
                #              "object": "%s %s" % (form.vmid,form.volumeid),
                #              "role": self.get_cookie('role', ""),
                #              "result": "失败",
                #              })
                new_logger.log(
                    self.username, "虚机管理", "挂载云硬盘到虚拟机", "失败", role, "挂载云硬盘: 虚拟机:{} 云硬盘:{}失败".format(vm_name, volume_name)
                )
                traceback.print_exc()
                self.set_status(502)
                return {"msg":"error"}
        return host

    @post(_path="/v2/instances/attach", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_attach_volume_to_instance_v2(self, form):
        """
        ---
        tags:
          - 虚拟机相关接口
        summary: 分离云硬盘
        responses:
          '200':
            description: 成功
            content: {}
        """
        role = self.get_cookie('role', "")
        vm_name = form.get("vm_name", "")
        volume_name = form.get("volume_name", "")
        volume_id = form.get("volumeid", "")
        device = form.get("device")
        try:
            client = Client()

            host = client.NovaClient.openstack_attach_volume_to_server_with_device_v2(client, form)
            # new_logger.log(
            #     self.username, "虚机管理", "挂载云硬盘到虚拟机", "成功", role,
            #     "挂载云硬盘: 虚拟机:{} 云硬盘:{}成功".format(vm_name, volume_name)
            # )
        except Exception as e:
            # new_logger.log(
            #     self.username, "虚机管理", "挂载云硬盘到虚拟机", "失败", role,
            #     "挂载云硬盘: 虚拟机:{} 云硬盘:{}失败".format(vm_name, volume_name)
            # )
            traceback.print_exc()
            self.set_status(502)
            return {"msg": "error"}
        return host

    @post(_path="/v1/instances/update/attach", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_update_attach_volume_to_instance(self, form):
        """
        ---
        tags:
          - 虚拟机相关接口
        summary: 分离云硬盘
        responses:
          '200':
            description: 成功
            content: {}
        """
        role = self.get_cookie('role', "")
        vm_name = form.get("vm_name", "")
        volume_name = form.get("volume_name", "")
        volume_id = form.get("volumeid", "")
        device = form.get("device")
        try:
            client = Client()

            host = client.NovaClient.openstack_update_attach_volume_to_server_with_device(client, form)
            # new_logger.log(
            #     self.username, "虚机管理", "挂载云硬盘到虚拟机", "成功", role,
            #     "挂载云硬盘: 虚拟机:{} 云硬盘:{}成功".format(vm_name, volume_name)
            # )
        except Exception as e:
            # new_logger.log(
            #     self.username, "虚机管理", "挂载云硬盘到虚拟机", "失败", role,
            #     "挂载云硬盘: 虚拟机:{} 云硬盘:{}失败".format(vm_name, volume_name)
            # )
            traceback.print_exc()
            self.set_status(502)
            return {"msg": "error"}
        return host

    @get(_path="/v1/instances/services",  _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_all_compute_services(self):
        """
        ---
        tags:
          - 虚拟机相关接口
        summary: 查询计算服务列表
        responses:
          '200':
            description: 成功
            content: {}
        """
        client = Client()
        host = client.NovaClient.openstack_get_all_compute_services(client)
        return host


    @post(_path="/v1/instances/cleanup",   _consumes=mediatypes.APPLICATION_JSON,  _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_cleanup_server_list(self,form):
        """
        ---
        tags:
          - 虚拟机相关接口
        summary: 查询计算服务列表
        responses:
          '200':
            description: 成功
            content: {}
        """
        order_by = form.get("order_by", "")
        order_type = form.get("order_type", "desc")
        search_str = form.get("search_str", "")
        client = Client()
        host = client.NovaClient.openstack_get_cleanup_list(client,form["shutdownTime"])
        data_list = host

        if order_type == "desc" and order_by:
            data_list = sorted(data_list, key = lambda x:x[order_by] , reverse=True)
        if order_type == "asc" and order_by:
            data_list = sorted(data_list, key = lambda x:x[order_by] , reverse=False)

        data = []
        for ins_detail in data_list:
            if search_str:
                if search_str in ins_detail["name"]  or search_str in ins_detail["ip"]:
                    data.append(ins_detail)
            else:
                data.append(ins_detail)

        obj = Pagenation(data,form.get("page", 1),form.get("pagecount", 10))
        server_list = obj.show()

        r = {
            "total": len(data),
            "pages": obj.total(),
            "data": server_list
            }

        return r

    @post(_path="/v1/instance/changeip",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_get_server_ports(self, form):
        role = self.get_cookie('role', "")
        client = Client()
        interface_list = client.NovaClient.openstack_get_server_ports(client,form.get("id", ""))

        now_portid,now_netid = "",""

        for interface in interface_list:
            if form["now_ipv4"] == interface["fixed_ips"][0]["ip_address"]:
                now_netid = interface["net_id"]
                now_portid = interface["port_id"]

        port_list= client.NeutronClient.openstack_get_all_ports(client)
        for port  in port_list:
            port_detail = from_dict(data_class=Ports, data=port)
            #if ins.__dict__["status"] == "ACTIVE":
            if port_detail.__dict__["fixed_ips"][0]["ip_address"] == form["new_ipv4"] :
                if port_detail.__dict__["status"] == "DOWN":
                    detach = client.NovaClient.openstack_detach_interface(client,form.get("id", ""),now_portid)
                    time.sleep(5)
                    new_list = client.NovaClient.openstack_get_server_ports(client,form.get("id", ""))
                    if len(new_list) < len(interface_list):
                        create_interface = client.NovaClient.openstack_create_interface(client,form["id"], port_detail.__dict__["id"])
                        new_logger.log(
                            self.username, "虚机管理", "修改IP", "成功", role, "修改ip: 旧ip:{} 新ip:{}成功".format(now_portid, form["new_ipv4"] )
                        )
                        return {"msg": "修改成功"}
                    else:
                        new_logger.log(
                            self.username, "虚机管理", "修改IP", "失败", role, "修改ip: 旧ip:{} 新ip:{}失败".format(now_portid, form["new_ipv4"])
                        )
                        return {"msg": "修改失败，当前IP无法分离，请检查虚拟机是否正常"}
                else :
                    new_logger.log(
                        self.username, "虚机管理", "修改IP", "失败", role, "修改ip: 旧ip:{} 新ip:{}失败".format(now_portid, form["new_ipv4"])
                    )
                    return {"msg": "IP地址已占用"}

        subnet_id = client.NeutronClient.openstack_get_subnet_from_network(client,now_netid )
        port = client.NeutronClient.openstack_create_port(client,now_netid,form["new_ipv4"],subnet_id)

        detach = client.NovaClient.openstack_detach_interface(client,form.get("id", ""),now_portid)
        time.sleep(5)
        new_list = client.NovaClient.openstack_get_server_ports(client,form.get("id", ""))
        if len(new_list) < len(interface_list):
            create_interface = client.NovaClient.openstack_create_interface(client,form["id"], port)
            new_logger.log(
                self.username, "虚机管理", "修改IP", "成功", role, "修改ip: 旧ip:{} 新ip:{}成功".format(now_portid, form["new_ipv4"])
            )
            return {"msg": "修改成功"}
        else:
            new_logger.log(
                self.username, "虚机管理", "修改IP", "失败", role, "修改ip: 旧ip:{} 新ip:{}失败".format(now_portid, form["new_ipv4"])
            )
            return {"msg": "修改失败，旧IP无法分离，请检查虚拟机是否正常"}

    @post(_path="/v2/instance/changeip",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_change_server_ips(self, form):
        role = self.get_cookie('role', "")
        client = Client()
        interface_list = client.NovaClient.openstack_get_server_ports(client,form.get("id", ""))

        now_portid,now_netid = "",""

        for interface in interface_list:
            if form["now_ipv4"] == interface["fixed_ips"][0]["ip_address"]:
                now_netid = interface["net_id"]
                now_portid = interface["port_id"]

        port_list= client.NeutronClient.openstack_get_all_ports(client)
        for port  in port_list:
            port_detail = from_dict(data_class=Ports, data=port)
            #if ins.__dict__["status"] == "ACTIVE":
            if port_detail.__dict__["fixed_ips"][0]["ip_address"] == form["new_ipv4"] :
                if port_detail.__dict__["status"] == "DOWN" or port_detail.__dict__["status"] == "ACTIVE":
                    new_logger.log(
                        self.username, "虚机管理", "修改IP", "失败", role, "修改ip: 旧ip:{} 新ip:{}失败".format(now_portid, form["new_ipv4"])
                    )
                    return {"msg": "IP地址已占用"}
                else :
                    # detach = client.NovaClient.openstack_detach_interface(client,form.get("id", ""),now_portid)
                    # time.sleep(5)
                    new_list = client.NovaClient.openstack_get_server_ports(client,form.get("id", ""))
                    if len(new_list) < len(interface_list):
                        create_interface = client.NovaClient.openstack_create_interface(client,form["id"], port_detail.__dict__["id"])
                        if create_interface["msg"] == "409":
                            new_logger.log(
                                self.username, "虚机管理", "修改IP", "失败", role, "修改ip: 旧ip:{} 新ip:{}失败".format(now_portid, form["new_ipv4"])
                            )
                            return {"msg":"IP地址已占用"}
                        try:
                            client.NeutronClient.openstack_delete_port(client, now_portid)
                        except Exception as e:
                            print(e)
                        new_logger.log(
                            self.username, "虚机管理", "修改IP", "成功", role, "修改ip: 旧ip:{} 新ip:{}成功".format(now_portid, form["new_ipv4"])
                        )
                        return {"msg":"修改成功"}
                    else:
                        new_logger.log(
                            self.username, "虚机管理", "修改IP", "失败", role, "修改ip: 旧ip:{} 新ip:{}失败".format(now_portid, form["new_ipv4"])
                        )
                        return {"msg":"修改失败，当前IP无法分离，请检查虚拟机是否正常"}


        subnet_id = client.NeutronClient.openstack_get_subnet_from_network(client,form["new_netid"] )
        port = client.NeutronClient.openstack_create_port(client,form["new_netid"],form["new_ipv4"],subnet_id)

        detach = client.NovaClient.openstack_detach_interface(client,form.get("id", ""),now_portid)
        time.sleep(5)
        new_list = client.NovaClient.openstack_get_server_ports(client,form.get("id", ""))
        if len(new_list) < len(interface_list):
            create_interface = client.NovaClient.openstack_create_interface(client,form["id"], port)
            if create_interface["msg"] == "200":
                try:
                    client.NeutronClient.openstack_delete_port(client, now_portid)
                except Exception as e:
                    print(e)
                new_logger.log(
                    self.username, "虚机管理", "修改IP", "成功", role, "修改ip: 旧ip:{} 新ip:{}成功".format(now_portid, form["new_ipv4"])
                )
                return {"msg": "修改成功"}
            else:
                new_logger.log(
                    self.username, "虚机管理", "修改IP", "失败", role, "修改ip: 旧ip:{} 新ip:{}失败".format(now_portid, form["new_ipv4"])
                )
                return {"msg": "修改失败"}
        else:
            new_logger.log(
                self.username, "虚机管理", "修改IP", "失败", role, "修改ip: 旧ip:{} 新ip:{}失败".format(now_portid, form["new_ipv4"])
            )
            return {"msg":"修改失败，旧IP无法分离，请检查虚拟机是否正常"}

    @post(_path="/v3/instance/changeip",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_change_server_ips_v3(self, form):
        role = self.get_cookie('role', "")
        client = Client()
        interface_list = client.NovaClient.openstack_get_server_ports(client,form.get("id", ""))

        now_portid,now_netid = "",""

        for interface in interface_list:
            if form["now_ipv4"] == interface["fixed_ips"][0]["ip_address"]:
                now_netid = interface["net_id"]
                now_portid = interface["port_id"]

        port_list= client.NeutronClient.openstack_get_all_ports(client)
        for port  in port_list:
            port_detail = from_dict(data_class=Ports, data=port)
            #if ins.__dict__["status"] == "ACTIVE":
            if port_detail.__dict__["fixed_ips"][0]["ip_address"] == form["new_ipv4"] :
                if port_detail.__dict__["status"] == "DOWN" or port_detail.__dict__["status"] == "ACTIVE":
                    new_logger.log(
                        self.username, "虚机管理", "修改IP", "失败", role, "修改ip: 旧ip:{} 新ip:{}失败".format(now_portid, form["new_ipv4"])
                    )
                    return {"msg":"IP地址已占用"}
                else :
                    # detach = client.NovaClient.openstack_detach_interface(client,form.get("id", ""),now_portid)
                    # time.sleep(5)
                    new_list = client.NovaClient.openstack_get_server_ports(client,form.get("id", ""))
                    if len(new_list) < len(interface_list):
                        create_interface = client.NovaClient.openstack_create_interface(client,form["id"], port_detail.__dict__["id"])
                        if create_interface["msg"] == "409":
                            new_logger.log(
                                self.username, "虚机管理", "修改IP", "失败", role, "修改ip: 旧ip:{} 新ip:{}失败".format(now_portid, form["new_ipv4"])
                            )
                            return {"msg": "IP地址已占用"}
                        try:
                            client.NeutronClient.openstack_delete_port(client, now_portid)
                        except Exception as e:
                            print(e)
                        new_logger.log(
                            self.username, "虚机管理", "修改IP", "成功", role, "修改ip: 旧ip:{} 新ip:{}成功".format(now_portid, form["new_ipv4"])
                        )
                        return {"msg": "修改成功"}
                    else:
                        new_logger.log(
                            self.username, "虚机管理", "修改IP", "失败", role, "修改ip: 旧ip:{} 新ip:{}失败".format(now_portid, form["new_ipv4"])
                        )
                        return {"msg": "修改失败，当前IP无法分离，请检查虚拟机是否正常"}


        # subnet_id = client.NeutronClient.openstack_get_subnet_from_network(client,form["new_netid"] )
        subnet_id = form.get("subnet_id", "")
        port = client.NeutronClient.openstack_create_port(client,form["new_netid"],form["new_ipv4"],subnet_id)

        detach = client.NovaClient.openstack_detach_interface(client,form.get("id", ""),now_portid)
        time.sleep(5)
        new_list = client.NovaClient.openstack_get_server_ports(client,form.get("id", ""))
        if len(new_list) < len(interface_list):
            create_interface = client.NovaClient.openstack_create_interface(client,form["id"], port)
            if create_interface["msg"] == "200":
                try:
                    client.NeutronClient.openstack_delete_port(client, now_portid)
                except Exception as e:
                    print(e)
                new_logger.log(
                    self.username, "虚机管理", "修改IP", "成功", role, "修改ip: 旧ip:{} 新ip:{}成功".format(now_portid, form["new_ipv4"])
                )
                return {"msg": "修改成功"}
            else:
                new_logger.log(
                    self.username, "虚机管理", "修改IP", "失败", role, "修改ip: 旧ip:{} 新ip:{}失败".format(now_portid, form["new_ipv4"])
                )
                return {"msg": "修改失败"}
        else:
            new_logger.log(
                self.username, "虚机管理", "修改IP", "失败", role, "修改ip: 旧ip:{} 新ip:{}失败".format(now_portid, form["new_ipv4"])
            )
            return {"msg": "修改失败，旧IP无法分离，请检查虚拟机是否正常"}

    @post(_path="/v1/instance/checkip",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_check_ip(self, form):
        # 检测自身IP是否重复
        if form["now_ipv4"] == form["new_ipv4"]:
            return {"msg": "新旧IP地址相同，请重新输入"}

        client = Client()
        port_list = client.NeutronClient.openstack_get_all_ports(client)
        for port in port_list:
            port_detail = from_dict(data_class=Ports, data=port)
            if port_detail.fixed_ips[0]["ip_address"] == form["new_ipv4"]:
                if port_detail.status == "ACTIVE":
                    return {"msg": "IP地址已占用"}
        return {"msg": "ok"}


    @post(_path="/v4/instance/changeip",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_change_server_ips_v4(self, form):
        msg = []
        msg.append("修改成功")
        role = self.get_cookie('role', "")
        user_name = self.get_cookie('username', "")
        name = form.get("name", "")
        client = Client()
        interface = client.NeutronClient.openstack_get_all_filter_ports(client, "mac_address={}".format(form.get("mac_addr", "")))
        if not interface:
            msg.append(",异常的网络端口：mac_addr：{}".format(format(form.get("mac_addr", ""))))
        if interface and interface[0].id:
            now_portid = interface[0].id
            thread = threading.Thread(target=self.detach_and_delete, args=(client, form.get("id", ""), now_portid, form["new_ipv4"], role))
            thread.start()
        subnet_id = form.get("subnet_id", "")
        port = client.NeutronClient.openstack_create_port(client, form["new_netid"], form["new_ipv4"], subnet_id)
        create_interface = client.NovaClient.openstack_create_interface(client, form["id"], port)
        if create_interface["msg"] == "200":
            new_logger.log(
                self.username, "虚机管理", "修改IP", "成功", role,
                "修改虚拟机{} ip: 旧ip:{} 新ip:{}成功".format(name, form["now_ipv4"], form["new_ipv4"])
            )
            return {"msg": ",".join(msg)}
        # TODO 没有异常情况

    def detach_and_delete(self, client, form_id, now_portid, new_ipv4, role):
        try:
            client.NovaClient.openstack_detach_interface(client, form_id, now_portid)
            time.sleep(7)
            client.NeutronClient.openstack_delete_port(client, now_portid)
        except Exception as e:
            print(e)
            print(
                "虚机管理", "修改IP", "失败", role, "异步删除网络：{} 失败".format(new_ipv4)
            )

    @post(_path="/v1/instance/changemac",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_change_server_mac_v1(self, form):
        role            = self.get_cookie("role", None)
        vm_name         = form.get("name", None)
        server_id       = form.get("id", None)
        now_mac_address = form.get("now_mac", None)
        new_mac_address = form.get("new_mac", None)

        if (vm_name is None
            and server_id is None
            and now_mac_address is None
            and new_mac_address is None):
            return {"msg": "缺少参数"}

        msg = [
            ["虚机管理", "修改mac", "成功"],
            ["虚机管理", "修改mac", "失败"],
            "虚拟机\"{}\"端口\"{}\"不存在".format(vm_name, now_mac_address),
            "虚拟机\"{}\"端口\"{}\"分离失败".format(vm_name, now_mac_address),
            "虚拟机\"{}\"端口\"{}\"连接失败".format(vm_name, now_mac_address),
            "虚拟机\"{}\"修改mac: \"{}\"失败".format(vm_name, now_mac_address),
            "虚拟机\"{}\"修改mac: \"{}\"成功".format(vm_name, new_mac_address)
        ]

        retries = 0
        port = {}
        client  = Client()

        while True:
            res = client.NeutronClient.openstack_get_all_filter_ports_v2(client, "mac_address={}".format(now_mac_address))
            if not res:
                return {"msg": msg[2]}
            port = res[0]
            if port.device_owner == "":
                break

            if retries == 0 and port.device_owner != "":
                res = client.NovaClient.openstack_detach_interface(client, server_id, port.id)
                if res == "error":
                    new_logger.log(self.username, *msg[1], role, msg[3])
                    return {"msg": msg[3]}

            if retries > 3:
                return {"msg": msg[3]}
            retries += 1
            time.sleep(1)

        res = client.NeutronClient.openstack_edit_port(client, port, new_mac_address)
        if res != "ok":
            new_logger.log(self.username, *msg[1], role, msg[5])
            return {"msg": msg[5]}

        res = client.NovaClient.openstack_create_interface(client, server_id, port.id)
        if res["msg"] == "error":
            new_logger.log(self.username, *msg[1], role, msg[4])
            return {"msg": msg[4]}

        res = client.NeutronClient.openstack_add_port_address_pairs(client, port, new_mac_address)
        new_logger.log(self.username, *msg[0], role, msg[6])

        return {"msg": "ok"}

    @post(_path="/v1/instance/addnetport",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_add_server_netport(self, form):
        role = self.get_cookie('role', "")
        client = Client()
        interface_list = client.NovaClient.openstack_get_server_ports(client,form.get("id", ""))

        port_list= client.NeutronClient.openstack_get_all_ports(client)

        for port  in port_list:
            port_detail = from_dict(data_class=Ports, data=port)
            #if ins.__dict__["status"] == "ACTIVE":
            if port_detail.__dict__["fixed_ips"][0]["ip_address"] == form["new_ipv4"] :
                if port_detail.__dict__["status"] == "DOWN":

                    create_interface = client.NovaClient.openstack_create_interface(client,form["id"], port_detail.__dict__["id"])
                    if create_interface["msg"] == "200":
                        new_logger.log(
                            self.username, "虚机管理", "增加IP", "成功", role, "增加ip: {}成功".format(form["new_ipv4"])
                        )
                        return {"msg": "添加成功"}
                    else:
                        new_logger.log(
                            self.username, "虚机管理", "增加IP", "失败", role, "增加ip: {}失败".format(form["new_ipv4"])
                        )
                        return {"msg": "添加失败"}
                else :
                    new_logger.log(
                        self.username, "虚机管理", "增加IP", "失败", role, "增加ip: {}失败".format(form["new_ipv4"])
                    )
                    return {"msg": "IP地址已占用"}

        subnet_id = client.NeutronClient.openstack_get_subnet_from_network(client,form["new_netid"] )
        if len(form["new_ipv4"])  >0 :
            port = client.NeutronClient.openstack_create_port(client,form["new_netid"],form["new_ipv4"],subnet_id)
        else:
            port = client.NeutronClient.openstack_create_port_withoutip(client,form["new_netid"],subnet_id,form["new_netid"][:8])

        create_interface = client.NovaClient.openstack_create_interface(client,form["id"], port)
        if create_interface["msg"] == "200":
            new_logger.log(
                self.username, "虚机管理", "增加IP", "成功", role, "增加ip: {}成功".format(form["new_ipv4"])
            )
            return {"msg":"添加成功"}
        else:
            new_logger.log(
                self.username, "虚机管理", "增加IP", "失败", role, "增加ip: {}失败".format(form["new_ipv4"])
            )
            return {"msg":"添加失败"}

    @post(_path="/v2/instance/addnetport", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_add_server_netport_v2(self, form):
        role = self.get_cookie('role', "")
        user_name = self.get_cookie('username', "")
        client = Client()
        interface_list = client.NovaClient.openstack_get_server_ports(client, form.get("id", ""))

        port_list = client.NeutronClient.openstack_get_all_ports(client)

        name = form.get("name", "")

        for port in port_list:
            port_detail = from_dict(data_class=Ports, data=port)
            # if ins.__dict__["status"] == "ACTIVE":
            if port_detail.__dict__["fixed_ips"][0]["ip_address"] == form["new_ipv4"]:
                if port_detail.__dict__["status"] == "DOWN":

                    create_interface = client.NovaClient.openstack_create_interface(client, form["id"],
                                                                                    port_detail.__dict__["id"])
                    if create_interface["msg"] == "200":
                        new_logger.log(
                            self.username, "虚机管理", "增加IP", "成功", role, "虚拟机:{} 增加ip: {}成功".format(name, form["new_ipv4"])
                        )
                        return {"msg": "添加成功"}
                    else:
                        new_logger.log(
                            self.username, "虚机管理", "增加IP", "失败", role, "虚拟机:{} 增加ip: {}失败".format(name, form["new_ipv4"])
                        )
                        return {"msg": "添加失败"}
                else:
                    new_logger.log(
                        self.username, "虚机管理", "增加IP", "失败", role, "虚拟机:{} 增加ip: {}失败".format(name, form["new_ipv4"])
                    )
                    return {"msg": "IP地址已占用"}

        # subnet_id = client.NeutronClient.openstack_get_subnet_from_network(client, form["new_netid"])
        subnet_id = form.get("subnet_id", "")
        if len(form["new_ipv4"]) > 0:
            port = client.NeutronClient.openstack_create_port(client, form["new_netid"], form["new_ipv4"], subnet_id)
        else:
            port = client.NeutronClient.openstack_create_port_withoutip(client, form["new_netid"], subnet_id,
                                                                        form["new_netid"][:8])

        create_interface = client.NovaClient.openstack_create_interface(client, form["id"], port)
        if create_interface["msg"] == "200":
            new_logger.log(
                user_name, "虚机管理", "增加IP", "成功", role, "{}增加ip: {}成功".format(user_name, form["new_ipv4"])
            )
            return {"msg": "添加成功"}
        else:
            new_logger.log(
                user_name,  "虚机管理", "增加IP", "失败", role, "{}增加ip: {}失败".format(user_name, form["new_ipv4"])
            )
            return {"msg": "添加失败"}

    @delete(_path="/v1/instance/deletenetport",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_del_server_port(self, form):
        role = self.get_cookie('role', "")
        user_name = self.get_cookie('username', "")

        name = form.get("name", "")
        client = Client()
        interface_list = client.NovaClient.openstack_get_server_ports(client,form.get("id", ""))

        now_portid,now_netid = "",""
        for interface in interface_list:
            if form["ipv4"] == interface["fixed_ips"][0]["ip_address"]:
                now_netid = interface["net_id"]
                now_portid = interface["port_id"]

        detach = client.NovaClient.openstack_detach_interface(client,form.get("id", ""),now_portid)
        del1 = client.NeutronClient.openstack_delete_port(client, now_portid)
        time.sleep(5)
        new_list = client.NovaClient.openstack_get_server_ports(client,form.get("id", ""))
        if len(new_list) < len(interface_list):
            new_logger.log(
                self.username, "虚机管理", "删除IP", "成功", role, "虚拟机：{} 删除ip: {}成功".format(name, form["ipv4"])
            )
            return {"msg": "删除成功"}
        else:
            new_logger.log(
                self.username, "虚机管理", "删除IP", "失败", role, "虚拟机：{} 删除ip: {}失败".format(name, form["ipv4"])
            )
            return {"msg": "删除失败，当前IP无法分离，请检查虚拟机是否正常"}

    @put(_path="/v1/modify/metadata",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def modify_metadata(self, form):
        role = self.get_cookie('role', "")
        user_name = self.get_cookie('username', "")
        vm_name = form.get("vm_name", "")
        try:
            client = Client()
            res = client.NovaClient.openstack_put_metadata(client, form)
            new_logger.log(
                self.username, "虚机管理", "切换系统类型", "成功", role, "切换虚拟机：{}系统类型: {}成功".format(vm_name, form["os_type"])
            )
        except Exception as e:
            new_logger.log(
                self.username, "虚机管理", "切换系统类型", "失败", role, "切换虚拟机：{}系统类型: {}成功".format(vm_name, form["os_type"])
            )
            self.set_status(502)
            return {"msg": "error"}
        return res

    # vm虚机获取所有tag列表
    @get(_path="/v1/instance/tag/list", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def instance_get_tag_list(self):
        vm_id = self.get_query_argument('vm_id', default=None)
        client = Client()
        res = client.NovaClient.openstack_get_vm_tags(client, vm_id)
        return res

    # vm虚机增加tag
    @put(_path="/v1/instance/set/tag",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def instance_add_tag(self, form):
        client = Client()
        res = client.NovaClient.openstack_set_vm_single_tag(client, form)
        return res

    # vm虚机替换tag
    @put(_path="/v1/instance/replace/tag", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def instance_replace_tag(self, form):
        client = Client()
        res = client.NovaClient.openstack_replace_vm_single_tag(client, form)
        return res

    # vm虚机删除单个tag
    @delete(_path="/v1/instance/delete/single/tag", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def instance_delete_single_tag(self, form):
        client = Client()
        res = client.NovaClient.openstack_delete_vm_single_tag(client, form)
        return res

    # 虚机废弃
    @post(_path="/v1/instance/shelve", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_server_shelve(self, instanceform):
        role = self.get_cookie('role', "")
        fail_ids = []
        vm_ids = instanceform.get("ids", [])
        vm_names = instanceform.get("names", [])

        for i in range(len(vm_ids)):
            vm_id = vm_ids[i]
            vm_name = vm_names[i]

            client = Client()

            try:
                res = client.NovaClient.openstack_server_shelve(client, vm_id)
                print(res)
                new_logger.log(
                    self.username, "虚机操作", "废弃虚拟机", "成功", role, "废弃虚拟机: {},成功".format(vm_name)
                )
            except Exception as e:
                fail_ids.append(vm_name)
                new_logger.log(
                    self.username, "虚机操作", "废弃虚拟机", "失败", role, "废弃虚拟机: {},失败".format(vm_name)
                )
                traceback.print_exc()
                self.set_status(502)

        if fail_ids:
            return {"msg": "异常的虚机名称: %s" % ",".join(fail_ids)}
        return {"msg": "ok"}

    # 虚机取消废弃
    @post(_path="/v1/instance/unshelve", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_server_unshelve(self, instanceform):
        role = self.get_cookie('role', "")
        fail_ids = []
        vm_ids = instanceform.get("ids", [])
        vm_names = instanceform.get("names", [])

        for i in range(len(vm_ids)):
            vm_id = vm_ids[i]
            vm_name = vm_names[i]

            client = Client()

            try:
                res = client.NovaClient.openstack_server_unshelve(client, vm_id)
                print(res)
                new_logger.log(
                    self.username, "虚机操作", "取消废弃虚拟机", "成功", role, "取消废弃虚拟机: {},成功".format(vm_name)
                )
            except Exception as e:
                fail_ids.append(vm_name)
                new_logger.log(
                    self.username, "虚机操作", "取消废弃虚拟机", "失败", role, "取消废弃虚拟机: {},失败".format(vm_name)
                )
                traceback.print_exc()
                self.set_status(502)

        if fail_ids:
            return {"msg": "异常的虚机名称: %s" % ",".join(fail_ids)}
        return {"msg": "ok"}

    # 设置白名单
    @post(_path="/v1/instance/whitelist", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_vm_whitelist(self, whiteform):
        role = self.get_cookie('role', "")
        fail_names = []
        vm_ids = whiteform.get("ids", [])
        vm_names = whiteform.get("names", [])
        ha_status = whiteform.get("ha_status", False)

        for i in range(len(vm_ids)):
            vm_id = vm_ids[i]
            vm_name = vm_names[i]

            if ha_status:
                try:
                    with self.session_scope() as session:
                        vm_whitelist = VmWhitelist(
                            vm_id=vm_id,
                        )
                    session.add(vm_whitelist)
                    session.commit()
                    new_logger.log(
                        self.username, "虚机操作", "设置白名单", "成功", role, "设置白名单: {},成功".format(vm_name)
                    )
                except Exception as e:
                    fail_names.append(vm_name)
                    new_logger.log(
                        self.username, "虚机操作", "设置白名单", "失败", role, "设置白名单: {},失败".format(vm_name)
                    )
                    traceback.print_exc()
                    self.set_status(502)
            else:
                try:
                    with self.session_scope() as session:
                        # 从数据库中查询实例
                        vm_whitelist = session.query(VmWhitelist).filter_by(vm_id=vm_id).first()

                        # 检查实例是否存在
                        if vm_whitelist:
                            session.delete(vm_whitelist)
                            session.commit()
                    new_logger.log(
                        self.username, "虚机操作", "取消白名单", "成功", role, "取消白名单: {},成功".format(vm_name)
                    )
                except Exception as e:
                    fail_names.append(vm_name)
                    new_logger.log(
                        self.username, "虚机操作", "取消白名单", "失败", role, "取消白名单: {},失败".format(vm_name)
                    )
                    traceback.print_exc()
                    self.set_status(502)

        if fail_names:
            return {"msg": "异常的虚机名称: %s" % ",".join(fail_names)}
        return {"msg": "ok"}
