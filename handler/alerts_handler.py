# -*- coding: utf-8 -*-

import tornado.ioloop
import pyrestful.rest
from api.log.log import <PERSON><PERSON>ogger

from pyrestful import mediatypes
from pyrestful.rest import get, post, put, delete

from model.instances import Clusters, Instances
from model.user import UserForm, UserStatusForm
from util.cov import todict, serialize, theevent
from api.openstack.client import Client
from api.prometheus.client_alert import AlertClient
from tornado_swagger.components import components
from mydoc.users import *
from db.model.user import User
from db.model.vm import VmGroup , Vms
from sqlalchemy import and_
from db.model.event import Event
import bcrypt
from sqlalchemy.orm import defer
from sqlalchemy.orm import undefer

new_logger = CustomLogger()

class AlertManagerHandler(pyrestful.rest.RestHandler):
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        #self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, PUT, GET, DELETE, OPTIONS')
        #self.set_header("Access-Control-Allow-Headers", "access-control-allow-origin,authorization,content-type")

    @get(_path="/v2/alerts/all", _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_all_alerts(self):

        client = AlertClient()
        res = client.get_alert_manager_alert()
        return res
    
    
    @post(_path="/v2/alerts/add/silences", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_add_alert_manager_silences(self, form):
        ids = form.get("ids", [])
        role = self.get_cookie('role', "")
        try:
            client = AlertClient()
            client.add_alert_manager_silences_fingerprint(role, ids)
            new_logger.log(
                self.username, "集群告警", "忽略集群告警", "成功", role, "{}:{},成功".format("忽略集群告警", form.get("names", ""))
            )
        except Exception as e:
            new_logger.log(
                self.username, "集群告警", "忽略集群告警", "失败", role, "{}:{},失败".format("忽略集群告警", form.get("names", ""))
            )
            self.set_status(502)
            return {"msg": "error"}
        return {"msg": "ok"}
    

    @get(_path="/v2/alerts/category", _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_all_alerts_category(self):

        client = AlertClient()
        res = client.get_alert_manager_alert()
        critical = []
        warning = []
        for cate in res:
            if cate["severity"] == "critical":
                critical.append(cate)
            if cate["severity"] == "warning":
                warning.append(cate)
        
        
        return {"critical":critical, "warning":warning}


class AlertCephHandler(pyrestful.rest.RestHandler):
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        # self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, PUT, GET, DELETE, OPTIONS')
        # self.set_header("Access-Control-Allow-Headers", "access-control-allow-origin,authorization,content-type")

    @get(_path="/v2/alerts/ceph", _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_all_alerts(self):

        client = AlertClient()
        res = client.get_alert_ceph_alert()
        return res

    @post(_path="/v2/alerts/add/silencesceph", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_add_alert_ceph_silences(self, form):

        ids = form.get("ids", [])
        names = form.get("names", [])
        role = self.get_cookie('role', "")
        try:
            client = AlertClient()
            client.add_alert_ceph_silences_fingerprint(ids, role)
            new_logger.log(
                self.username, "存储告警", "忽略存储告警", "成功", role, "{}:{},成功".format("忽略存储告警", names)
            )
        except Exception as e:
            new_logger.log(
                self.username, "存储告警", "忽略存储告警", "失败", role, "{}:{},失败".format("忽略存储告警", names)
            )
            self.set_status(502)
            return {"msg": "error"}
        return {"msg": "ok"}

class AlertsHandler(pyrestful.rest.RestHandler):
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        #self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, PUT, GET, DELETE, OPTIONS')
        #self.set_header("Access-Control-Allow-Headers", "access-control-allow-origin,authorization,content-type")

    @get(_path="/v1/alerts/all", _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_all_alerts(self):

        client = AlertClient()
        res = client.get_alert_list()
        return res
    
    @get(_path="/v1/alerts/category", _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_all_alerts_category(self):

        client = AlertClient()
        res = client.get_alert_list()
        critical = []
        warning = []
        for cate in res:
            if cate["labels"]["severity"] == "critical":
                critical.append(cate)
            if cate["labels"]["severity"] == "warning":
                warning.append(cate)
        
        
        return {"critical":critical, "warning":warning}    
    
class TasksHandler(pyrestful.rest.RestHandler):
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        #self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, PUT, GET, DELETE, OPTIONS')
        #self.set_header("Access-Control-Allow-Headers", "access-control-allow-origin,authorization,content-type")

    @get(_path="/v1/task/all", _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_all_alerts(self):

        client = AlertClient()
        res = client.get_alert_list()
        return res
    

        
    