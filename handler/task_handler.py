# -*- coding: utf-8 -*-

import tornado.ioloop
import pyrestful.rest
from db.model.task import InstanceCreateTask

from pyrestful import mediatypes
from pyrestful.rest import get, post, put, delete
import json

from model.volumes import *
from util.cov import todict
from api.openstack.client import Client
from api.prometheus.client import Client as Pclient
from tornado_swagger.components import components
from mydoc.volumes import *
from Page.Pagenation import  Pagenation
from db.model.user import User
from db.model.vm import Volumes, Snapshot
from db.model.user import User
from api.model.volumes import VolumeCreateFrom, VolumeCreateFromV2
from string import Template

import logging
logger = logging.getLogger(__name__)


class TaskHandler(pyrestful.rest.RestHandler):


    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        #self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, GET, DELETE, PUT, OPTIONS')
        #self.set_header("Access-Control-Allow-Headers", "access-control-allow-origin,authorization,content-type")

    @post(_path="/v1/task/instances", _consumes=mediatypes.APPLICATION_JSON , _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_all_volumes(self, form):
        """
        ---
        tags:
          - task相关接口
        summary: 查询task列表
        requestBody:
          description: 查询条件
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VolumesListFormModel'
          required: true
        responses:
          '200':
            description: 成功
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ArrayVolumesDetailModel'
        """

        username = self.get_cookie("username", "")
        role = self.get_cookie("role", "")
        with self.session_scope() as session:
            user = session.query(User).filter(User.username == username).first()
            session.expunge(user)
        order_by = form.get("order_by") or "created_at"
        order_type = form.get("order_type", "desc")
        search_str = form.get("search_str", "")
        res = []
        if role == "operator":
            with self.session_scope() as session:
                my_task = session.query(InstanceCreateTask).filter(InstanceCreateTask.user_id == user.id).all()
                res = [self.instance_to_dict(task) for task in my_task]
        if role == "sysadm":
            with self.session_scope() as session:
                my_task = session.query(InstanceCreateTask).all()
                res = [self.instance_to_dict(task) for task in my_task]

        data = []

        if search_str:
            for d in res:
                if search_str in d["name"]:
                    data.append(d)
        else:
            data = res

        if order_type == "desc" and order_by:
            data = sorted(data, key=lambda x: x[order_by], reverse=True)
        if order_type == "asc" and order_by:
            data = sorted(data, key=lambda x: x[order_by])

        obj = Pagenation(data,form.get("page", 1),form.get("pagecount", 10))
        task_list = obj.show()



        client = Client()
        zone_dict = client.HypervisorsClient.openstack_get_name_availabilityzone_by_zonename(client)

        for task in task_list:
            task["availability_zone"] = zone_dict.get(task["availability_zone"], task["availability_zone"])
            

        r = {
            "total": len(data),
            "pages": obj.total(),
            "data": task_list
        }

        return r

    @post(_path="/v1/task/with/user/instances", _consumes=mediatypes.APPLICATION_JSON , _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_all_volumes_with_user(self, form):
        """
        ---
        tags:
          - task相关接口
        summary: 查询task列表
        requestBody:
          description: 查询条件
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VolumesListFormModel'
          required: true
        responses:
          '200':
            description: 成功
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ArrayVolumesDetailModel'
        """

        username = self.get_cookie("username", "")
        role = self.get_cookie("role", "")
        with self.session_scope() as session:
            user = session.query(User).filter(User.username == username).first()
            session.expunge(user)
        order_by = form.get("order_by") or "created_at"
        order_type = form.get("order_type", "desc")
        search_str = form.get("search_str", "")
        res = []
        if role == "operator":
            with self.session_scope() as session:
                my_task = session.query(InstanceCreateTask).filter(InstanceCreateTask.user_id == user.id).all()
                res = [self.instance_to_dict(task) for task in my_task]
        if role == "sysadm":
            with self.session_scope() as session:
                my_task = session.query(InstanceCreateTask).all()
                res = [self.instance_to_dict(task) for task in my_task]

        data = []

        if search_str:
            for d in res:
                if search_str in d["name"]:
                    data.append(d)
        else:
            data = res

        if order_type == "desc" and order_by:
            data = sorted(data, key=lambda x: x[order_by], reverse=True)
        if order_type == "asc" and order_by:
            data = sorted(data, key=lambda x: x[order_by])

        obj = Pagenation(data,form.get("page", 1),form.get("pagecount", 10))
        task_list = obj.show()



        client = Client()
        zone_dict = client.HypervisorsClient.openstack_get_name_availabilityzone_by_zonename(client)

        for task in task_list:
            task["availability_zone"] = zone_dict.get(task["availability_zone"], task["availability_zone"])

        r = {
            "total": len(data),
            "pages": obj.total(),
            "data": task_list
        }

        return r

    @get(_path="/v1/task/instances", _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_health_check(self):
        status = self.get_argument('status')
        status_list = ["ready", "error", "complete"]
        if status not in status_list:
            r = {
                "code": 400,
                "msg": "请求参数错误"
            }
            return r

        username = self.get_cookie("username", "")
        role = self.get_cookie("role", "")
        with self.session_scope() as session:
            user = session.query(User).filter(User.username == username).first()
            session.expunge(user)
        res = []

        client = Client()
        zone_dict = client.HypervisorsClient.openstack_get_name_availabilityzone_by_zonename(client)


        if role == "operator":
            with self.session_scope() as session:
                my_task = session.query(InstanceCreateTask).filter(InstanceCreateTask.user_id == user.id).filter(InstanceCreateTask.status == status).all()
                for task in my_task:
                    task_dict = self.instance_to_dict(task)
                    task_dict["availability_zone"] = zone_dict.get(task_dict["availability_zone"], task_dict["availability_zone"])
                    res.append(task_dict)
        if role == "sysadm":
            with self.session_scope() as session:
                my_task = session.query(InstanceCreateTask).filter(InstanceCreateTask.status == status).all()
                for task in my_task:
                    task_dict = self.instance_to_dict(task)
                    task_dict["availability_zone"] = zone_dict.get(task_dict["availability_zone"], task_dict["availability_zone"])
                    res.append(task_dict)


        r = {
            "total": len(res),
            "data": res
        }

        return r

    def instance_to_dict(self,task):
    # 这里 InstanceCreateTask 有 'id', 'name', 'status' 这些属性
        return {
        'id': task.id,
        'name': task.name,
        'status': task.status,
        'user_id': task.user_id,
        'created_at': task.create_at.isoformat() if task.create_at else None,
        'iso': task.iso,
        'availability_zone': task.availability_zone,
        'ipv4': task.ipv4,
        'os_type': task.os_type,
        'imageRef': task.imageRef,
        'flavorRef': task.flavorRef,
        'networkRef': task.networkRef,
        'volume_id': task.volume_id,
        'driver_volume_id': task.driver_volume_id,
        'vmid':task.vmid
        }