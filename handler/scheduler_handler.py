# -*- coding: utf-8 -*-

from pyrestful import mediatypes
from pyrestful.rest import <PERSON><PERSON>andler, get, post, put, delete

from api.log.log import CustomLogger
from api.scheduler.client import Client

import logging
from threading import Lock
logger     = logging.getLogger(__name__)
new_logger = CustomLogger()


class SchedlerHandler(RestHandler):


    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        #self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, GET, DELETE, PUT, OPTIONS')
        #self.set_header("Access-Control-Allow-Headers", "access-control-allow-origin,authorization,content-type")

    @get(_path="/v1/cluster/drs/table", _produces=mediatypes.APPLICATION_JSON)
    def cluster_drs_table(self):
        role     = self.get_cookie('role', "")
        username = self.get_cookie('username', "")
        client   = Client()
        res      = client.get_cluster_drs()

        # if res:
        #     new_logger.log(
        #         username, "集群查询操作", "动态资源调度", "成功", role, "动态资源调度HTML: {}".format(
        #             res["snapshot_html"]))

        return res

    @post(_path="/v1/cluster/drs/table",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def cluster_drs_table_update(self, form):
        role           = self.get_cookie('role', "")
        username       = self.get_cookie('username', "")
        node           = form.get("node")
        vm_name        = form.get("vm_name")
        vm_id          = form.get("vm_id")
        recommend_node = form.get("recommend_node")
        data           = {"vm_name": vm_name,
                          "id": vm_id,
                          "target_host": recommend_node}
        client         = Client()
        res            = client.post_cluster_drs(data)

        if res["msg"] == "ok":
            new_logger.log(
                username, "动态资源调度操作", "动态资源调度", "成功", role, "动态资源调度：虚拟机：{}，id：{}，当前节点：{},目标节点：{}HTML: {}".format(
                    vm_name, vm_id, node, recommend_node, res["snapshot_html"]))
        else:
            new_logger.log(
                username, "动态资源调度操作", "动态资源调度", "失败", role, "动态资源调度HTML: {}".format(
                    res["snapshot_html"]))

        return res
