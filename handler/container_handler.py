# -*- coding: utf-8 -*-

import tornado.ioloop
import pyrestful.rest
from api.log.log import Custom<PERSON>ogger

from pyrestful import mediatypes
from pyrestful.rest import get, post, put, delete, patch

from model.container import ContainerCreateFrom
from util.cov import todict
from api.zun.client import ZunClient
from tornado_swagger.components import components
from mydoc.flavors import *
from Page.Pagenation import  Pagenation
from asynservices.tasks import create_container_task

import logging
logger = logging.getLogger(__name__)
new_logger = CustomLogger()


class Container1Handler(pyrestful.rest.RestHandler):
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        #self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, GET, DELETE, PUT, OPTIONS')
        #self.set_header("Access-Control-Allow-Headers", "access-control-allow-origin,authorization,content-type")

    @get(_path="/v1/containers/hosts",  _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_container_hosts(self):
        client = ZunClient()
        res = client.ZunClient.openstack_container_hosts(client)
        return res

class ContainerHandler(pyrestful.rest.RestHandler):
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        #self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, GET, DELETE, PUT, OPTIONS')
        #self.set_header("Access-Control-Allow-Headers", "access-control-allow-origin,authorization,content-type")

    @post(_path="/v1/containers", _consumes=mediatypes.APPLICATION_JSON , _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_all_containers(self,form):
        """
        ---
        tags:
          - 云主机类型相关接口
        summary: 查询全部云主机类型
        responses:
          '200':
            description: 成功
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ArrayImageModel'
        """

        order_by = form.get("order_by", "")
        order_type = form.get("order_type", "desc")
        search_str = form.get("search_str", "")

        client = ZunClient()
        res = client.ZunClient.openstack_get_all_container_list(client)
        data = []

        if search_str:
            for d in res:
                if search_str in d["name"]:
                    data.append(d)
        else:
            data = res

        if order_type == "desc" and order_by:
            #data = sorted(data, key = lambda x:-x[order_by])
            data = sorted(data, key = lambda x:x[order_by], reverse=True)

        if order_type == "asc" and order_by:
            data = sorted(data, key = lambda x:x[order_by])

        obj = Pagenation(data,form.get("page", 1),form.get("pagecount", 10))
        container_list = obj.show()

        for container in res:
            ips = []
            if container["addresses"]:
                for key in container["addresses"]:
                    for address in container["addresses"][key]:
                        ips.append(address["addr"])

            container["ip"] = ips

        r = {
            "total": len(data),
            "pages": obj.total(),
            "data": container_list
            }

        return r
      
    @post(_path="/v1/containers/details",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_container_details(self, form):
      """
      添加安全组
      """
      role = self.get_cookie('role', "")
      username = self.get_cookie('username', "")
      
      try:
        container_id = form.get("id", "")
        client = ZunClient()
        res = client.ZunClient.openstack_container_details(client,container_id)
      except Exception as e:
        return {"msg": "error"}
        
      return res

    @post(_path="/v1/containers/images", _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_all_images(self, form):
        """
        ---
        tags:
          - 镜像相关接口
        summary: 查询全部镜像
        responses:
          '200':
            description: 成功
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ArrayImageModel'
        """
        client = ZunClient()
        res = client.ImageClient.openstack_get_all_image_list(client)
        
        # 从请求参数中获取分页和排序信息
        order_by = form.get("order_by", "")
        order_type = form.get("order_type", "desc")
        search_str = form.get("search_str", "")
        page = int(form.get("page", 1))
        pagecount = int(form.get("pagecount", 10))

        data = []

        # 搜索过滤
        if search_str:
            for d in res:
                if search_str in d.get("tag", ""):
                    data.append(d)
        else:
            data = res

        # 排序
        if order_type == "desc" and order_by:
            data = sorted(data, key=lambda x: x.get(order_by, ""), reverse=True)
        elif order_type == "asc" and order_by:
            data = sorted(data, key=lambda x: x.get(order_by, ""))

        # 分页
        obj = Pagenation(data, page, pagecount)
        image_list = obj.show()

        r = {
            "total": len(data),
            "pages": obj.total(),
            "data": image_list
        }

        return r

    @post(_path="/v1/containers/images/delete",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_delete_image(self, imagedeleteform):
        role = self.get_cookie('role', "")
        client = ZunClient()
        ids = imagedeleteform.get("ids", [])

        if not ids:
            return {"msg": "ids不能为空"}

        try:
            for id in ids:
                res = client.ImageClient.openstack_delete_image(client, id)
                if res["msg"] != "ok":
                    self._log_image_deletion("失败", imagedeleteform, role)
                    return res
        except Exception as e:
            self._log_image_deletion("失败", imagedeleteform, role)
            return {"msg": "error"}

        self._log_image_deletion("成功", imagedeleteform, role)
        return res

    def _log_image_deletion(self, status, imagedeleteform, role):
        new_logger.log(
            self.username, "镜像管理", "删除容器镜像", status, role,
            "{} {},{}".format("删除镜像", imagedeleteform.get("tags", ""), status)
        )

    @post(_path="/v1/containers/images/pull",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_pull_image(self, form):
        role = self.get_cookie('role', "")
        client = ZunClient()
        repo = form.get("repo", "")

        if not repo:
            return {"msg": "repo不能为空"}

        try:
            res = client.ImageClient.openstack_container_pull_image(client, form)
            if res["msg"] != "ok":
                self._log_image_pull("失败", form, role)
                return res
        except Exception as e:
            self._log_image_pull("失败", form, role)
            return {"msg": "error"}

        self._log_image_pull("成功", form, role)
        return res

    def _log_image_pull(self, status, form, role):
        new_logger.log(
            self.username, "镜像管理", "拉取镜像", status, role,
            "{} {},{}".format("拉取镜像", form.get("repo", ""), status)
        )

    @get(_path="/v1/containers/networks",_consumes=mediatypes.APPLICATION_JSON,  _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_all_networks(self):
        """
        ---
        tags:
          - 网络相关接口
        summary: 查询全部网络  
        responses:
          '200':
            description: 查询成功
            content: {}
        """
        client = ZunClient()
        res = client.NeutronClient.openstack_get_all_list(client)
        for network in res:
            network["cidr"] = ""
            for subnet in network["subnets"]:
                subnet_detail = client.NeutronClient.openstack_get_subnet_detail(client, subnet)
                
                print(subnet_detail["name"])
                print(subnet_detail["cidr"])
                network["cidr"] = "%s%s:%s," % (network["cidr"] ,subnet_detail["name"],subnet_detail["cidr"])
            network["cidr"] = network["cidr"].rstrip(',')
        return res
    
    @post(_path="/v1/containers/create",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_create_containers(self, form):
        """
        ---
        tags:
          - 容器相关接口
        summary: 创建容器
        requestBody:
          description: 创建
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RequestCreateInstanceModel'
          required: true
        responses:
          '200':
            description: 创建成功
            content: {}
        """

        client = ZunClient()

        # create_form = ContainerCreateFrom()
        # create_form.name = form.get("name", "")
        # create_form.vcpus = form.get("vcpus", "")
        # create_form.ram = form.get("ram", "")
        # create_form.imagename = form.get("image_name", "")
        # create_form.networkid = form.get("network_id", "")
        
        created_to_run = form.get("run", True)
        if created_to_run:
          create_container_task.delay(form)
          return {"msg": "ok", "error": "容器创建任务已提交，请稍后查看状态"}
        
        restart_policy = form.get("restart_policy", {})
        if restart_policy:
          Name = restart_policy.get("Name", "")
          if Name == "":
            form["restart_policy"] = None

        container = client.ZunClient.openstack_create_container(client,form)

        return container

    @post(_path="/v1/containers/action",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_container_action(self, form):
        """
        ---
        tags:
          - 容器相关接口
        summary: 容器动作
        responses:
          '200':
            description: 成功
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ImageEditModel'
        """
        client = ZunClient()

        if form["action"] == "stop":
            res = client.ZunClient.openstack_container_stop(client,form["id"])
        elif form["action"] == "start":
            res = client.ZunClient.openstack_container_start(client,form["id"])
        elif form["action"] == "restart":
            res = client.ZunClient.openstack_container_restart(client,form["id"])
        elif form["action"] == "edit":
            res = client.ZunClient.openstack_container_rename(client,form["id"],form["data"])
        elif form["action"] == "resize":
            res = client.ZunClient.openstack_container_resize(client,form)
        elif form["action"] == "pause":
            res = client.ZunClient.openstack_container_pause(client,form["id"])
        elif form["action"] == "unpause":
            res = client.ZunClient.openstack_container_unpause(client,form["id"])
        elif form["action"] == "rebuild":
            res = client.ZunClient.openstack_container_rebuild(client,form)
        elif form["action"] == "add_security_group":
            res = client.ZunClient.openstack_container_add_security_group(client,form)
        elif form["action"] == "commit":
            res = client.ZunClient.openstack_container_commit(client,form)
        elif form["action"] == "network_detach":
            res = client.ZunClient.openstack_container_network_detach(client,form)
        elif form["action"] == "network_attach":
            res = client.ZunClient.openstack_container_network_attach(client,form)
        elif form["action"] == "execute":
            res = client.ZunClient.openstack_container_execute(client,form)
        elif form["action"] == "execute_resize":
            res = client.ZunClient.openstack_container_execute_resize(client,form)
        return res

    @post(_path="/v1/containers/action/execute",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_container_action_execute(self, form):
      role = self.get_cookie('role', "")
      username = self.get_cookie('username', "")
      try:
        
        client = ZunClient()
        res = client.ZunClient.openstack_container_execute(client,form)
      except Exception as e:
        return {"msg": "error"}
        
      return res
    
    @post(_path="/v1/containers/action/kill",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_container_action_kill(self, form):
      role = self.get_cookie('role', "")
      username = self.get_cookie('username', "")
      try:
        
        client = ZunClient()
        res = client.ZunClient.openstack_container_kill(client,form)
      except Exception as e:
        return {"msg": "error"}
        
      return res
    
    
    @post(_path="/v1/containers/action/add_security_group",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_container_action_add_security_group(self, form):
      """
      添加安全组
      """
      role = self.get_cookie('role', "")
      username = self.get_cookie('username', "")
      try:
        
        client = ZunClient()
        res = client.ZunClient.openstack_container_add_security_group(client,form)
      except Exception as e:
        return {"msg": "error"}
        
      return res
    
    @post(_path="/v1/containers/action/remove_security_group",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_container_action_remove_security_group(self, form):
      """
      添加安全组
      """
      role = self.get_cookie('role', "")
      username = self.get_cookie('username', "")
      try:
        
        client = ZunClient()
        res = client.ZunClient.openstack_container_remove_security_group(client,form)
      except Exception as e:
        return {"msg": "error"}
        
      return res

    @post(_path="/v1/containers/delete",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_container_delete(self, form):
        """
        ---
        tags:
          - 容器相关接口
        summary: 删除容器
        requestBody:
          description: 删除容器参数
          content:
            application/json:
              schema:
                type: object
                properties:
                  ids:
                    type: array
                    items:
                      type: string
                    description: 要删除的容器ID或名称列表
                required:
                  - ids
        responses:
          '200':
            description: 成功
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    msg:
                      type: string
                      description: 操作结果
                    error:
                      type: string
                      description: 错误信息
        """
        # 参数校验
        # print(form)
        names = form.get("names")
        if not form:
            return {"msg": "error", "error": "请求体不能为空"}
            
        ids = form.get("ids")
        if not ids:
            return {"msg": "error", "error": "ids参数不能为空"}
        
        if not isinstance(ids, list):
            return {"msg": "error", "error": "ids必须是数组"}
          
          
        client = ZunClient()
        try:
            for container_id in ids:
                del_form = {
                  "container_id": container_id,
                  "force": form.get("force"),
                  "stop": form.get("stop"),
                }
                res = client.ZunClient.openstack_container_delete(client, del_form)
                # if res["msg"] != "ok":
                #     return {"msg": "error", "error": f"删除容器{container_id}失败: {res.get('error', '未知错误')}"}
            return {"msg": "ok"}
        except Exception as e:
            return {"msg": "error", "error": str(e)}

    @post(_path="/v1/containers/logs", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_container_logs(self,form):
        """
        ---
        tags:
          - 容器相关接口
        summary: 获取容器日志
        requestBody:
          description: 获取容器日志参数
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: string
                    description: 容器ID或名称
                  timestamps:
                    type: boolean
                    description: 是否显示时间戳
                  tail:
                    type: string
                    description: 显示最后多少行日志
                  stderr:
                    type: boolean
                    description: 是否获取标准错误输出
                  stdout:
                    type: boolean
                    description: 是否获取标准输出
                  since:
                    type: string
                    description: 从指定时间开始显示日志
                required:
                  - id
        responses:
          '200':
            description: 成功
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    msg:
                      type: string
                    logs:
                      type: string
                      description: 容器日志内容
        """
        client = ZunClient()
        res = client.ZunClient.openstack_container_logs(client, form)
        return res



    @post(_path="/v1/containers/availability_zones", _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_availability_zones(self):
        """
        ---
        tags:
          - 容器相关接口
        summary: 获取可用域列表
        """
        client = ZunClient()
        res = client.ZunClient.openstack_get_availability_zones(client)
        return res

    @post(_path="/v1/containers/volumes", _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_volumes(self):
        """
        ---
        tags:
          - 容器相关接口
        summary: 获取可用域列表
        """
        client = ZunClient()
        res = client.ZunClient.openstack_get_all_volumes(client)
        return res


    @post(_path="/v1/containers/stats/{id}", _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_container_stats(self, id):
        """
        ---
        tags:
          - 容器相关接口
        summary: 获取容器统计信息
        parameters:
          - name: id
            in: path
            required: true
            description: 容器ID或名称
            schema:
              type: string
        responses:
          '200':
            description: 成功
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    stats_info:
                      type: object
                      description: 容器统计信息
        """
        client = ZunClient()
        res = client.ZunClient.openstack_container_stats(client, id)
        return res

    @post(_path="/v1/containers/update", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_patch_container_update(self, form):
        """
        ---
        tags:
          - 容器相关接口
        summary: 更新容器信息
        requestBody:
          description: 更新容器的内存/CPU/名称
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: string
                    description: 容器ID或名称
                  memory:
                    type: integer
                    description: 内存大小(MiB)
                  cpu:
                    type: number
                    format: float
                    description: CPU核心数
                  name:
                    type: string
                    description: 新的容器名称
        responses:
          '200':
            description: 更新成功
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    msg:
                      type: string
        """
        client = ZunClient()
        res = client.ZunClient.openstack_container_update_information(client, form)
        return res

    @post(_path="/v1/containers/get_archive", _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_container_get_archive(self, form):
        """
        ---
        tags:
          - 容器相关接口
        summary: 获取容器文件归档
        parameters:
          - name: id
            in: query
            required: true
            description: 容器ID或名称
            schema:
              type: string
          - name: source_path
            in: query  
            required: true
            description: 容器内的文件路径
            schema:
              type: string
        responses:
          '200':
            description: 成功
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    data:
                      type: string
                      description: tar文件内容
                    stat:
                      type: string 
                      description: 文件状态信息
        """
        client = ZunClient()
        res = client.ZunClient.openstack_container_get_archive(client, form)
        return res

    @post(_path="/v1/containers/put_archive", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_container_put_archive(self, form):
        """
        ---
        tags:
          - 容器相关接口
        summary: 上传文件归档到容器
        requestBody:
          description: 上传tar文件到容器指定路径
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: string
                    description: 容器ID或名称
                  destination_path:
                    type: string
                    description: 容器内的目标路径
                  data:
                    type: string
                    description: tar文件的内容
                required:
                  - id
                  - destination_path
                  - data
        responses:
          '200':
            description: 上传成功
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    msg:
                      type: string
        """
        client = ZunClient()
        res = client.ZunClient.openstack_container_put_archive(client, form)
        return res

    @post(_path="/v1/containers/attach/{id}", _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_container_attach(self, id):
        """
        ---
        tags:
          - 容器相关接口
        summary: 附加到容器
        parameters:
          - name: id
            in: path
            required: true
            description: 容器ID或名称
            schema:
              type: string
        responses:
          '200':
            description: 附加成功
            content:
              application/json:
                schema:
                  type: object
        """
        client = ZunClient()
        res = client.ZunClient.openstack_container_attach(client, id)
        return res

    @post(_path="/v1/containers/{id}/top", _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_container_top(self, id):
        """
        ---
        tags:
          - 容器相关接口
        summary: 获取容器进程列表
        parameters:
          - name: id
            in: path
            required: true
            description: 容器ID或名称
            schema:
              type: string
          - name: ps_args
            in: query
            required: false
            description: ps命令的参数
            schema:
              type: string
        responses:
          '200':
            description: 成功
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    msg:
                      type: string
                    ps_output:
                      type: object
                      description: 进程列表信息
        """
        client = ZunClient()
        form = {
            "id": id,
            "ps_args": self.get_argument("ps_args", None)
        }
        res = client.ZunClient.openstack_container_top(client, form)
        return res

    @post(_path="/v1/containers/{id}/network_list", _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_container_network_list(self, id):
        """
        ---
        tags:
          - 容器相关接口
        summary: 获取容器网络列表
        parameters:
          - name: id
            in: path
            required: true
            description: 容器ID或名称
            schema:
              type: string
        responses:
          '200':
            description: 成功
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    msg:
                      type: string
                    networks:
                      type: array
                      items:
                        type: object
                        properties:
                          net_id:
                            type: string
                            description: 网络UUID
                          subnet_id:
                            type: string
                            description: 子网UUID
                          port_id:
                            type: string
                            description: 端口UUID
                          version:
                            type: string
                            description: IP协议版本
                          ip_address:
                            type: string
                            description: IP地址
                          fixed_ips:
                            type: array
                            description: 固定IP地址信息列表
        """
        client = ZunClient()
        form = {"id": id}
        res = client.ZunClient.openstack_container_network_list(client, form)
        return res

    @post(_path="/v1/containers/{id}/container_actions", _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_container_actions(self, id):
        """
        ---
        tags:
          - 容器相关接口
        summary: 获取容器操作记录列表
        parameters:
          - name: id
            in: path
            required: true
            description: 容器ID或名称
            schema:
              type: string
        responses:
          '200':
            description: 成功
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    msg:
                      type: string
                    actions:
                      type: array
                      items:
                        type: object
                        properties:
                          action:
                            type: string
                            description: 操作名称
                          container_uuid:
                            type: string
                            format: uuid
                            description: 容器UUID
                          message:
                            type: string
                            description: 错误信息(如果有)
                          request_id:
                            type: string
                            description: 请求ID
                          start_time:
                            type: string
                            description: 操作开始时间(ISO 8601格式)
                          project_id:
                            type: string
                            description: 项目ID
                          user_id:
                            type: string
                            description: 用户ID
        """
        client = ZunClient()
        form = {"id": id}
        res = client.ZunClient.openstack_container_actions(client, form)
        return res

    @post(_path="/v1/containers/{id}/container_actions/{request_id}", _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_container_action_details(self, id, request_id):
        """
        ---
        tags:
          - 容器相关接口
        summary: 获取容器操作详情
        parameters:
          - name: id
            in: path
            required: true
            description: 容器ID或名称
            schema:
              type: string
          - name: request_id
            in: path
            required: true
            description: 操作请求ID
            schema:
              type: string
        responses:
          '200':
            description: 成功
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    msg:
                      type: string
                    action:
                      type: object
                      properties:
                        action:
                          type: string
                          description: 操作名称
                        container_uuid:
                          type: string
                          format: uuid
                          description: 容器UUID
                        message:
                          type: string
                          description: 错误信息(如果有)
                        project_id:
                          type: string
                          description: 项目ID
                        request_id:
                          type: string
                          description: 请求ID
                        start_time:
                          type: string
                          description: 操作开始时间(ISO 8601格式)
                        user_id:
                          type: string
                          description: 用户ID
                        events:
                          type: array
                          items:
                            type: object
                            properties:
                              event:
                                type: string
                                description: 事件名称
                              start_time:
                                type: string
                                description: 事件开始时间
                              finish_time:
                                type: string
                                description: 事件结束时间
                              result:
                                type: string
                                description: 事件结果
                              traceback:
                                type: string
                                description: 错误堆栈信息
        """
        client = ZunClient()
        form = {
            "id": id,
            "request_id": request_id
        }
        res = client.ZunClient.openstack_container_action_details(client, form)
        return res

    @post(_path="/v1/services", _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_services(self):
        """
        ---
        tags:
          - 服务相关接口
        summary: 获取所有Zun服务状态
        responses:
          '200':
            description: 成功
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    msg:
                      type: string
                    services:
                      type: array
                      items:
                        type: object
                        properties:
                          binary:
                            type: string
                            description: Zun服务的二进制名称
                          availability_zone:
                            type: string
                            description: 可用区
                          created_at:
                            type: string
                            description: 创建时间(ISO 8601格式)
                          state:
                            type: string
                            description: 服务当前状态
                          report_count:
                            type: integer
                            description: 报告总数
                          updated_at:
                            type: string
                            description: 更新时间(ISO 8601格式)
                          host:
                            type: string
                            description: 服务所在主机
                          disabled_reason:
                            type: string
                            description: 禁用原因(如果服务被禁用)
                          id:
                            type: string
                            description: 服务ID
        """
        client = ZunClient()
        res = client.ZunClient.openstack_get_zun_services(client)
        return res

    @delete(_path="/v1/services", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_delete_service(self, form):
        """
        ---
        tags:
          - 服务相关接口
        summary: 删除Zun服务
        requestBody:
          description: 删除服务的参数
          content:
            application/json:
              schema:
                type: object
                properties:
                  binary:
                    type: string
                    description: Zun服务的二进制名称
                  host:
                    type: string
                    description: 服务所在主机
                required:
                  - binary
                  - host
        responses:
          '200':
            description: 删除成功
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    msg:
                      type: string
        """
        client = ZunClient()
        res = client.ZunClient.openstack_delete_service(client, form)
        return res

    @put(_path="/v1/services/enable", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_enable_service(self, form):
        """
        ---
        tags:
          - 服务相关接口
        summary: 启用Zun服务
        requestBody:
          description: 启用服务的参数
          content:
            application/json:
              schema:
                type: object
                properties:
                  binary:
                    type: string
                    description: Zun服务的二进制名称
                  host:
                    type: string
                    description: 服务所在主机
                required:
                  - binary
                  - host
        responses:
          '200':
            description: 启用成功
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    msg:
                      type: string
        """
        client = ZunClient()
        res = client.ZunClient.openstack_enable_service(client, form)
        return res

    @put(_path="/v1/services/disable", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_disable_service(self, form):
        """
        ---
        tags:
          - 服务相关接口
        summary: 禁用Zun服务
        requestBody:
          description: 禁用服务的参数
          content:
            application/json:
              schema:
                type: object
                properties:
                  binary:
                    type: string
                    description: Zun服务的二进制名称
                  host:
                    type: string
                    description: 服务所在主机
                  disabled_reason:
                    type: string
                    description: 禁用原因
                required:
                  - binary
                  - host
        responses:
          '200':
            description: 禁用成功
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    msg:
                      type: string
                    service:
                      type: object
                      properties:
                        host:
                          type: string
                          description: 服务所在主机
                        binary:
                          type: string
                          description: 服务二进制名称
                        disabled:
                          type: boolean
                          description: 是否已禁用
                        disabled_reason:
                          type: string
                          description: 禁用原因
        """
        client = ZunClient()
        res = client.ZunClient.openstack_disable_service(client, form)
        return res

    @put(_path="/v1/services/force_down", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_force_down_service(self, form):
        """
        ---
        tags:
          - 服务相关接口
        summary: 强制设置Zun服务状态
        requestBody:
          description: 强制设置服务状态的参数
          content:
            application/json:
              schema:
                type: object
                properties:
                  binary:
                    type: string
                    description: Zun服务的二进制名称
                  host:
                    type: string
                    description: 服务所在主机
                  forced_down:
                    type: boolean
                    description: 是否强制设置为down状态
                required:
                  - binary
                  - host
                  - forced_down
        responses:
          '200':
            description: 设置成功
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    msg:
                      type: string
                    service:
                      type: object
                      properties:
                        host:
                          type: string
                          description: 服务所在主机
                        binary:
                          type: string
                          description: 服务二进制名称
                        forced_down:
                          type: boolean
                          description: 是否已强制设置为down状态
        """
        client = ZunClient()
        res = client.ZunClient.openstack_force_down_service(client, form)
        return res

    @post(_path="/v1/hosts", _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_container_hosts(self):
        """
        ---
        tags:
          - 宿主机相关接口
        summary: 获取所有容器宿主机列表
        responses:
          '200':
            description: 获取成功
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    msg:
                      type: string
                    hosts:
                      type: array
                      items:
                        type: object
                        properties:
                          architecture:
                            type: string
                            description: 主机系统架构
                          cpus:
                            type: string
                            description: CPU总数
                          cpu_used:
                            type: string
                            description: 已使用CPU数
                          disk_total:
                            type: string
                            description: 磁盘总量
                          disk_used:
                            type: string
                            description: 已使用磁盘量
                          hostname:
                            type: string
                            description: 主机名称
                          kernel_version:
                            type: string
                            description: 内核版本
                          labels:
                            type: object
                            description: 主机标签
                          mem_total:
                            type: string
                            description: 内存总量
                          mem_used:
                            type: string
                            description: 已使用内存
                          os:
                            type: string
                            description: 操作系统名称
                          os_type:
                            type: string
                            description: 操作系统类型
                          total_containers:
                            type: string
                            description: 容器总数
                          uuid:
                            type: string
                            description: 主机UUID
                          enable_cpu_pinning:
                            type: boolean
                            description: 是否启用CPU绑定
        """
        client = ZunClient()
        res = client.ZunClient.openstack_container_hosts(client)
        return res

    @post(_path="/v1/hosts/{host_ident}", _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_host_details(self, host_ident):
        """
        ---
        tags:
          - 宿主机相关接口
        summary: 获取指定宿主机的详细信息
        parameters:
          - name: host_ident
            in: path
            required: true
            description: 宿主机的UUID或名称
            schema:
              type: string
        responses:
          '200':
            description: 获取成功
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    msg:
                      type: string
                    host:
                      type: object
                      properties:
                        architecture:
                          type: string
                          description: 主机系统架构
                        cpus:
                          type: string
                          description: CPU总数
                        cpu_used:
                          type: string
                          description: 已使用CPU数
                        disk_total:
                          type: string
                          description: 磁盘总量
                        disk_used:
                          type: string
                          description: 已使用磁盘量
                        hostname:
                          type: string
                          description: 主机名称
                        kernel_version:
                          type: string
                          description: 内核版本
                        labels:
                          type: object
                          description: 主机标签
                        mem_total:
                          type: string
                          description: 内存总量
                        mem_used:
                          type: string
                          description: 已使用内存
                        os:
                          type: string
                          description: 操作系统名称
                        os_type:
                          type: string
                          description: 操作系统类型
                        total_containers:
                          type: string
                          description: 容器总数
                        uuid:
                          type: string
                          description: 主机UUID
                        enable_cpu_pinning:
                          type: boolean
                          description: 是否启用CPU绑定
        """
        client = ZunClient()
        res = client.ZunClient.openstack_container_host_details(client, host_ident)
        return res

