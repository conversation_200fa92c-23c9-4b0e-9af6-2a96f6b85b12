# -*- coding: utf-8 -*-

import tornado.ioloop
import pyrestful.rest

from pyrestful import mediatypes
from pyrestful.rest import get, post, put, delete

from model.container import ContainerCreateFrom
from util.cov import todict
from api.thesc.client import Client as SClient
from tornado_swagger.components import components
from mydoc.flavors import *
from Page.Pagenation import  Pagenation

import logging
logger = logging.getLogger(__name__)

class ThescHandler(pyrestful.rest.RestHandler):
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        #self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, GET, DELETE, PUT, OPTIONS')
        #self.set_header("Access-Control-Allow-Headers", "access-control-allow-origin,authorization,content-type")

    @get(_path="/v1/migrate/task/recommend/list", _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_migrate_task_list(self):
        
        c = SClient()
        task_list = c.get_migrate_recommend_list()

        return task_list 
    
    @post(_path="/v1/migrate/task/run",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_migrate_task_run(self, form):

        c = SClient()
        run = c.post_migrate_task_run(form)

        return run
    


