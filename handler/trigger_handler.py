# -*- coding: utf-8 -*-
import json

import requests

import pyrestful.rest
from api.log.log import <PERSON><PERSON><PERSON><PERSON>

from pyrestful import mediatypes
from pyrestful.rest import get, post, put, delete

from api.trigger.client import Client as TClient

import logging
logger = logging.getLogger(__name__)
new_logger = CustomLogger()

class TriggerHandler(pyrestful.rest.RestHandler):
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        #self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, GET, DELETE, PUT, OPTIONS')
        #self.set_header("Access-Control-Allow-Headers", "access-control-allow-origin,authorization,content-type")

    @get(_path="/v1/instance/autoexpansion/real")
    def thecloud_instance_autoexpansion_real(self):
        t = TClient()
        task_list = t.get_autoexpansion_real_value()
        if task_list:
            return task_list.text
        return []

    @get(_path="/v1/instance/autoexpansion/info")
    def thecloud_instance_autoexpansion_info(self):

        t = TClient()
        task_list = t.get_autoexpansion_info()

        if task_list:
            return task_list.text
        return []

    @post(_path="/v1/instance/autoexpansion/update",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_instance_autoexpansion_update(self, form):
        role = self.get_cookie('role', "")
        username = self.get_cookie('username', "")
        t = TClient()
        r = t.post_autoexpansion_update(form)
        response_data = r.json()

        if r.status_code == 200:
            new_logger.log(
                username, "虚机操作", "动态资源扩展", "成功", role, response_data.get("data")
            )
            return {"msg":"ok"}
        else:
            new_logger.log(
                username, "虚机操作", "动态资源扩展", "失败", role, response_data.get("data")
            )
            return {"msg":"error"}
