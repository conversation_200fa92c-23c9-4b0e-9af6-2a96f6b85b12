# -*- coding: utf-8 -*-

import tornado.ioloop
import pyrestful.rest
from api.log.log import <PERSON><PERSON>ogger

from pyrestful import mediatypes
from pyrestful.rest import get, post, put, delete

from model.instances import Clusters, Instances
from model.hypervisors import <PERSON>luster, ClusterHostForm,ClusterEditForm,ClusterDeleteForm
from util.cov import todict, serialize
from api.openstack.client import Client
from tornado_swagger.components import components
from mydoc.clusters import *
from model.hosts import CloudyAddFrom
import settings
from api.grafana.mod.ceph import CephClient
from api.prometheus.client import Client as Pclient
from db.model.cloudy import CloudyMaster
from db.model.cluster import ClusterHost
import traceback

import logging
logger = logging.getLogger(__name__)
new_logger = CustomLogger()
class ClusterHandler(pyrestful.rest.RestHandler):
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        #self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, PUT, GET, DELETE, OPTIONS')
        #self.set_header("Access-Control-Allow-Headers", "access-control-allow-origin,authorization,content-type")

    @get(_path="/v1/monitor", _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_monitor_url(self):
        return {"vm_url": settings.VM_MON_URL, "host_url": settings.HOST_MON_URL, "linux_vm_url":settings.LINUX_VM_MON_URL}

    @get(_path="/v1/ceph", _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_ceph_url(self):
        return {"pool_url": settings.POOL_URL, "ceph_url": settings.CEPH_URL}

    @get(_path="/v1/home", _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_home(self):
        """
        ---
        tags:
          - 首页相关接口
        summary: 首页
        responses:
          '200':
            description: 成功
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/StatisticsModel'
        """
        logger.info("test ")
        client = Client()
        res = client.HypervisorsClient.openstack_get_statistics(client)

        vcpus = res["vcpus"]
        vcpus_used = res["vcpus_used"]
        memory_mb = res["memory_mb"]
        memory_mb_used = res["memory_mb_used"]


        cpu_pre = vcpus_used / (vcpus * 4)
        mem_pre = memory_mb_used / memory_mb


        res["sys_status"] = (cpu_pre + mem_pre) / 2
        if res["sys_status"] < 0.01 :
            res["sys_status"] = 0.01

        try:
            ceph_client = CephClient()
            dd = ceph_client.grafana_get_ceph_usage()
            print(dd)

            res["ceph_pool_max"] = dd["ceph_pool_max"]
            res["ceph_pool_stored"] = dd["ceph_pool_stored"]
        except Exception as e:
            res["ceph_pool_max"] = 0
            res["ceph_pool_stored"] = 0

        return res

    @get(_path="/v2/home", _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_home_v2(self):

        client = Client()
        res = client.HypervisorsClient.openstack_get_statistics(client)
        c = Pclient()
        res["usage_ram_total"] = 0
        res["usage_ram_used"] = 0  #从Prom拿值
        res["usage_percent_ram"] = 0 #从Prom拿值
        res["usage_percent_cpu"] = 0
        ram_ava = 0

        #total_ram_list = c.query_vector_by_query(' node_memory_MemTotal_bytes')
        total_ram_list = c.query_vector_by_query('node_memory_MemTotal_bytes{job="openstack_hypervisors"}')
        available_ram_list = c.query_vector_by_query(' node_memory_MemAvailable_bytes{job="openstack_hypervisors"}')

        cpu_percent_list = c.query_vector_by_query("(1 - sum(increase(node_cpu_seconds_total{mode='idle',job='openstack_hypervisors'}[5m])) by (instance) / sum(increase(node_cpu_seconds_total{job='openstack_hypervisors'}[5m])) by (instance) ) * 100")

        for node in total_ram_list:
            res["usage_ram_total"] += int(node["value"][1])
        for node in available_ram_list:
            ram_ava += int(node["value"][1])

        for node in cpu_percent_list:
            res["usage_percent_cpu"] += float(node["value"][1])

        res["usage_percent_cpu"] = res["usage_percent_cpu"] / len(cpu_percent_list) / 100
        res["usage_percent_ram"] = (res["usage_ram_total"] - ram_ava) / res["usage_ram_total"]
        vcpus = res["vcpus"]
        vcpus_used = res["vcpus_used"]
        memory_mb = res["memory_mb"]
        memory_mb_used = res["memory_mb_used"]

        res["usage_cpus_total"] = res["vcpus"]

        res["usage_ram_used"] = round ( (res["usage_ram_total"] - ram_ava  )/1024/1024)
        res["usage_ram_total"] = round (res["usage_ram_total"]/1024/1024)



        cpu_pre = vcpus_used / (vcpus * 4)
        mem_pre = memory_mb_used / memory_mb




        try:
            ceph_client = CephClient()
            dd = ceph_client.grafana_get_ceph_usage()
            print(dd)

            res["ceph_pool_max"] = dd["ceph_pool_max"]
            res["ceph_pool_stored"] = dd["ceph_pool_stored"]
        except Exception as e:
            traceback.print_exc()
            res["ceph_pool_max"] = 0
            res["ceph_pool_stored"] = 0

        res["usage_percent_ceph"] = 0
        if res["ceph_pool_max"] != 0:
            res["usage_percent_ceph"] = res["ceph_pool_stored"] / res["ceph_pool_max"]

        res["sys_status"] = (res["usage_percent_cpu"] + res["usage_percent_ram"] + res["usage_percent_ceph"]) / 3
        if res["sys_status"] < 0.01 :
            res["sys_status"] = 0.01


        return res

    @get(_path="/v1/hypervisors", _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_all_hypervisors(self):
        """
        ---
        tags:
          - 物理机相关接口
        summary: 查询物理机简单列表
        responses:
          '200':
            description: 成功
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ArrayHypervisorModel'
        """
        client = Client()
        res = client.HypervisorsClient.openstack_get_all_list(client)
        return res

    @get(_path="/v1/clusters", _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_clusters(self):
        """
        ---
        tags:
          - 物理机相关接口
        summary: 查询主机聚合列表
        responses:
          '200':
            description: 成功
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ArrayHypervisorModel'
        """
        client = Client()
        res = client.HypervisorsClient.openstack_get_all_aggregates_list(client)

        return res

    @get(_path="/v1/clusters/detail", _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_clusters_detail(self):

        client = Client()
        cluster_list = client.HypervisorsClient.openstack_get_all_aggregates_list(client)
        hy_list = client.HypervisorsClient.openstack_get_all_list(client)

        clu_list = []

        for cluster in cluster_list:
            if len(cluster["hosts"]) > 0 :
                clu_list.append(cluster)

        for cluster in clu_list:
            cpu_used , vcpus , ram_used , rams ,vms = 0,0,0,0,0

            for hy in hy_list:
                if hy["hypervisor_hostname"] in cluster["hosts"]:
                    hydetail = client.HypervisorsClient.openstack_get_hy_87detail(client, hy["id"])
                    cpu_used += hydetail["vcpus_used"]
                    vcpus += (hydetail["vcpus"] * 4)
                    ram_used += hydetail["memory_mb_used"]
                    rams += hydetail["memory_mb"]
                    vms += hydetail["running_vms"]

            cluster["cpu"] = "%.1f" % (cpu_used/vcpus*100)
            cluster["ram"] = "%.1f" % (ram_used/rams*100)
            cluster["vms"] = "%d" % vms

        return clu_list

    @get(_path="/v1/available/hosts", _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_available_hosts(self):
        """
        ---
        tags:
          - 物理机相关接口
        summary: 查询可用主机列表
        responses:
          '200':
            description: 成功
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ArrayHypervisorModel'
        """
        client = Client()
        res = []
        ava_host = client.HypervisorsClient.openstack_get_all_availabilityzone_list(client)

        hy_list = client.HypervisorsClient.openstack_get_all_cluster_detail(client)
        for hy in hy_list:
            if hy["hypervisor_hostname"] not in ava_host:
                res.append(hy)
        return res

    @get(_path="/v1/id/clusters/{id}",_types=[str], _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_clusters_by_id(self, id):
        """
        ---
        tags:
          - 物理机相关接口
        summary: 查询主机聚合里的机器详情
        responses:
          '200':
            description: 成功
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ArrayClusterDetailModel'
        """
        client = Client()
        agg_list = client.HypervisorsClient.openstack_get_aggregates_list(client, id)
        hy_list = client.HypervisorsClient.openstack_get_all_list(client)
        res = []
        c = Pclient()
        #res["usage_percent_cpu"] = 0
        total_ram_list = c.query_vector_by_query('node_memory_MemTotal_bytes{job="openstack_hypervisors"}')
        available_ram_list = c.query_vector_by_query('node_memory_MemAvailable_bytes{job="openstack_hypervisors"}')
        cpu_percent_list = c.query_vector_by_query("(1 - sum(increase(node_cpu_seconds_total{mode='idle', job='openstack_hypervisors'}[5m])) by (instance) / sum(increase(node_cpu_seconds_total{job='openstack_hypervisors'}[5m])) by (instance) ) * 100")

        for hy in hy_list:
            if hy["hypervisor_hostname"] in agg_list.hosts:
                hy_detail = client.HypervisorsClient.openstack_get_hy_87detail(client,hy["id"])
                hydetail = client.HypervisorsClient.openstack_get_hy_detail(client, hy["id"])
                if hydetail.get("uptime"):
                    hy_detail["time"] = hydetail["uptime"].split("up")[1].split("user")[0][:-5]

                    if len(hy_detail["time"].split(",") ) > 1:

                        if len(hy_detail["time"].split(",")[1].split(":")) > 1:
                            hy_detail["time"] = "%s天%s小时%s分钟" % (hy_detail["time"].split(",")[0].lstrip().split(" ")[0],hy_detail["time"].split(",")[1].split(":")[0].lstrip(),hy_detail["time"].split(",")[1].split(":")[1])
                        else:
                            hy_detail["time"] = "%s天0小时%s分钟" % (hy_detail["time"].split(",")[0].lstrip().split(" ")[0],hy_detail["time"].split(",")[1].split(" ")[1])
                    else:
                        if len(hy_detail["time"].split(":") ) > 1:
                            hy_detail["time"] = "%s小时%s分钟" % ( hy_detail["time"].lstrip().split(":")[0], hy_detail["time"].lstrip().split(":")[1]  )
                        else:
                            hy_detail["time"] = "%s分钟" % hy_detail["time"].lstrip()[:-4]

                res.append(hy_detail)

        for hy in res:
            for node in total_ram_list:
                if hy["host_ip"] == node["metric"]["hypervisor_host_ip"] :
                    hy["memory_mb"] = round(int(node["value"][1]) / 1024 / 1024)

        for hy in res:
            for node in available_ram_list:
                if hy["host_ip"] == node["metric"]["hypervisor_host_ip"] :
                    hy["free_ram_mb"] = round(int(node["value"][1]) / 1024 / 1024)

        for hy in res:
            hy["memory_mb_used"] = hy["memory_mb"] - hy["free_ram_mb"]

        for hy in res:
            for node in cpu_percent_list:
                if hy["host_ip"] == node["metric"]["instance"].split(":")[0] :
                    hy["cpu_percent"] = float(node["value"][1])


        return res

    @post(_path="/v1/hypervisor/detail",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_hypervistor_by_id(self, form):
        """
        ---
        tags:
          - 物理机相关接口
        summary: 查询主机聚合里的机器详情
        responses:
          '200':
            description: 成功
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ArrayClusterDetailModel'
        """
        client = Client()
        hy_detail = client.HypervisorsClient.openstack_get_hy_87detail(client,form["id"])

        hy_detail["allocation_ram"] = hy_detail["memory_mb_used"]


        c = Pclient()
        hy_detail["free_ram_mb"] = 0
        hy_detail["memory_mb"] = 0




        total_ram_list = c.query_vector_by_query(' node_memory_MemTotal_bytes')
        available_ram_list = c.query_vector_by_query(' node_memory_MemAvailable_bytes')

        for node in total_ram_list:
            if node["metric"]["instance"].split(":")[0] == hy_detail["host_ip"]:
                hy_detail["memory_mb"] = round(int(node["value"][1]) / 1024 / 1024)
        for node in available_ram_list:
            if node["metric"]["instance"].split(":")[0] == hy_detail["host_ip"]:
                hy_detail["free_ram_mb"] = round(int(node["value"][1]) / 1024 / 1024)

        hy_detail["memory_mb_used"] = hy_detail["memory_mb"] - hy_detail["free_ram_mb"]


        return hy_detail

    @post(_path="/v1/cluster/config/dayu",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_cluster_config_dayu(self, form):

        client = Client()
        id = form["cluster_id"]

        agg_list = client.HypervisorsClient.openstack_get_aggregates_list(client, id)
        hy_list = client.HypervisorsClient.openstack_get_all_list(client)
        res = []
        with self.session_scope() as session:
            for hy in hy_list:
                if hy["hypervisor_hostname"] in agg_list.hosts:
                    hy_detail = client.HypervisorsClient.openstack_get_hy_87detail(client,hy["id"])
                    res.append(hy_detail)

                    cluster = ClusterHost()
                    cluster.cluster_id = id
                    cluster.ip = hy_detail["host_ip"]
                    cluster.cluster_name = agg_list.name
                    cluster.hostname = hy_detail["hypervisor_hostname"]
                    cluster.cpu_usage_upper = form["cpu_upper"]
                    cluster.memory_usage_upper = form["ram_upper"]
                    cluster.host_horizon_usage = form["host_horizon"]
                    cluster.migrate_type = form["migrate_type"]

                    clusterhost = session.query(ClusterHost).filter(ClusterHost.cluster_id == id).first()
                    if not clusterhost:
                        session.add(cluster)

                    else:
                        data = {}
                        data["cluster_id"] = id
                        data["ip"] = hy_detail["host_ip"]
                        data["cluster_name"] = agg_list.name
                        data["hostname"] = hy_detail["hypervisor_hostname"]
                        data["cpu_usage_upper"] = form["cpu_upper"]
                        data["memory_usage_upper"] = form["ram_upper"]
                        data["host_horizon_usage"] = form["host_horizon"]
                        data["migrate_type"] = form["migrate_type"]

                        session.query(ClusterHost).filter(ClusterHost.hostname == hy_detail["hypervisor_hostname"]).update(data)
                    session.commit()

        return {"msg":"ok"}

    @get(_path="/v1/all/clusters", _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_all_clusters(self):
        """
        ---
        tags:
          - 物理机相关接口
        summary: 查询全部主机机器详情
        responses:
          '200':
            description: 成功
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ArrayClusterDetailModel'
        """
        client = Client()

        hy_list = client.HypervisorsClient.openstack_get_all_cluster_detail(client)
        hy_list.sort(key=lambda x: x['hypervisor_hostname'])
        res = []

        c = Pclient()
        ext_ip_dict = {}
        external_ip_list = c.query_vector_by_query("thxh_theexport_docker_volume_status{volumeName='thedb'}")
        for bd in external_ip_list:
            ip = bd["metric"]["instance"].split(":")[0]
            ext_ip = bd["metric"]["externalIp"]
            ext_ip_dict[ip] = ext_ip

        total_ram_list = c.query_vector_by_query('node_memory_MemTotal_bytes{job="openstack_hypervisors"}')
        available_ram_list = c.query_vector_by_query(' node_memory_MemAvailable_bytes{job="openstack_hypervisors"}')
        cpu_percent_list = c.query_vector_by_query("(1 - sum(increase(node_cpu_seconds_total{job='openstack_hypervisors',mode='idle'}[5m])) by (instance) / sum(increase(node_cpu_seconds_total{job='openstack_hypervisors'}[5m])) by (instance) ) * 100")
        #for node in cpu_percent_list:
        #    res["usage_percent_cpu"] += float(node["value"][1])

        #res["usage_percent_cpu"] = res["usage_percent_cpu"] / len(cpu_percent_list) / 100

        with self.session_scope() as session:
            for hy in hy_list:
                host = session.query(ClusterHost).filter(ClusterHost.hostname == hy["hypervisor_hostname"]).first()
                hy["weihu_state"] = host.weihu_state if host else ""

        for hy in hy_list:
            for node in total_ram_list:
                if hy["host_ip"] == node["metric"]["instance"].split(":")[0] :
                    hy["memory_mb"] = round(int(node["value"][1]) / 1024 / 1024)

        for hy in hy_list:
            for node in available_ram_list:
                if hy["host_ip"] == node["metric"]["instance"].split(":")[0] :
                    hy["free_ram_mb"] = round(int(node["value"][1]) / 1024 / 1024)

        for hy in hy_list:
            hy["memory_mb_used"] = hy["memory_mb"] - hy["free_ram_mb"]

        for hy in hy_list:
            for node in cpu_percent_list:
                if hy["host_ip"] == node["metric"]["instance"].split(":")[0] :
                    hy["cpu_percent"] = float(node["value"][1])



        bandwidth_dict = {}
        download_bandwidth = c.query_vector_by_query("max(rate(node_network_receive_bytes_total{job='openstack_hypervisors'}[5m])*8) by (instance)")
        for bd in download_bandwidth:
            ip = bd["metric"]["instance"].split(":")[0]
            band = bd["value"][1]
            bandwidth_dict[ip] = {"download":round(float(band) / 1024 / 1024, 2)}
            print(bd)

        upload_bandwidth = c.query_vector_by_query("max(rate(node_network_transmit_bytes_total{job='openstack_hypervisors'}[5m])*8) by (instance)")
        for bd in upload_bandwidth:
            ip = bd["metric"]["instance"].split(":")[0]
            band = bd["value"][1]
            bandwidth_dict[ip]["upload"] = round(float(band) / 1024 / 1024, 2)

        for hy in hy_list:
            if hy["state"] == "up":
                hy_detail = client.HypervisorsClient.openstack_get_hy_detail(client, hy["id"])

                if hy_detail.get("state") == "up":
                    hy["time"] = hy_detail["uptime"].split("up")[1].split("user")[0][:-5]
                    #天
                    if len(hy["time"].split(",") ) > 1:
                        if len(hy["time"].split(",")[1].split(":")) > 1:
                            hy["time"] = "%s天%s小时%s分钟" % (hy["time"].split(",")[0].lstrip().split(" ")[0],hy["time"].split(",")[1].split(":")[0].lstrip(),hy["time"].split(",")[1].split(":")[1])
                        else:
                            hy["time"] = "%s天0小时%s分钟" % (hy["time"].split(",")[0].lstrip().split(" ")[0],hy["time"].split(",")[1].split(" ")[1])
                    else:
                        if len(hy["time"].split(":") ) > 1:
                            hy["time"] = "%s小时%s分钟" % ( hy["time"].lstrip().split(":")[0], hy["time"].lstrip().split(":")[1]  )
                        else:
                            hy["time"] = "%s分钟" % hy["time"].lstrip()[:-4]
                    ip = hy["host_ip"]
                    hy["download_bandwidth"] = bandwidth_dict.get(ip, {}).get("download", 0)
                    hy["upload_bandwidth"] = bandwidth_dict.get(ip, {}).get("upload", 0)

                    hy["ext_ip"] = ext_ip_dict.get(ip, "")

            else:
                hy["time"] = ""

        return hy_list

    @get(_path="/v1/screen/nodes", _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_all_nodes(self):

        client = Client()

        hy_list = client.HypervisorsClient.openstack_get_all_cluster_detail(client)
        res = {}
        hp_online , hp_offline ,vm_online , vm_offline = 0 , 0 , 0 , 0
        res["vm_deleted_count"] = 0
        for hp in hy_list:
            if hp["state"] == "up":
                hp_online += 1
            else :
                hp_offline += 1

        res["hypervisors"] = len(hy_list)
        res["hp_online"] = hp_online
        res["hp_offline"] = hp_offline

        vm_list = client.NovaClient.openstack_get_all_instance_detail(client)
        recycle_list = client.NovaClient.openstack_get_softdeleted_instance_detail(client,"")

        for vm in vm_list:
            if vm["status"] == "ACTIVE":
                vm_online += 1
            else :
                vm_offline += 1

        res["vms"] = len(vm_list) + len(recycle_list)
        res["vm_online"] = vm_online
        res["vm_offline"] = vm_offline + len(recycle_list)
        ins_detail = client.NovaClient.openstack_get_softdeleted_instance_detail(client, "")
        if ins_detail:
            res["vm_deleted_count"] = len(ins_detail)

        return res

    #@put(_path="/v1/add/clusters",_types=[ClusterHostForm],_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    @put(_path="/v1/add/clusters", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_put_add_cluster(self, form):
        """
        ---
        tags:
          - 物理机相关接口
        summary: 增加主机到集群中 增加主机到主机聚合中 参数 1.集群id 2.主机名
        requestBody:
          description: 删除
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RequestClusterHostModel'
          required: true
        responses:
          '200':
            description: 创建成功
            content: {}
        """
        role = self.get_cookie('username', "")
        cluster_id = form.get("id", "")
        name = form.get("name", "")
        hostname = form.get("hostname", "")

        try:
            client = Client()
            try:
                client.HypervisorsClient.openstack_delete_aggregate_host(client, cluster_id, hostname)
            except Exception as e:
                pass

            res = client.HypervisorsClient.openstack_add_aggregate_host(client, cluster_id, hostname)

            agg_list = client.HypervisorsClient.openstack_get_aggregates_list(client, cluster_id)
            hy_list = client.HypervisorsClient.openstack_get_all_list(client)

            with self.session_scope() as session:
                session.query(ClusterHost).filter(ClusterHost.hostname == hostname).update({"cluster_id":cluster_id})

            # logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
            #                 {"username": self.get_cookie('username', ""),
            #                  "op": "添加主机到集群",
            #                  "object": "%s %s" % (clusterform.id,clusterform.hostname),
            #                  "role": self.get_cookie('role', ""),
            #                  "result": "成功",
            #                  })
            new_logger.log(
                self.username, "物理机管理", "添加主机到集群", "成功", role, "添加主机 {} 到集群: {},成功".format(hostname, name)
            )
        except Exception as e:
                traceback.print_exc()
                # logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                #             {"username": self.get_cookie('username', ""),
                #              "op": "添加主机到集群",
                #              "object": "%s %s" % (clusterform.id,clusterform.hostname),
                #              "role": self.get_cookie('role', ""),
                #              "result": "失败",
                #              })
                new_logger.log(
                    self.username, "物理机管理", "添加主机到集群", "失败", role,
                    "添加主机 {} 到集群: {},失败".format(hostname, name)
                )
                self.set_status(502)
                return {"msg":"error"}
        return res

    # @put(_path="/v1/clusters/create",_types=[Cluster],_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    @put(_path="/v1/clusters/create", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_put_create_clusters(self, cluster):
        """
        ---
        tags:
          - 物理机相关接口
        summary: 新建集群 主机聚合 参数 1.集群名称
        requestBody:
          description: 创建主机聚合
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RequestCreateClusterModel'
          required: true
        responses:
          '200':
            description: 成功
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ClusterModel'
        """
        role = self.get_cookie('username', "")
        cluster_name = cluster.get("name", "")

        try:
            client = Client()
            res = client.HypervisorsClient.openstack_create_aggregate_v2(client, cluster)
            # logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
            #                 {"username": self.get_cookie('username', ""),
            #                  "op": "创建主机集群",
            #                  "object": cluster.name,
            #                  "role": self.get_cookie('role', ""),
            #                  "result": "成功",
            #                  })
            new_logger.log(
                self.username, "物理机管理", "创建主机集群", "成功", role,
                "创建主机集群: {},成功".format(cluster_name)
            )
        except Exception as e:

                traceback.print_exc()

                # logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                #             {"username": self.get_cookie('username', ""),
                #              "op": "创建主机集群",
                #              "object": cluster.name,
                #              "result": "失败",
                #              })
                new_logger.log(
                    self.username, "物理机管理", "创建主机集群", "失败", role,
                    "创建主机集群: {},失败".format(cluster_name)
                )
                self.set_status(502)
                return {"msg":"error"}
        return res

    # @delete(_path="/v1/clusters/delete",_types=[ClusterDeleteForm],_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    @delete(_path="/v1/clusters/delete",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_delete_cluster(self, clusterdeleteform):
        """
        ---
        tags:
          - 物理机相关接口
        summary: 删除集群 主机聚合 参数 1.集群id
        responses:
          '200':
            description: 创建成功
            content: {}
        """
        role = self.get_cookie('username', "")
        id = clusterdeleteform.get("id", "")
        name = clusterdeleteform.get("name", "")
        try:
            client = Client()

            agg_list = client.HypervisorsClient.openstack_get_aggregates_list(client, id)

            with self.session_scope() as session:
                for hostname in agg_list.hosts:
                    client.HypervisorsClient.openstack_delete_aggregate_host(client, id, hostname)
                    clusterhost = session.query(ClusterHost).filter(ClusterHost.hostname == hostname).update({"cluster_id":1})
                    session.commit()

            res = client.HypervisorsClient.openstack_delete_aggregate(client, id)
            # logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
            #                 {"username": self.get_cookie('username', ""),
            #                  "op": "删除主机集群",
            #                  "object": clusterdeleteform.id,
            #                  "role": self.get_cookie('role', ""),
            #                  "result": "成功",
            #                  })
            new_logger.log(
                self.username, "物理机管理", "删除主机集群", "成功", role,
                "删除主机集群: {},成功".format(name)
            )
        except Exception as e:

                # logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                #             {"username": self.get_cookie('username', ""),
                #              "op": "删除主机集群",
                #              "object": clusterdeleteform.id,
                #              "result": "失败",
                #              })
                new_logger.log(
                    self.username, "物理机管理", "删除主机集群", "失败", role,
                    "删除主机集群: {},失败".format(name)
                )

                traceback.print_exc()
                self.set_status(502)
                return {"msg":"error"}
        return res


class ClusterHostHandler(pyrestful.rest.RestHandler):
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        #self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, PUT, GET, DELETE, OPTIONS')
        #self.set_header("Access-Control-Allow-Headers", "access-control-allow-origin,authorization,content-type")

    # @put(_path="/v1/update/clusters/name",_types=[ClusterEditForm],_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    @put(_path="/v1/update/clusters/name", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_put_update_cluster_name(self, clustereditform):
        """
        ---
        tags:
          - 物理机相关接口
        summary: 修改集群名称
        requestBody:
          description: 修改集群名称
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ClusterEditModel'
          required: true
        responses:
          '200':
            description: 修改成功
            content: {}
        """
        role = self.get_cookie('username', "")
        id = clustereditform.get("id", "")
        name = clustereditform.get("name", "")
        try:
            client = Client()

            res = client.HypervisorsClient.openstack_update_aggregate_name(client, id, name)
            with self.session_scope() as session:
                for host in res["hosts"]:
                    data = {}
                    data["cluster_name"] = name
                    session.query(ClusterHost).filter(ClusterHost.hostname == host).update(data)

                session.commit()

            # logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
            #                 {"username": self.get_cookie('username', ""),
            #                  "op": "修改主机集群名称",
            #                  "object": "%s %s" % (clustereditform.id , clustereditform.name),
            #                  "role": self.get_cookie('role', ""),
            #                  "result": "成功",
            #                  })
            new_logger.log(
                self.username, "物理机管理", "修改主机集群名称", "成功", role,
                "修改主机集群: {},成功".format( name)
            )
        except Exception as e:
 
                # logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                #             {"username": self.get_cookie('username', ""),
                #              "op": "修改主机集群名称",
                #              "object": "%s %s" % (clustereditform.id , clustereditform.name),
                #              "role": self.get_cookie('role', ""),
                #              "result": "失败",
                #              })
                new_logger.log(
                    self.username, "物理机管理", "修改主机集群名称", "失败", role,
                    "修改主机集群: {},失败".format(name)
                )
                traceback.print_exc()
                self.set_status(502)
                return {"msg":"error"}
        return res

    # @delete(_path="/v1/delete/clusters/host",_types=[ClusterHostForm],_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    @delete(_path="/v1/delete/clusters/host", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_delete_host_cluster_host(self, clusterHost):
        """
        ---
        tags:
          - 物理机相关接口
        summary: 删除主机从集群 参数 1.集群id 2.主机名
        requestBody:
          description: 删除
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RequestClusterHostModel'
          required: true
        responses:
          '200':
            description: 创建成功
            content: {}
        """
        role = self.get_cookie('username', "")
        id = clusterHost.get("id", "")
        hostname = clusterHost.get("hostname", "")
        name = clusterHost.get("name", "")
        try:
            client = Client()
            res = client.HypervisorsClient.openstack_delete_aggregate_host(client, id, hostname)

            with self.session_scope() as session:
                clusterhost = session.query(ClusterHost).filter(ClusterHost.hostname == hostname).update({"cluster_id":1})
                session.commit()

            # logger.info("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
            #                 {"username": self.get_cookie('username', ""),
            #                  "op": "从主机集群移除主机",
            #                  "object": "%s %s" % (clusterHost.id , clusterHost.hostname),
            #                  "role": self.get_cookie('role', ""),
            #                  "result": "成功",
            #                  })
            new_logger.log(
                self.username, "物理机管理", "从主机集群移除主机", "成功", role,
                "从主机集群{} 移除主机 {} ,成功".format(name, hostname)
            )
        except Exception as e:
                # logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
                #             {"username": self.get_cookie('username', ""),
                #              "op": "从主机集群移除主机",
                #              "object": "%s %s" % (clusterHost.id , clusterHost.hostname),
                #              "role": self.get_cookie('role', ""),
                #              "result": "失败",
                #              })
                new_logger.log(
                    self.username, "物理机管理", "从主机集群移除主机", "失败", role,
                    "从主机集群{} 移除主机 {} ,失败".format(name, hostname)
                )
                traceback.print_exc()
                self.set_status(502)
                return {"msg":"error"}
        return res

    @post(_path="/v1/cluster/dayu/getconfig",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_cluster_dayu_byid(self, form):


        client = Client()
        id = form["cluster_id"]
        with self.session_scope() as session:
            clusterhost = session.query(ClusterHost).filter(ClusterHost.cluster_id == id).first()
            session.expunge(clusterhost)
        d = serialize(clusterhost)
        #r.append(d)
        return d

    @get(_path="/v1/auto/evacuate", _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_auto_evacuate(self):
        enabled_auto = "off"
        with self.session_scope() as session:
            clusterhost = session.query(ClusterHost).first()
            if clusterhost:
                enabled_auto = clusterhost.enabled_auto

        return {"enabled_auto" : enabled_auto}

    @post(_path="/v1/update/clusters/full", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_put_update_cluster_full(self, clustereditform):
        """
        ---
        tags:
          - 物理机相关接口
        summary: 修改集群完整信息（名称、可用区、元数据）
        requestBody:
          description: 修改集群完整信息
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ClusterEditModel'
          required: true
        responses:
          '200':
            description: 修改成功
            content: {}
        """
        role = self.get_cookie('username', "")
        id = clustereditform.get("id", "")
        name = clustereditform.get("name", "")
        availability_zone = clustereditform.get("name", "")
        metadata = clustereditform.get("name", "")

        try:
            client = Client()

            # 更新名称和可用区
            res = client.HypervisorsClient.openstack_update_aggregate_full(client, id, name, availability_zone)

            # 如果有元数据，单独更新元数据
            if metadata:
                client.HypervisorsClient.openstack_update_aggregate_metadata(client, id, metadata)

            # 更新数据库中的集群名称
            if name:
                with self.session_scope() as session:
                    for host in res["hosts"]:
                        data = {}
                        data["cluster_name"] = name
                        session.query(ClusterHost).filter(ClusterHost.hostname == host).update(data)

                    session.commit()

            update_info = []
            if name:
                update_info.append(f"名称: {name}")
            if availability_zone:
                update_info.append(f"可用区: {availability_zone}")
            if metadata:
                update_info.append(f"元数据: {metadata}")

            new_logger.log(
                self.username, "物理机管理", "修改主机集群完整信息", "成功", role,
                "修改主机集群: {},成功".format(", ".join(update_info))
            )
        except Exception as e:

            # logger.error("%(username)s;%(op)s;%(object)s;%(role)s;%(result)s", \
            #             {"username": self.get_cookie('username', ""),
            #              "op": "修改主机集群名称",
            #              "object": "%s %s" % (clustereditform.id , clustereditform.name),
            #              "role": self.get_cookie('role', ""),
            #              "result": "失败",
            #              })
            new_logger.log(
                self.username, "物理机管理", "修改主机集群名称", "失败", role,
                "修改主机集群: {},失败".format(name)
            )
            traceback.print_exc()
            self.set_status(502)
            return {"msg":"error"}
        return res


    @post(_path="/v1/auto/evacuate",  _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_auto_evacuate(self, form):
        role = self.get_cookie('username', "")
        enabled_auto = form["enabled_auto"]
        try:
            with self.session_scope() as session:
                session.query(ClusterHost).update({"enabled_auto": enabled_auto})
        except Exception as e:
            new_logger.log(
                self.username, "系统功能", "设置自动宕机迁移", "失败", role,
                "设置自动宕机迁移:{} 失败".format(enabled_auto)
            )
            self.set_status(502)
            return {"msg": "error"}
        new_logger.log(
            self.username, "系统功能", "设置自动宕机迁移", "成功", role,
            "设置自动宕机迁移:{} 成功".format(enabled_auto)
        )
        return {"msg" : "ok"}


    @post(_path="/v1/cloudy/addmaster",_types=[CloudyAddFrom],_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_add_master(self,form):

        print(form)

        cloudy = CloudyMaster()
        cloudy.cloudy_ip = form.cloudy_ip
        cloudy.cloudy_port = form.cloudy_port
        cloudy.cloudy_id = form.cloudy_id
        cloudy.cloudy_domain = form.cloudy_domain

        try:
            with self.session_scope() as session:
                cloudymaster = session.query(CloudyMaster).all()
                if len(cloudymaster) > 0:
                    session.query(CloudyMaster).filter(CloudyMaster.cloudy_domain==form.cloudy_domain).update(cloudy)
                else:
                    session.add(cloudy)

                session.commit()
        except Exception as e:
            return {"msg":"error"}

        return {"msg":"ok"}


    @post(_path="/v1/evacuate/host",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_evacuate_host(self, form):
        target_host = form.get("host", "")

        if target_host:
            client = Client()
            print(target_host)
            client.NovaClient.openstack_force_down_host(client, target_host)

            target_host_hypervisor = client.HypervisorsClient.openstack_get_hosts_all_vms(client, target_host)
            print(target_host_hypervisor)
            for down_host in target_host_hypervisor:
                state_host = down_host.get("state", "")
                if state_host == "down":
                    print(type(down_host))
                    for vm in down_host.get("servers"):
                        print(type(vm))
                        client = Client()
                        server_id = vm.uuid
                        print(server_id)
                        print(vm.name)
                        instance = client.NovaClient.openstack_get_server_detail(client, server_id)
                        if instance.get("status", "") in ["UP", "SHUTOFF"]:
                            client.NovaClient.openstack_evacuate_vm(client, server_id)
                            print("迁移了一台虚机: %s %s %s" % (vm.name, server_id, instance.get("status", "")))
                        else:
                            print("没有迁移了这台虚机: %s %s %s" % (vm.name, server_id, instance.get("status", "")))

        return {"msg":"ok"}


