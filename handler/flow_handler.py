# -*- coding: utf-8 -*-

import tornado.ioloop
import pyrestful.rest
import requests
import json
from pyrestful import mediatypes
from pyrestful.rest import get, post, put, delete


from util.cov import todict, serialize, theevent
from api.openstack.client import Client
from tornado_swagger.components import components
from mydoc.users import *
from db.model.cloudy import CloudyMaster
from db.model.vm import VmGroup , Vms
from sqlalchemy import and_
from db.model.event import Event
import bcrypt
from sqlalchemy.orm import defer
from sqlalchemy.orm import undefer

class FlowHandler(pyrestful.rest.RestHandler):
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        #self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, PUT, GET, DELETE, OPTIONS')
        #self.set_header("Access-Control-Allow-Headers", "access-control-allow-origin,authorization,content-type")


    @post(_path="/v1/apply/vms",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_add_user(self, body):
        method = "/v1/cloudy/apply/vms"
        
        try:
            with self.session_scope() as session:
                m = session.query(CloudyMaster).first()
            
            cloudy_url = "http://%s:%s/flow" % (m.cloudy_ip, m.cloudy_port) 
            url = "%s%s" % (cloudy_url, method)
            body["cloudy_id"] = m.cloudy_id
            
            r = requests.post(url, data=json.dumps(body))
            d = json.loads(r.text)
            
            if d.get("msg") != "ok":
                return {"msg": "error"}
            
        except Exception as e:
            return {"msg":"error"}
        return {"msg":"ok"}



                
