# -*- coding: utf-8 -*-
import traceback

import tornado.ioloop
import pyrestful.rest
from api.log.log import Custom<PERSON>ogger

from pyrestful import mediatypes
from pyrestful.rest import get, post, put, delete

from model.flavors import FlavorCreateFrom,FlavorDetailFrom
from util.cov import todict
from api.openstack.client import Client
from tornado_swagger.components import components
from mydoc.flavors import *

import logging

logger = logging.getLogger(__name__)
new_logger = CustomLogger()
class Securitygroupshandler(pyrestful.rest.RestHandler):
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        #self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, GET, DELETE, PUT, OPTIONS')
        #self.set_header("Access-Control-Allow-Headers", "access-control-allow-origin,authorization,content-type")

    @get(_path="/v1/securitygroups/list", _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_all_securitygroups(self):

        client = Client()
        res = client.SecuritygroupClient.openstack_get_all_securitygroup_list(client)
        return res   
    
    @post(_path="/v1/securitygroups/detail",_consumes=mediatypes.APPLICATION_JSON,  _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_securitygroup_detail(self,form):

        client = Client()
        res = client.SecuritygroupClient.openstack_get_securitygroup_detail(client,form["id"])

        return res
    
    @post(_path="/v1/securitygroups/create",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_create_securitygroup(self, form):
        response = {"msg": "ok"}
        role = self.get_cookie('role', "")
        try:
        
            client = Client()
            seclist = client.SecuritygroupClient.openstack_get_all_securitygroup_list(client)
            nowlist = []
            for sec in seclist:
                nowlist.append(sec["name"])

            if form["name"] not in nowlist:

                securitygroup = client.SecuritygroupClient.openstack_create_securitygroup(client,form.get("name", ""),form.get("desc", ""))

            new_logger.log(
                self.username, "安全组", "创建安全组", "成功", role, "创建安全组: {},成功".format(form.get("name", ""))
            )

        except Exception as e:
            error_message = str(e)
            new_logger.log(
                self.username, "安全组", "创建安全组", "失败", role, "创建安全组: {},失败".format(form.get("name", ""))
            )
            response = {"msg": "fail", "error": error_message}


        return response
        
    
    @post(_path="/v1/securitygroups/edit",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_edit_securitygroup(self, form):

        client = Client()
        res = client.SecuritygroupClient.openstack_edit_securitygroup(client,form["id"],form["name"],form["desc"])
        return {"msg":"ok"}
    
    @post(_path="/v1/securitygroups/delete",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_delete_securitygroups_delete(self, form):
        response = {"msg": "ok"}
        role = self.get_cookie('role', "")
        try:
            client = Client()
            res = client.SecuritygroupClient.openstack_delete_securitygroup(client, form["id"])
            if res["msg"] != "ok":
                new_logger.log(
                    self.username, "安全组", "删除安全组", "失败", role, "删除安全组: {},失败".format(form["name"])
                )
                return res
            new_logger.log(
                self.username, "安全组", "删除安全组", "成功", role, "删除安全组: {},成功".format(form["name"])
            )
        except Exception as e:
            error_message = str(e)
            new_logger.log(
                self.username, "安全组", "删除安全组", "失败", role, "删除安全组: {},失败".format(form["name"])
            )
            response = {"msg": "fail", "error": error_message}
        return response
    
    @post(_path="/v1/securitygroups/rule/create",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_create_securitygroup_rule(self, form):
        response = {"msg": "ok"}
        role = self.get_cookie('role', "")
        try:
            client = Client()
            if form["port_range_min"] == None:
                form["port_range_min"] = "None"
            if form["port_range_max"] == None:
                form["port_range_max"] = "None"

            if form["remote_group_id"] == "":
                securitygroup = client.SecuritygroupClient.openstack_create_securitygroup_rule_withip(client, form)
            elif form["remote_ip_prefix"] == "":
                securitygroup = client.SecuritygroupClient.openstack_create_securitygroup_rule_withsg(client, form)
            new_logger.log(
                self.username, "安全组", "安全组创建规则", "成功", role, "安全组创建规则: {},成功".format(form.get("security_group_name", ""))
            )
        except Exception as e:
            error_message = str(e)
            new_logger.log(
                self.username, "安全组", "安全组创建规则", "失败", role, "安全组创建规则: {},失败".format(form.get("security_group_name", ""))
            )
            traceback.print_exc()
            response = {"msg": "fail", "error": error_message}
            self.set_status(502)

        return response

    
    @post(_path="/v1/securitygroups/rule/delete",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_delete_securitygroup_rule(self, form):
        response = {"msg": "ok"}
        role = self.get_cookie('role', "")
        client = Client()
        ids = form["ids"]
        names = form["names"]
        # group_name = form["group_name"]
        for i in range(len(ids)):
            id = ids[i]
            name = names
            try:
                res = client.SecuritygroupClient.openstack_delete_securitygroup_rule(client, id)
                new_logger.log(
                    self.username, "安全组", "安全组删除规则", "成功", role, "删除安全组: {} 规则,成功".format(name)
                )
            except Exception as e:
                error_message = str(e)
                new_logger.log(
                    self.username, "安全组", "安全组删除规则", "失败", role, "删除安全组: {} 规则,失败".format(name)
                )
                response = {"msg": "fail", "error": error_message}

        return response
    
    @post(_path="/v1/securitygroups/getbyserver",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_securitygroup_by_server(self, form):

        client = Client() 
        res = client.SecuritygroupClient.openstack_get_securitygroup_byserver(client,form["id"])
        return res
    
    @post(_path="/v1/securitygroups/addtoserver",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_add_securitygroup_to_server(self, form):

        role = self.get_cookie('role', "")
        username = self.get_cookie('username', "")
        vm_name = form.get("vm_name", "")
        seclist_form = form.get("seclist", [])

        
        client = Client() 
        seclist = client.SecuritygroupClient.openstack_get_securitygroup_byserver(client,form["id"])
        nowlist = []
        for sec in seclist:
            nowlist.append(sec["name"])
        
        for name in form["seclist"]:
            if name not in nowlist:
                res = client.SecuritygroupClient.openstack_add_securitygroup_to_server(client,form["id"],name)
            
        
        for sec in seclist:
            if sec["name"] not in form["seclist"]:
                res = client.SecuritygroupClient.openstack_remove_securitygroup_from_server(client,form["id"],sec["name"])

        for sec in seclist_form:
            new_logger.log(
                self.username, "虚机管理", "选择安全组", "成功", role,
                "操作虚拟机：{} {}:[{}],成功".format(vm_name, "选择安全组", sec)
            )
        return {"msg":"ok"}

