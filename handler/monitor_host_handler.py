# -*- coding: utf-8 -*-

import tornado.ioloop
import pyrestful.rest
from api.prometheus.client import Client
from api.openstack.client import Client as OpenStackClient
from api.prometheus.client import Client as Pclient
from db.model.vm import VmGroup, Vms

from pyrestful import mediatypes
from pyrestful.rest import get, post, put, delete

from model.instances import Clusters, Instances
from model.user import UserForm, UserStatusForm
from util.cov import todict, serialize, theevent
from api.grafana.mod.host import HostClient, CephClient
from api.grafana.mod.vm import VmClient
from tornado_swagger.components import components
from mydoc.users import *
from db.model.user import User
from db.model.event import Event
import bcrypt
from sqlalchemy.orm import defer
from sqlalchemy.orm import undefer

class MonitorHostHandler(pyrestful.rest.RestHandler):
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        #self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, PUT, GET, DELETE, OPTIONS')
        #self.set_header("Access-Control-Allow-Headers", "access-control-allow-origin,authorization,content-type")

    @post(_path="/v1/monitor/host/cpu", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def monitor_get_host_cpu(self, form):
        client = Client()
        start = client.string_to_timestamp(form.get("start"))
        end = client.string_to_timestamp(form.get("end"))
        res = client.HostClient.get_host_cpu_usage_rate(client, start, end)
        res["unit"] = "%"
        return res
    

    @post(_path="/v1/monitor/host/mem", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def monitor_get_host_mem(self, form):
        client = Client()
        start = client.string_to_timestamp(form.get("start"))
        end = client.string_to_timestamp(form.get("end"))
        res = client.HostClient.get_host_mem_usage_rate(client, start, end)
        res["unit"] = "%"
        return res
    
    @post(_path="/v1/monitor/host/disk", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def monitor_get_host_disk(self, form):
        client = Client()
        start = client.string_to_timestamp(form.get("start"))
        end = client.string_to_timestamp(form.get("end"))
        res = client.HostClient.get_host_io_usage_rate(client, start, end)
        res["unit"] = "%"
        return res
    
    @post(_path="/v1/monitor/host/network", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def monitor_get_host_network(self, form):
        client = Client()
        start = client.string_to_timestamp(form.get("start"))
        end = client.string_to_timestamp(form.get("end"))
        res = client.HostClient.get_host_network(client, start, end)
        res["unit"] = "B"
        return res

class MonitorVMHandler(pyrestful.rest.RestHandler):
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        #self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, PUT, GET, DELETE, OPTIONS')
        #self.set_header("Access-Control-Allow-Headers", "access-control-allow-origin,authorization,content-type")


    @get(_path="/v1/monitor/vm/home", _produces=mediatypes.APPLICATION_JSON)
    def monitor_get_vm_home(self):

        client = OpenStackClient()

        # 获取所有节点
        hy_list = client.HypervisorsClient.openstack_get_all_cluster_detail(client)
        hy_list.sort(key=lambda x: x['hypervisor_hostname'])
        # 返回参数
        res = []

        # 处理每个主机信息
        for host in hy_list:
            node = {}
            node["hostname"] = host["hypervisor_hostname"]
            node["ip"] = host["host_ip"]
            node["trend"] = "cpu"
            node["time"] = 1
            node["chatdata"] = {}

            # 查询该主机openstack
            params = {"host": host["hypervisor_hostname"]}
            vmsdata = client.NovaClient.openstack_get_all_instance_detail_v2(client, params)


            count_normal = 0
            count_normal_list = []
            count_shutoff = 0
            count_shutoff_list = []
            count_alarm = 0
            count_alarm_list = []
            count_fault = 0
            count_fault_list = []

            client1 = Pclient()
            alert_res = client1.query_vector_by_target('active')

            # 遍历统计正常， 关机， 告警， 故障等数据
            for vm in vmsdata:
                rely = True
                if vm["vm_state"] == "active":
                    count_normal += 1
                    # 检查当前虚拟机是否存在于报警结果中
                    vm_ip = vm["ip"].split(',')[0]
                    vm_in_alerts = any(
                        item['health'] == 'down' for item in alert_res if vm_ip in item['labels']['instance'])
                    if vm_in_alerts:
                        rely = False
                    # 将结果添加到列表中
                    count_normal_dict = {'name': vm["name"], "ip": vm["ip"], "rely": rely}
                    count_normal_list.append(count_normal_dict)
                elif vm["vm_state"] == "stopped":
                    count_shutoff += 1
                    count_shutoff_dict = {'name': vm["name"], "ip": vm["ip"], "rely": rely}
                    count_shutoff_list.append(count_shutoff_dict)
                elif vm["vm_state"] == "suspended":
                    count_alarm += 1
                    count_alarm_dict = {'name': vm["name"], "ip": vm["ip"]}
                    count_alarm_list.append(count_alarm_dict)
                elif vm["vm_state"] == "error":
                    count_fault += 1
                    count_fault_dict = {'name': vm["name"], "ip": vm["ip"]}
                    count_fault_list.append(count_fault_dict)

            node["vm_count"] = len(vmsdata)
            node["count_normal"] = count_normal
            node["count_normal_list"] = count_normal_list
            node["count_shutoff"] = count_shutoff
            node["count_shutoff_list"] = count_shutoff_list
            node["count_alarm"] = count_alarm
            node["count_alarm_list"] = count_alarm_list
            node["count_fault"] = count_fault
            node["count_fault_list"] = count_fault_list
            res.append(node)
        return res


    @post(_path="/v1/monitor/vm/cpu", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def monitor_get_host_cpu(self, form):
        host_name = form.get("hostname")  # 节点名称
        query_str = self.get_query_str(host_name)
        if query_str == "":
            return {"time": [], "data": [], "unit": "%"}
        client = Client()
        start = client.string_to_timestamp(form.get("start"))
        end = client.string_to_timestamp(form.get("end"))
        res = client.VMClient.get_vm_cpu_usage_rate(client, start, end, query_str)
        if res.get("time") is None:
            res["time"] = []
        res["unit"] = "%"
        return res

    @post(_path="/v1/monitor/vm/with/user/cpu", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def monitor_get_host_with_user_cpu(self, form):
        """
        只针对操作员返回自己的监控数据
        """
        host_name = form.get("hostname")  # 节点名称
        username = self.get_cookie("username")

        # 用户虚拟机信息
        # vms = []
        # vms_dict = {}
        # with self.session_scope() as session:
        #     user = session.query(User).filter(User.username == username).first()  # V3.1版本临时修改 以后需要修改为username
        #     myvms = session.query(Vms).filter(Vms.user_id == user.id)
        #     client = OpenStackClient()
        #     vmsdata = client.NovaClient.openstack_get_all_instance_detail(client)
        #     for ins in vmsdata:
        #         vms_dict[ins.get("id")] = ins
        #
        #     for item in myvms:
        #         if vms_dict.get(item.vmid):
        #             vms.append(vms_dict.get(item.vmid))

        vms = self.get_vms(username)

        query_str = self.get_query_str(host_name)
        if query_str == "":
            return {"time": [], "data": [], "unit": "%"}
        client = Client()
        start = client.string_to_timestamp(form.get("start"))
        end = client.string_to_timestamp(form.get("end"))
        res = client.VMClient.get_vm_cpu_usage_rate(client, start, end, query_str)
        if res.get("time") is None:
            res["time"] = []
        res["unit"] = "%"

        # 筛选用户虚机数据
        data = []
        for d in res["data"]:
            for vm in vms:
                if vm['ip'] == d['title']:
                    data = data.append(d)

        res["data"] = data
        return res
    

    @post(_path="/v1/monitor/vm/mem", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def monitor_get_host_mem(self, form):
        host_name = form.get("hostname")  # 节点名称
        query_str = self.get_query_str(host_name)
        if query_str == "":
            return {"time": [], "data": [], "unit": "%"}
        client = Client()
        start = client.string_to_timestamp(form.get("start"))
        end = client.string_to_timestamp(form.get("end"))
        res = client.VMClient.get_vm_mem_usage_rate(client, start, end, query_str)
        if res.get("time") is None:
            res["time"] = []
        res["unit"] = "%"
        return res

    @post(_path="/v1/monitor/vm/with/user/mem", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def monitor_get_host_with_user_mem(self, form):
        host_name = form.get("hostname")  # 节点名称
        query_str = self.get_query_str(host_name)

        username = self.get_cookie("username")
        # 获取虚拟机信息
        vms = self.get_vms(username)

        if query_str == "":
            return {"time": [], "data": [], "unit": "%"}
        client = Client()
        start = client.string_to_timestamp(form.get("start"))
        end = client.string_to_timestamp(form.get("end"))
        res = client.VMClient.get_vm_mem_usage_rate(client, start, end, query_str)
        if res.get("time") is None:
            res["time"] = []
        res["unit"] = "%"

        # 筛选用户虚机数据
        data = []
        for d in res["data"]:
            for vm in vms:
                if vm['ip'] == d['title']:
                    data = data.append(d)

        res["data"] = data
        return res
    
    @post(_path="/v1/monitor/vm/disk", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def monitor_get_host_disk(self, form):
        host_name = form.get("hostname")  # 节点名称
        query_str = self.get_query_str(host_name)
        if query_str == "":
            return {"time": [], "data": [], "unit": "%"}
        client = Client()
        start = client.string_to_timestamp(form.get("start"))
        end = client.string_to_timestamp(form.get("end"))
        res = client.VMClient.get_vm_io_usage_rate(client, start, end, query_str)
        if res.get("time") is None:
            res["time"] = []
        res["unit"] = "%"
        return res

    @post(_path="/v1/monitor/vm/with/user/disk", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def monitor_get_host_with_user_disk(self, form):
        host_name = form.get("hostname")  # 节点名称
        query_str = self.get_query_str(host_name)
        if query_str == "":
            return {"time": [], "data": [], "unit": "%"}

        username = self.get_cookie("username")
        # 获取虚拟机信息
        vms = self.get_vms(username)

        client = Client()
        start = client.string_to_timestamp(form.get("start"))
        end = client.string_to_timestamp(form.get("end"))
        res = client.VMClient.get_vm_io_usage_rate(client, start, end, query_str)
        if res.get("time") is None:
            res["time"] = []
        res["unit"] = "%"

        # 筛选用户虚机数据
        data = []
        for d in res["data"]:
            for vm in vms:
                if vm['ip'] == d['title']:
                    data = data.append(d)

        res["data"] = data
        return res
    
    @post(_path="/v1/monitor/vm/network", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def monitor_get_host_network(self, form):
        host_name = form.get("hostname")  # 节点名称
        query_str = self.get_query_str(host_name)
        if query_str == "":
            return {"time": [], "data": [], "unit": "B"}
        client = Client()
        start = client.string_to_timestamp(form.get("start"))
        end = client.string_to_timestamp(form.get("end"))
        res = client.VMClient.get_vm_network(client, start, end, query_str)
        res["unit"] = "B"
        return res

    @post(_path="/v1/monitor/vm/with/user/network", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def monitor_get_host_with_user_network(self, form):
        host_name = form.get("hostname")  # 节点名称
        query_str = self.get_query_str(host_name)
        if query_str == "":
            return {"time": [], "data": [], "unit": "B"}

        username = self.get_cookie("username")
        # 获取虚拟机信息
        vms = self.get_vms(username)

        client = Client()
        start = client.string_to_timestamp(form.get("start"))
        end = client.string_to_timestamp(form.get("end"))
        res = client.VMClient.get_vm_network(client, start, end, query_str)
        res["unit"] = "B"

        # 筛选用户虚机数据
        data = []
        for d in res["data"]:
            for vm in vms:
                if vm['ip'] == d['title']:
                    data = data.append(d)

        res["data"] = data
        return res


    def get_query_str(self, hostname):
        # 获取正常的虚拟机id，拼接成prometheus查询字段
        openstack_client = OpenStackClient()
        params = {"host": hostname, "vm_state": "active"}
        vmsdata = openstack_client.NovaClient.openstack_get_all_instance_detail_v2(openstack_client, params)
        query_str = ''
        for vm in vmsdata:
            if vm["os_type"] == "windows":
                query_str += vm["ip"].split(',')[0] + ":9182|"
            else:
                query_str += vm["ip"].split(',')[0] + ":9100|"
        return query_str

    def get_vms(self, username):
        # 用户虚拟机信息
        vms = []
        vms_dict = {}
        with self.session_scope() as session:
            if username == "supadm":
                username="sysadm"
            user = session.query(User).filter(User.username == username).first()  # V3.1版本临时修改 以后需要修改为username
            myvms = session.query(Vms).filter(Vms.user_id == user.id)
            client = OpenStackClient()
            vmsdata = client.NovaClient.openstack_get_all_instance_detail(client)
            for ins in vmsdata:
                vms_dict[ins.get("id")] = ins

            for item in myvms:
                if vms_dict.get(item.vmid):
                    vms.append(vms_dict.get(item.vmid))

        return vms