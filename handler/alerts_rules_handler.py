# -*- coding: utf-8 -*-
import json

import yaml
from sqlalchemy import func

import pyrestful.rest
from Page.Pagenation import Pagenation
from api.log.log import CustomLogger
from db.model.alert_rules import AlertRule
from db.model.alert_rules_group import AlertRuleGroups
from pyrestful import mediatypes
from pyrestful.rest import get, post, put, delete
new_logger = CustomLogger()

class AlertRulesHandler(pyrestful.rest.RestHandler):
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        #self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, PUT, GET, DELETE, OPTIONS')
        #self.set_header("Access-Control-Allow-Headers", "access-control-allow-origin,authorization,content-type")

    @post(_path="/v1/alertsrules/all", _consumes=mediatypes.APPLICATION_JSON , _produces=mediatypes.APPLICATION_JSON)
    def hecloud_alerts_rules_query(self, form):
        try:
            # 解析请求中的分页和排序参数
            order_by = form.get("order_by", "")
            order_type = form.get("order_type", "desc")
            search_str = form.get("search_str", "")
            page = int(form.get("page", 1))
            pagecount = int(form.get("pagecount", 10))

            with self.session_scope() as session:
                # 查询所有规则
                all_rules = session.query(AlertRule).all()

                # 如果有搜索字符串，则过滤结果
                if search_str:
                    all_rules = [rule for rule in all_rules if search_str.lower() in rule.name.lower()]

                # 排序规则
                if order_type == "desc" and order_by:
                    all_rules = sorted(all_rules, key=lambda x: getattr(x, order_by), reverse=True)
                elif order_type == "asc" and order_by:
                    all_rules = sorted(all_rules, key=lambda x: getattr(x, order_by), reverse=False)

                # 分页
                pagenation = Pagenation(all_rules, page, pagecount)
                current_page_data = pagenation.show()
                total_pages = pagenation.total()

                # 准备返回的数据结构
                result = {
                    "total": len(all_rules),
                    "pages": total_pages,
                    "current_page": page,
                    "data": [rule.to_dict() for rule in current_page_data],
                }
                return result
        except Exception as e:
            self.set_status(502)  # Internal Server Error
            return {"msg": "error"}
    
    @post(_path="/v1/alertsrules/add", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hecloud_alerts_rules_add(self, data):
        role = self.get_cookie("role", "")
        # data = json.loads(self.request.body.decode('utf-8'))
        if not data:
            return {"msg": "error"}
        required_fields = ['name', 'expr', 'for']
        for field in required_fields:
            if field not in data:
                return {"msg": "error"}
        try:

            with self.session_scope() as session:
                # 更新数据库中的规则
                new_rule = AlertRule(
                    name=data['name'],
                    expr=data['expr'],
                    for_interval=data['for'],
                    value=data.get('value'),
                    critical_value=data.get('critical_value'),
                    major_value = data.get('major_value'),
                    warning_value = data.get('warning_value'),
                    info_value = data.get('info_value'),
                    expr_code = data.get('expr_code', "0"),
                    description=data.get('description'),
                    report_interval=data.get('report_interval', "10m"),
                    summary=data.get('summary'),
                    job=data.get('job'),
                )
                session.add(new_rule)
                session.commit()
            new_logger.log(
                self.username, "告警规则管理", "创建告警规则", "成功", role, "{}:{},成功".format("创建告警规则", data.get("name", ""))
            )
            return {"msg": "ok"}
        except Exception as e:
            new_logger.log(
                self.username, "告警规则管理", "创建告警规则", "失败", role, "{}:{},失败".format("创建告警规则", data.get("name", ""))
            )
            self.set_status(502)
            return {"msg": "error"}

    @put(_path="/v1/alertsrules/edit", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hecloud_alerts_rules_edit(self, data):
        role = self.get_cookie("role", "")
        # data = json.loads(self.request.body.decode('utf-8'))
        if not data:
            return {"msg": "error"}
            # 获取ID和确保ID存在
        id = data.get("id")
        if not id:
            return {"msg": "请提供要修改的规则ID"}
        try:
            with self.session_scope() as session:
                old_rule = session.query(AlertRule).filter(AlertRule.id == id).first()
                if not old_rule:
                    return {"msg": "找不到指定ID的规则"}

                # if old_rule.is_default == 1:
                #     return {"msg": "默认规则，不能修改"}

                # 更新数据库中的规则
                update_data = {}
                for field in ['name', 'expr', 'value', 'for_interval', 'report_interval', 'critical_value', 'major_value', 'warning_value', 'info_value', 'expr_code', 'description', 'summary', 'job']:
                    if field in data:
                        update_data[field] = data[field]

                session.query(AlertRule).filter(AlertRule.id == id).update(update_data)
                session.commit()
            new_logger.log(
                self.username, "告警规则管理", "修改告警规则", "成功", role, f"修改告警规则:{id},成功: {data['name']}"
            )
            return {"msg": "ok"}
        except Exception as e:
            # Log the exception for debugging purposes
            new_logger.log(
                self.username, "告警规则管理", "修改告警规则", "失败", role, f"修改告警规则:{id},失败: {data['name']}"
            )
            self.set_status(502)  # Internal Server Error
            return {"msg": "error"}

    @put(_path="/v1/alertsrules/status", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hecloud_alerts_rules_status(self, data):
        role = self.get_cookie("role", "")
        # data = json.loads(self.request.body.decode('utf-8'))
        if not data:
            return {"msg": "error"}
            # 获取ID和确保ID存在
        ids = data.get("ids")
        names = data.get("names")
        if not ids:
            return {"msg": "请提供要修改的规则ID"}
        try:
            for id, name in zip(ids, names):
                with self.session_scope() as session:
                    # 更新数据库中的规则
                    update_data = {}
                    for field in ['status']:
                        if field in data:
                            update_data[field] = data[field]

                    session.query(AlertRule).filter(AlertRule.id == id).update(update_data)
                    session.commit()
                new_logger.log(
                    self.username, "告警规则管理", "设置告警规则状态", "成功", role, f"设置告警规则状态:{id},成功: {name}"
                )
            return {"msg": "ok"}
        except Exception as e:
            # Log the exception for debugging purposes
            new_logger.log(
                self.username, "告警规则管理", "设置告警规则状态", "失败", role, f"设置告警规则状态失败: {names}"
            )
            self.set_status(502)  # Internal Server Error
            return {"msg": "error"}

    @delete(_path="/v1/alertsrules/delete", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hecloud_alerts_rules_delete_batch(self, form):
        role = self.get_cookie("role", "")
        try:
            ids_to_delete = form["ids"]

            with self.session_scope() as session:
                # 查询所有要删除的规则
                rules_to_delete = session.query(AlertRule).filter(AlertRule.id.in_(ids_to_delete)).all()

                # 过滤出非默认规则及其名称
                non_default_ids = []
                non_default_names = []

                for rule in rules_to_delete:
                    if rule.is_default != 1:
                        non_default_ids.append(rule.id)
                        non_default_names.append(rule.name)

                # 如果所有要删除的规则都是默认规则，则返回错误
                if not non_default_ids:
                    return {"msg": "所有指定的规则都是默认规则，不能删除"}

                # 批量删除非默认规则
                session.query(AlertRule).filter(AlertRule.id.in_(non_default_ids)).delete(synchronize_session='fetch')
                session.commit()

                # 日志记录
                deleted_names = ", ".join(non_default_names)
                print("告警规则管理", "批量删除告警规则", "成功", role, f"批量删除告警规则:{deleted_names},成功")
                new_logger.log(
                    self.username, "告警规则管理", "批量删除告警规则", "成功", role, f"批量删除告警规则:{deleted_names},成功"
                )

                return {"msg": "ok"}
        except Exception as e:
            new_logger.log(
                self.username, "告警规则管理", "批量删除告警规则", "失败", role, f"批量删除告警规则,失败"
            )
            self.set_status(502)  # Internal Server Error
            return {"msg": "error"}

class AlertRuleGroupsHandler(pyrestful.rest.RestHandler):
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, PUT, GET, DELETE, OPTIONS')

    @put(_path="/v1/alertsrulegroups/update", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def hecloud_alerts_rule_groups_update(self, data):
        role = self.get_cookie("role", "")
        try:
            if not data:
                return {"msg": "error"}

            with self.session_scope() as session:
                # 获取 id 为 1 的记录
                group = session.query(AlertRuleGroups).filter(AlertRuleGroups.id == 1).first()
                if not group:
                    return {"msg": "未找到指定ID的规则组"}

                # 更新数据库中的记录
                update_data = {}
                for field in ['rules', 'description', 'reserved1', 'reserved2']:
                    if field in data:
                        update_data[field] = data[field]

                session.query(AlertRuleGroups).filter(AlertRuleGroups.id == 1).update(update_data)
                session.commit()
            new_logger.log(
                self.username, "告警规则组管理", "更新告警规则组", "成功", role, f"更新告警规则组,成功 "
            )
            return {"msg": "ok"}
        except Exception as e:
            new_logger.log(
                self.username, "告警规则组管理", "更新告警规则组", "失败", role, f"更新告警规则组,失败"
            )
            self.set_status(500)  # Internal Server Error
            return {"msg": "error"}

    @post(_path="/v1/alertsrulegroups/default", _produces=mediatypes.APPLICATION_JSON)
    def hecloud_alerts_rule_groups_reset_default(self):
        role = self.get_cookie("role", "")
        try:
            with self.session_scope() as session:
                # 查询所有 is_default 为 True 的规则的 ID
                default_rule_ids = session.query(AlertRule.id).filter(AlertRule.is_default == True).all()

                # 将查询结果转换为逗号分隔的字符串
                default_rules_str = ",".join(str(id[0]) for id in default_rule_ids)

                # 获取 id 为 1 的规则组
                group = session.query(AlertRuleGroups).filter(AlertRuleGroups.id == 1).first()
                if not group:
                    return {"msg": "未找到指定ID的规则组"}

                # 更新规则组的 rules 字段
                group.rules = default_rules_str
                session.commit()

            new_logger.log(
                self.username, "告警规则组管理", "重置为默认规则", "成功", role, f"重置为默认规则,成功"
            )
            return {"msg": "ok"}
        except Exception as e:
            new_logger.log(
                self.username, "告警规则组管理", "重置为默认规则", "失败", role, f"重置为默认规则,失败"
            )
            self.set_status(502)  # Internal Server Error
            return {"msg": "error"}
