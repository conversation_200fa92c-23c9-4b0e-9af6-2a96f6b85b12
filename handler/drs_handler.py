# -*- coding: utf-8 -*-

import pyrestful.rest
from api.log.log import CustomLogger
from db.model.user import User
from db.model.vm_drs import VmDrs
from db.model.host_drs import ClusterDrs
from db.model.cluster import ClusterHost
from db.model.vm import Vms
from pyrestful import mediatypes
from pyrestful.rest import get, post, put, delete
from api.openstack.client import Client

new_logger = CustomLogger()

class DrsHandler(pyrestful.rest.RestHandler):
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        #self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, GET, DELETE, PUT, OPTIONS')
        #self.set_header("Access-Control-Allow-Headers", "access-control-allow-origin,authorization,content-type")

    @get(_path="/v1/drs/{vmid}", _produces=mediatypes.APPLICATION_JSON)
    def drs_query(self, vmid):
        try:
            with self.session_scope() as session:
                # 查询所有规则
                drs_detail = session.query(VmDrs).filter(VmDrs.vm_id == vmid).first()
                if drs_detail:
                    drs_detail_dict = {
                        'id': drs_detail.id,
                        'vm_id': drs_detail.vm_id,
                        'cpu': drs_detail.cpu,
                        'mem': drs_detail.mem,
                        'interval': drs_detail.interval,
                        'cpu_enabled': drs_detail.cpu_enabled,
                        'mem_enabled': drs_detail.mem_enabled,
                        'enabled': drs_detail.enabled
                    }
                    return {"data": drs_detail_dict}
                else:
                    return {"data": None}
        except Exception as e:
            self.set_status(502)  # Internal Server Error
            return {"msg": "error"}
    
    @get(_path="/v1/drs/vm/list", _produces=mediatypes.APPLICATION_JSON)
    def drs_list(self):
        try:
            client = Client()
            ins_detail = client.NovaClient.openstack_get_all_instance_detail(client)
            username = self.get_cookie("username", "")
            role = self.get_cookie("role", "")
            
            # 获取当前用户ID
            if hasattr(self, 'userid') and self.userid:
                user_id = self.userid

            with self.session_scope() as session:
                if username == "supadm":
                    # 系统管理员可以查看所有DRS记录
                    drs_records = session.query(VmDrs).filter(VmDrs.enabled == "true").all()
                else:
                    # 普通用户只能查看自己的VM相关的DRS记录
                    if username:
                        # 先查询用户拥有的VM列表
                        user = session.query(User).filter(User.username==username).first()
                        user_vms = session.query(Vms).filter(Vms.user_id == user.id).all()
                        vm_ids = [vm.vmid for vm in user_vms]
                        
                        # 然后查询这些VM相关的DRS记录
                        drs_records = session.query(VmDrs).filter(VmDrs.vm_id.in_(vm_ids)).filter(VmDrs.enabled == "true").all()#
                    else:
                        drs_records = []
                
                result = []
                for record in drs_records:
                    # 从OpenStack获取的实例详情中查找对应的VM名称
                    vm_name = ""
                    for instance in ins_detail:
                        if instance.get('id') == record.vm_id:
                            vm_name = instance.get('name', "")
                            break

                    if vm_name:
                        result.append({
                            'id': record.id,
                            'vm_id': record.vm_id,
                            'vm_name': vm_name,
                            'cpu': record.cpu,
                            'mem': record.mem,
                            'interval': record.interval,
                            'cpu_enabled': record.cpu_enabled,
                            'mem_enabled': record.mem_enabled,
                            'enabled': record.enabled
                        })
                
                return {"data": result}
        except Exception as e:
            self.set_status(502)  # Internal Server Error
            return {"msg": "error"}

    @post(_path="/v1/drs/update",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def drs_update(self, form):
        role = self.get_cookie("role", "")
        try:
            with self.session_scope() as session:
                not_allowed_fields = ['vm_name']
                filtered_form = {k: v for k, v in form.items() if k not in not_allowed_fields}

                drs_detail = session.query(VmDrs).filter(VmDrs.vm_id == form['vm_id']).first()
                if drs_detail is None:
                # 更新 VmDrs 实例
                    new_drs = VmDrs(**filtered_form)
                    session.add(new_drs)
                else:
                    session.query(VmDrs).filter(VmDrs.vm_id == form['vm_id']).update(filtered_form)
                session.commit()

            new_logger.log(
                self.username, "虚机操作", "动态资源扩展", "成功", role, f"修改动态资源扩展:成功: {form['vm_name']}"
            )
            return {"msg": "ok"}
        except Exception as e:
            new_logger.log(
                self.username, "虚机操作", "动态资源扩展", "失败", role, f"修改动态资源扩展:失败: {form['vm_name']}"
            )
            self.set_status(502)  # Internal Server Error
            return {"msg": "error"}

    @delete(_path="/v1/drs/delete",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def drs_delete(self, form):
        role = self.get_cookie("role", "")
        try:
            with self.session_scope() as session:
                # 删除对应规则
                session.query(VmDrs).filter(VmDrs.vm_id == form["vm_id"]).delete()
                session.commit()

            new_logger.log(
                self.username, "虚机操作", "动态资源扩展", "成功", role, f"取消设置动态资源扩展:成功: {form['vm_name']}"
            )
            return {"msg": "ok"}
        except Exception as e:
            new_logger.log(
                self.username, "虚机操作", "动态资源扩展", "失败", role, f"取消设置动态资源扩展:失败: {form['vm_name']}"
            )
            self.set_status(502)  # Internal Server Error
            return {"msg": "error"}

    @get(_path="/v1/cluster/drs", _produces=mediatypes.APPLICATION_JSON)
    def cluster_drs_query(self):
        try:
            with self.session_scope() as session:
                cluster_status = []
                cluster_hosts = session.query(ClusterHost).all()
                for host in cluster_hosts:
                    if host.enabled_auto == "on":
                        enabled_auto = True
                    else:
                        enabled_auto = False
                    cluster_status.append(enabled_auto)

                if cluster_status and all(cluster_status):
                    # 查询所有规则
                    drs_detail = session.query(ClusterDrs).order_by(ClusterDrs.id).first()
                    if drs_detail:
                        drs_detail_dict = {
                            "id": drs_detail.id,
                            "auto": drs_detail.auto,
                            "enabled": drs_detail.enabled,
                            "strategy": drs_detail.strategy,
                            "cpu_enabled": drs_detail.cpu_enabled,
                            "mem_enabled": drs_detail.mem_enabled
                        }
                        self.write({"data": drs_detail_dict})
                else:
                    self.write({"msg": "自动宕机迁移未启动"})

        except Exception as e:
            self.set_status(502)  # Internal Server Error
            self.write({"msg": "error"})
        finally:
            self.finish()

    @post(_path="/v1/cluster/drs/update",_consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def cluster_drs_update(self, form):
        role = self.get_cookie("role", "")
        try:
            with self.session_scope() as session:
                filtered_form = {k: v for k, v in form.items()}
                
                if 'id' not in form or form['id'] is None:
                    # If id is None, create a new record with id=1
                    filtered_form['id'] = 1
                    # Check if record with id=1 already exists
                    existing_record = session.query(ClusterDrs).filter(ClusterDrs.id == 1).first()
                    if existing_record:
                        # Update existing record with id=1
                        session.query(ClusterDrs).filter(ClusterDrs.id == 1).update(filtered_form)
                    else:
                        # Create new record with id=1
                        new_record = ClusterDrs(**filtered_form)
                        session.add(new_record)
                else:
                    # Update existing record with the specified id
                    session.query(ClusterDrs).filter(ClusterDrs.id == form['id']).update(filtered_form)
                
                session.commit()

            new_logger.log(
                self.username, "集群操作", "动态资源扩展", "成功", role, f"修改集群动态资源扩展:成功。"
            )
            self.write({"msg": "ok"})
        except Exception as e:
            new_logger.log(
                self.username, "集群操作", "动态资源扩展", "失败", role, f"修改集群动态资源扩展:失败。"
            )
            self.set_status(502)  # Internal Server Error
            self.write({"msg": "error"})
        finally:
            self.finish()
