# -*- coding: utf-8 -*-

import tornado.ioloop
import pyrestful.rest

from pyrestful import mediatypes
from pyrestful.rest import get, post, put, delete

from model.instances import Clusters, Instances
from model.hypervisors import Cluster, ClusterHostForm,ClusterEditForm,ClusterDeleteForm
from util.cov import todict
from api.ceph.client import Client
#from api.prometheus.client import PClient
from tornado_swagger.components import components
from mydoc.store import *

class CephHandler(pyrestful.rest.RestHandler):
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        #self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, PUT, GET, DELETE, OPTIONS')
        #self.set_header("Access-Control-Allow-Headers", "access-control-allow-origin,authorization,content-type")

    @get(_path="/v1/ceph/all", _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_all_ceph(self):
        pass
        """
        pclient = PClient()
        pool_list = pclient.get_ceph_pool_list()
        


        client = Client()
        res = client.FsClient.ceph_get_ceph_host(client)
        res_list = []
        for b in res:
            res_list.append(b.get("hostname"))
        for row in pool_list:
            row["hostname"] = ",".join(res_list)
            row["status"] = client.FsClient.ceph_get_ceph_health(client)
        
        return  pool_list
        """

    @get(_path="/v1/ceph/osd", _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_ceph_osd(self):
        client = Client()
        osd_list = client.OsdClient.ceph_osd_info(client)

        res = []
        dev_data = {}
        osd_data = {}
        for osd_ele in osd_list:
            host = osd_ele["host"]["name"]
            if not dev_data.get(host):
                dev_list = client.OsdClient.ceph_device_info(client, host)
                dev_data[host] = dev_list

            dev_list = dev_data.get(host)
            dev = dev_list[str(osd_ele["osd"])]
            osd_ele.update(dev)

            if osd_data.get(host):
                osd_data[host].append(osd_ele)
            else:
                osd_data[host] = [osd_ele]

        for host, osd in osd_data.items():
            res.append({"title": host, "osd_data": osd})

        res = sorted(res, key=lambda e: e["title"])

        return res
