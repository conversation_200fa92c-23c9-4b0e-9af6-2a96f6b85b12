'''
Created on Jan 20, 2022

@author: maojj
'''
import requests
import json
import settings
from api.model.instances import Clusters, Instances, InstanceDetail
from dataclasses import dataclass
from dacite import from_dict
import importlib
from pathlib import Path
from pkgutil import iter_modules
from inspect import isclass
import os
import urllib3
from vmware.vapi.vsphere.client import create_vsphere_client
from com.vmware.vcenter_client import VM
from com.vmware.vcenter.vm_client import Power
from six.moves import cStringIO
from vmware.vapi.bindings.struct import PrettyPrinter

    
class Client(object):
    
    client = None
    def __init__(self, ip, username, password):
        self.ip = ip
        self.username = username
        self.password = password
        
        
        self.client = self.openstatck_get_session()

        package_perfix= "api.vmware.mod"
        
        package_dir = os.path.abspath(os.path.join(__file__, '../mod'))
        for module_name in iter_modules([package_dir]):
            
            module_str = '%s.%s' % (package_perfix, module_name[1])
            module = importlib.import_module(module_str)

            for attribute_name in dir(module):
                attribute = getattr(module, attribute_name)
        
                if isclass(attribute) and attribute.__module__ == module_str:              
                    self.__dict__[attribute_name] = attribute
        

    def openstatck_get_session(self):
        session = requests.session()
        session.verify = False
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
        return create_vsphere_client(server=self.ip, username=self.username, password=self.password, session=session)


        
