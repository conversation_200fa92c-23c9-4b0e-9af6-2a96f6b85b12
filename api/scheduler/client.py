'''
Created on Nov 7, 2024

@author: qhz
'''
import requests
import settings


class Client():

    scheduler_url = settings.SCHEDULER_URI
    token         = ""

    def get_cluster_drs(self):
        method = "/v1/cluster/drs/table"
        url = "%s%s" % (self.scheduler_url, method)
        try:
            r = requests.get(url, timeout=(0.5, 1))
            return r.json()
        except Exception as e:
            print(e)
            return []

    def post_cluster_drs(self, form):
        method = "/v1/cluster/drs/table"
        url = "%s%s" % (self.scheduler_url, method)
        r = requests.post(url, json=form)
        return r.json()
