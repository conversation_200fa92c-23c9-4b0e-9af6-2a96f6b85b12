'''
Created on Sep 6, 2022

@author: ct
'''
import requests
from api.openstack.client import Client
from dataclasses import dataclass
from dacite import from_dict

name = "网络服务状态"
weigth = 20

@dataclass
class NetworkService(object):
    agent_type : str
    host : str
    admin_state_up : bool
    alive : bool
    
def check():
    print("check method")
    client = Client()
    services = client.NeutronClient.openstack_get_all_agent_list(client)
    data = []
    count = 0
    for service  in services:
        ins = from_dict(data_class=NetworkService, data=service)
        if ins.__dict__["alive"] == True:
            count = count + 1
        data.append(ins.__dict__)

    if count == len(services):
        res = "ok"
    elif count < len(services) and count > 0 :
        res = "warning"
    elif  count == 0 :
        res = "error"
            
    result = {
        "key": "network_service",
        "name": name,
        "weigth": weigth,
        "result": res,    # ok (V), warning (!) ,error (X)
        "columns": [
            {
                "title":"服务",
                "key":"agent_type",
                "tooltip":True
            },
            {
                "title":"管理状态",
                "key":"admin_state_up",
                "tooltip":True,
                "align":"center"
            },
            {
                "title":"运行状态",
                "key":"alive",
                "tooltip":True,
                "align":"center"
            },
            {
                "title":"主机",
                "key":"host",
                "tooltip":True,
                "align":"center"
            }
        ],
        "data": data
    }
    return result


