from api.prometheus.client import Client
import time

name = "存储空间状态"
weigth = 20

def check():
    
    client = Client()
    
    query_parm = 'max((node_filesystem_size_bytes-node_filesystem_free_bytes) *100/(node_filesystem_avail_bytes+(node_filesystem_size_bytes-node_filesystem_free_bytes)))by(instance)'
    res = client.query_vector_by_query(query_parm)
    
    
    for info in res:
        print(info)
    result_status = "ok"
    
    columns = [
            {
                "title":"主机IP",
                "key":"instance",
                "tooltip":True
            },
            {
                "title":"分区使用率",
                "key":"value",
                "tooltip":True,
                "align":"center"
            },
            {
                "title":"时间",
                "key":"date",
                "tooltip":True,
                "align":"center"
            }
        ]
    data = []
    
    
    for info in res:
        print(info)
        tmp = {}
        tmp["instance"] = info["metric"]["instance"]
        tmp["value"] = "%0.2f" % float(info["value"][1])
        tmp["date"] = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(info["value"][0]))
        data.append(tmp)
    
    result = {
        "key": "storage_status",
        "name": "存储空间状态",
        "weigth": 20,
        "columns": columns,
        "result":result_status,    # ok (V), warning (!) ,error (X)
        "data": data
    }
    return result