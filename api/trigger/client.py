'''
Created on Nov 7, 2024

@author: sjb
'''
import requests
import json
import settings
from string import Template

class Client(object):
    
    trigger_url = settings.TRIGGER_URI
    token = ""
    data = {}

    def get_autoexpansion_real_value(self):
        method = "/real/autoexpansion/value"
        url = "%s%s" % (self.trigger_url, method)
        try:
            r = requests.get(url,timeout=(0.5, 1))
            return r
        except Exception as e:
            print(e)

    def get_autoexpansion_info(self):
        method = "/v1/instance/autoexpansion/info"
        url = "%s%s" % (self.trigger_url, method)
        try:
            r = requests.get(url,timeout=(0.5, 1))
            return r
        except Exception as e:
            print(e)

    
    def post_autoexpansion_update(self, form):
        method = "/v1/instance/autoexpansion/update"
        url = "%s%s" % (self.trigger_url, method)

        dd = json.dumps(form)
        
        r = requests.post(url, data=dd, timeout=(2, 3))
        
        return r
