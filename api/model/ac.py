# -*- coding: utf-8 -*-


from dataclasses import dataclass
from sqlalchemy.engine import strategies
from bson import int64
from pygments.lexers import int_fiction


class ACGroupForm:
    usb: str
    clipboard: str

class ACUSBForm:
    global_deny: str
    global_allow: str
    byte_two_way_deny: int
    byte_two_way_allow: str
    byte_in_to_out_allow: str
    byte_out_to_in_allow: str
    file_two_way_deny: str
    file_two_way_allow: str
    file_in_to_out_allow: str
    file_out_to_in_allow: str







