# -*- coding: utf-8 -*-


from dataclasses import dataclass, field
from typing import List, Optional







class Cluster:
    name : str
    
class ClusterEditForm:
    id : int
    name: str
    
class ClusterDeleteForm:
    id : int

class ClusterHostForm(object):
    id : int
    hostname : str

class DeleteHostCluster(object):
    name : str

@dataclass
class Hypervisor(object):
    id : str
    hypervisor_hostname : str
    state : str
    status : str

@dataclass
class Server(object):
    name : str
    uuid : str

@dataclass
class Hypervisor_id(object):
    id : int
    hypervisor_hostname : str
    state : str
    status : str
    servers : List[Server] = field(default_factory=list)

@dataclass
class HypervisorStatistics(object):
    count : int
    vcpus : int
    memory_mb : int
    local_gb : int
    vcpus_used : int
    memory_mb_used : int
    local_gb_used : int
    free_ram_mb : int
    free_disk_gb : int
    current_workload : int
    running_vms : int
    disk_available_least : int

@dataclass
class HypervisorDetail(object):
    id : str
    hypervisor_hostname : str
    state : str
    status : str
    vcpus : int
    memory_mb : int
    local_gb : int
    vcpus_used: int
    memory_mb_used: int
    local_gb_used : int
    hypervisor_type : str
    hypervisor_version : int
    free_ram_mb : int
    free_disk_gb: int
    current_workload : int
    running_vms : int
    disk_available_least : int
    host_ip: str
    #cpu_info: str
    #uptime : str

@dataclass  
class HypervisorDetail_version(object):
    id : str
    hypervisor_hostname :str
    state : str
    status : str
    hypervisor_type : str
    hypervisor_version : int
    host_ip : str
    #service : list
    uptime : Optional[str]
    
@dataclass   
class Aggregate(object):
    id : int
    name : str
    created_at : str
    hosts : list
    
@dataclass   
class Aggregates(object):
    id : int
    name : str
    availability_zone : str
    created_at : str
    hosts : list
    
@dataclass   
class CreateAggregates(object):
    id : int
    name : str
    availability_zone : str
    created_at : str
