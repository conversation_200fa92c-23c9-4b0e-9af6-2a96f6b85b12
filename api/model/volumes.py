# -*- coding: utf-8 -*-

from typing import Optional
from dataclasses import dataclass, field

@dataclass
class Volume(object):
    id : str
    status : str
    size : int
    
@dataclass
class VolumeDetailforlist(object):
    id : str
    status : str
    name : str
    size : int
    attachments : list
    bootable : str
    volume_type : str
    snapshot_id : Optional[str] = ""
    
@dataclass
class VolumeDetail(object):
    id : str
    status : str
    name : str
    size : int
    attachments : list
    bootable : str
    volume_type : str
    #volume_image_metadata : dict
    #description : str

@dataclass
class VolumeTypeDetail(object):
    id : str
    name : str
    is_public : bool
   
    
@dataclass
class VolumeSnapshotDetail(object):
    id : str
    status : str
    name : str
    size : int
    volume_id : str
    created_at : str
    #description : str


class VolumesDetailFrom(object):
    page: int
    pagecount: int
    search: str
    
class VolumeCreateFrom:
    name : str
    size : int
    imageRef: str
    description : str
    
class VolumeCreateFromV2:
    name : str
    size : int
    imageRef: str
    volume_type: str
    description : str

class VolumeCloneForm:
    name: str
    size: int
    source_volid: str
    
class VolumeEditFrom:
    id : str
    name : str
    
class VolumeDeleteFrom:
    id : str
    
class VolumeActionFrom:
    id : str
    action : str
    data : str
    
class VolumeAttachFrom:
    id : str
    vmid : str
    mountpoint : str
    
@dataclass
class VolumeAgent(object):
    binary : str
    host : str
    status : str
    state : str
    updated_at : str
    
@dataclass
class VolumeType(object):
    id : str
    name : str
    extra_specs : Optional[dict] = field(default_factory=dict)
    description : Optional[str] = ""



    
