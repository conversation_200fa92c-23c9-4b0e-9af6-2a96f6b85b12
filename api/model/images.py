# -*- coding: utf-8 -*-


from dataclasses import dataclass


@dataclass
class Image(object):
    id : str
    name : str
    status: str
    disk_format: str
    visibility: str
    protected: bool
    
@dataclass
class ImageV2(object):
    id : str
    name : str
    status: str
    disk_format: str
    visibility: str
    protected: bool
    os_type: str = 'linux'
    
class ImageCreateFrom:
    disk_format : str
    name : str
    
class ImageEditFrom:
    id : str
    name : str
    
class ImageDeleteFrom:
    id : str
    
class ImageUploadFrom:
    id : str
    
@dataclass
class ImageDetail(object):
    id : str
    name : str
    file : str
    created_at : str
    updated_at : str
    protected : bool
    status : str
    visibility : str
    min_disk : int
    owner : str
    

    
    
    