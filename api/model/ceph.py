
from dataclasses import dataclass
from typing import Optional

@dataclass
class Osd(object):
    osd : int
    host : Optional[dict]
    osd_stats : Optional[dict]

    def __delattr__(self):
        self.__dict__['osd_stats'].pop('hb_peers')
        self.__dict__['osd_stats'].pop('num_shards_repaired')
        self.__dict__['osd_stats'].pop('num_snap_trimming')
        self.__dict__['osd_stats'].pop('op_queue_age_hist')
        self.__dict__['osd_stats'].pop('snap_trim_queue_len')
        self.__dict__['osd_stats'].pop('statfs')
        self.__dict__['osd_stats'].pop('perf_stat')
