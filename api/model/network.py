# -*- coding: utf-8 -*-


from dataclasses import dataclass
from builtins import int, bool



@dataclass
class Network(object):
    id : str
    name : str
    created_at : str
    updated_at : str
    mtu : int
    shared : bool
    status : str
    subnets : list

@dataclass
class Port(object):
    id : str
    name : str
    status : str
    fixed_ips : list


class NetworkCreateForm:
    name : str
    enable_dhcp : str
    cidr : str
    gateway_ip : str
    
class NetworkCreatePhyicalForm:
    name : str
    cidr : str
    vlanid : str
    bridge : str
    bonding : str
    devinfo : str
    speed : str
    type : str
    
class NetworkUpdatePhyicalForm:
    id : str
    name : str
    cidr : str
    vlanid : str
    bridge : str
    bonding : str
    devinfo : str
    speed : str
    type : str
    
class NetworkEditForm:
    name : str
    id : str
    
class AdminNetworkCreateForm:
    name : str
    network_type : str
    segmentation_id : int
    enable_dhcp : bool
    cidr : str
    gateway_ip : str


@dataclass
class NetworkDetail(object):
    id : str
    name : str
    created_at : str
    updated_at : str
    mtu : int
    shared : bool
    status : str
    
class NetworkDeleteFrom:
    id : str
    
@dataclass
class SubnetDetail(object):
    id : str
    name : str
    created_at : str
    updated_at : str
    network_id : str
    ip_version : int
    enable_dhcp : bool
    cidr : str
    
@dataclass
class NetworkAgent(object):
    id : str
    agent_type : str
    host : str
    admin_state_up : bool
    alive : int

@dataclass
class Vms_for_topology(object):
    id : str
    fixed_ips : list
    status : str
    
@dataclass
class PolicyDetail(object):
    id : str
    name : str
    created_at : str
    updated_at : str
    rules : list
   
