import requests
import json
import settings
from api.model.hosts import Host
from dataclasses import dataclass
from dacite import from_dict
from string import Template
import time
from datetime import datetime, timedelta
from api.grafana.client import Client
import numpy as np


class CephClient(Client):

    def grafana_get_ceph_iops(self):

        #query = """increase(node_network_receive_bytes_total{instance=~"%s:9100",device=~"(%s)"}[30m])""" % (ip, devices)
        query = """sum without (instance, ceph_daemon) (irate(ceph_osd_op_r[5m]))"""
        res = self.query_pormQL_range(query)
        data = res["data"]["result"][0]["values"]
        t = [time.strftime("%H:%M",  time.localtime(d[0])) for d in data]
        dd = np.array([])
        for d in res["data"]["result"]:
            tmp = [float(d[1]) for d in d["values"]]
            if dd.tolist():
                dd = dd + np.array(tmp)
            else:
                dd = np.array(tmp)
        
        #query = """increase(node_network_transmit_bytes_total{instance=~"%s:9100",device=~"(%s)"}[30m])""" % (ip, devices)
        query = """sum without (instance, ceph_daemon) (irate(ceph_osd_op_w[5m]))"""
        res = self.query_pormQL_range(query)
        uu = np.array([])
        for d in res["data"]["result"]:
            tmp = [float(d[1]) for d in d["values"]]
            if uu.tolist():
                uu = uu + np.array(tmp)
            else:
                uu = np.array(tmp)        
        
        uu = np.around(uu, 0)
        dd = np.around(dd, 0)     
        return {"t":t, "u": uu.tolist(), "d":dd.tolist()}

    def grafana_get_ceph_throughput(self):
        #query = """increase(node_network_receive_bytes_total{instance=~"%s:9100",device=~"(%s)"}[30m])""" % (ip, devices)
        query = """sum without (instance, ceph_daemon) (irate(ceph_osd_op_r_out_bytes[5m]))"""
        res = self.query_pormQL_range(query)
        data = res["data"]["result"][0]["values"]
        t = [time.strftime("%H:%M",  time.localtime(d[0])) for d in data]
        dd = np.array([])
        for d in res["data"]["result"]:
            tmp = [float(d[1]) for d in d["values"]]
            if dd.tolist():
                dd = dd + np.array(tmp)
            else:
                dd = np.array(tmp)
        
        #query = """increase(node_network_transmit_bytes_total{instance=~"%s:9100",device=~"(%s)"}[30m])""" % (ip, devices)
        query = """sum without (instance, ceph_daemon) (irate(ceph_osd_op_w_in_bytes[5m]))"""
        res = self.query_pormQL_range(query)
        uu = np.array([])
        for d in res["data"]["result"]:
            tmp = [float(d[1]) for d in d["values"]]
            if uu.tolist():
                uu = uu + np.array(tmp) 
            else:
                uu = np.array(tmp)        
        uu = uu / (1024 * 1024)
        uu = np.around(uu, 0)
        dd = dd / (1024 * 1024)
        dd = np.around(dd, 0)     
        return {"t":t, "u": uu.tolist(), "d":dd.tolist()}


class HostClient(Client):

    def grafana_get_host_cpu(self, vmid):

        query = """sum(node_load5{instance="%s:9100"})""" % vmid
        res = self.query_pormQL_range(query)
        #data = json.loads(res)
        data = res["data"]["result"][0]["values"]
        v = [float(d[1]) for d in data]
        
        t = [time.strftime("%H:%M",  time.localtime(d[0])) for d in data]
        #t = [d[0] for d in data]
        
        v = np.around(v, 2) 
        return {"v":v.tolist(), "t":t}
    
    
    def grafana_get_host_mem_v2(self, ip):
        query = """(sum(node_memory_MemTotal_bytes{instance="%s:9100"} - node_memory_MemAvailable_bytes{instance="%s:9100"}) / sum(node_memory_MemTotal_bytes{instance="%s:9100"}))*100""" % (ip, ip, ip)
        #query = """avg(1 - avg(rate(node_cpu_seconds_total{instance="%s:9100",mode="idle"}[30])) by (instance)) * 100""" % ip
        
        res = self.query_pormQL_range(query)
        #data = json.loads(res)
        data = res["data"]["result"][0]["values"]
        v = [float(d[1]) for d in data]
        
        t = [time.strftime("%H:%M",  time.localtime(d[0])) for d in data]
        #t = [d[0] for d in data]
        
        v = np.around(v, 2) 
        return {"v":v.tolist(), "t":t} 
    
    
    def grafana_get_host_mem(self, ip):
        query = """sum(node_memory_MemTotal_bytes{instance="%s:9100"} - node_memory_MemAvailable_bytes{instance="%s:9100"})""" % (ip,ip)
        #query = """avg(1 - avg(rate(node_cpu_seconds_total{instance="%s:9100",mode="idle"}[30])) by (instance)) * 100""" % ip
        
        res = self.query_pormQL_range(query)
        data = res["data"]["result"][0]["values"]
        v = [float(d[1]) / (1024 ** 2) for d in data]
        
        t = [time.strftime("%H:%M",  time.localtime(d[0])) for d in data]
        v = np.around(v, 2)
        return {"v":v.tolist(), "t":t}

    def grafana_get_host_network(self, ip, devices):
        #query = """increase(node_network_receive_bytes_total{instance=~"%s:9100",device=~"(%s)"}[30m])""" % (ip, devices)
        query = """rate(node_network_receive_bytes_total{instance=~'%s:9100',device=~"%s"}[5m])*8""" % (ip, devices)
        res = self.query_pormQL_range(query)
        data = res["data"]["result"][0]["values"]
        t = [time.strftime("%H:%M",  time.localtime(d[0])) for d in data]
        dd = np.array([])
        if res["data"]["result"]:
            for d in res["data"]["result"]:
                tmp = [float(d[1])/1000.0/1000.0 for d in d["values"]]
                # if dd.tolist():
                #     dd = dd + np.array(tmp)
                #else:
                #    dd = np.array(tmp)
                if dd.size == 0:
                    dd = np.array(tmp)
                else:
                    dd = np.concatenate((dd, np.array(tmp)))
        
        #query = """increase(node_network_transmit_bytes_total{instance=~"%s:9100",device=~"(%s)"}[30m])""" % (ip, devices)
        query = """rate(node_network_transmit_bytes_total{instance=~'%s:9100',device=~"%s"}[5m])*8""" % (ip, devices)
        res = self.query_pormQL_range(query)
        uu = np.array([])
        if res["data"]["result"]:
            for d in res["data"]["result"]:
                tmp = [float(d[1])/1000.0/1000.0 for d in d["values"]]
                # if uu.tolist():
                #     uu = uu + np.array(tmp)
                # else:
                #     uu = np.array(tmp)
                if uu.size == 0:
                    uu = np.array(tmp)
                else:
                    uu = np.concatenate((uu, np.array(tmp)))
        
        uu = np.around(uu, 2) * -1
        dd = np.around(dd, 2)    
        return {"t":t, "u": uu.tolist(), "d":dd.tolist()}

    def grafana_get_host_device(self, ip):
        query = """node_network_info{instance='%s:9100',device!~'tap.*|veth.*|br.*|docker.*|virbr.*|lo.*|cni.*'}""" % ip
        d = []
        res = self.query_pormQL_point(query)
        
        for v  in res["data"]["result"]:
            d.append(v["metric"]["device"])
        
        return "|".join(d)

    

        
        