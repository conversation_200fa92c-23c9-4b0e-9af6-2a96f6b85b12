import time

import requests
import json
import settings
from api.model.images import Image,ImageV2,ImageDetail
from dataclasses import dataclass
from dacite import from_dict, Config
from string import Template



class ImageClient:
    def openstack_test(self, id):
        print(id)

    def openstack_get_all_list(self):
        method = "v2/images?visibility=all&limit=999" 
        url = "%s%s" % (self.glance_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "X-OpenStack-Glance-API-Version": "2.9"
        }
        
        r = requests.get(url, headers=headers)
        
        d = json.loads(r.text)
        res = []
        for ins_dict in d["images"]:
            ins = from_dict(data_class=Image, data=ins_dict)
            if ins_dict["status"] == "active":
                ins.__dict__["size"] = ins_dict["size"]
            res.append(ins.__dict__)
        return res

    def openstack_get_all_list_v2(self):
        method = "v2/images?visibility=all&limit=999" 
        url = "%s%s" % (self.glance_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "X-OpenStack-Glance-API-Version": "2.9"
        }
        
        r = requests.get(url, headers=headers)
        
        d = json.loads(r.text)
        print(d)
        res = []
        for ins_dict in d["images"]:
            ins = from_dict(data_class=ImageV2, data=ins_dict)
            if ins_dict["status"] == "active":
                ins.__dict__["size"] = ins_dict["size"]
            res.append(ins.__dict__)
        return res
        
    def openstack_get_image_detail(self,id):
        method = "v2/images/%s" % id
        url = "%s%s" % (self.glance_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        
        r = requests.get(url, headers=headers)
        if r.status_code != 200:
            ins = ImageDetail
            ins.name = ""
            # time.sleep(300)
            return ins.__dict__
        d = json.loads(r.text)
        ins = from_dict(data_class=ImageDetail, data=d)
        return ins.__dict__

    
    def openstack_create_image(self, imagecreateform):
        method = "v2/images"
        url = "%s%s" % (self.glance_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
    
        temp = """{
                    "container_format": "bare",
                    "disk_format": "$disk_format",
                    "name": "$name"
            }"""
        
        t = Template(temp)
        body = t.substitute(imagecreateform.__dict__)
        
        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)
        
        d = json.loads(r.text)
        
        ins = from_dict(data_class=ImageDetail, data=d)
        return ins.__dict__
    
    def openstack_create_image_v2(self, imagecreateform):
        method = "v2/images"
        url = "%s%s" % (self.glance_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
    
        temp = """{
                    "container_format": "bare",
                    "disk_format": "$disk_format",
                    "name": "$name",
                    "os_type": "$os_type"
            }"""
        
        t = Template(temp)
        body = t.substitute(imagecreateform)
        
        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)
        
        d = json.loads(r.text)
        
        ins = from_dict(data_class=ImageDetail, data=d)
        return ins.__dict__
        
    def openstack_delete_image(self, id):
        method = "v2/images/%s" % id
        url = "%s%s" % (self.glance_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        
        r = requests.delete(url, headers=headers)
        
        if r.status_code == 204:
            return {"msg": "ok"}
        if r.status_code == 409:
            return {"msg": "409"}
        
        return {"msg": "error"}
        
    def openstack_edit_image(self, id,name):
        method = "v2/images/%s" % id
        url = "%s%s" % (self.glance_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Content-Type":"application/openstack-images-v2.1-json-patch"
        }
                 
        temp = """[
                {
                    "op": "replace",
                    "path": "/name",
                    "value": "$name"
                }
            ]"""
        
        t = Template(temp)
        body = t.substitute({"name":name})
        
        dd = json.dumps(json.loads(body))
        r = requests.patch(url, data=dd, headers=headers)
        
        d = json.loads(r.text)
        
        ins = from_dict(data_class=ImageDetail, data=d)
        return ins.__dict__
        
    def openstack_update_image_os_type(self, id, os_type):
        method = "v2/images/%s" % id
        url = "%s%s" % (self.glance_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Content-Type":"application/openstack-images-v2.1-json-patch"
        }
                 
        temp = """[
                {
                    "op": "replace",
                    "path": "/os_type",
                    "value": "$os_type"
                }
            ]"""
        
        t = Template(temp)
        body = t.substitute({"os_type":os_type})
        
        dd = json.dumps(json.loads(body))
        r = requests.patch(url, data=dd, headers=headers)
        
        if r.status_code == 409:
            
            temp = """[
                    {
                        "op": "add",
                        "path": "/os_type",
                        "value": "$os_type"
                    }
                ]"""
            
            t = Template(temp)
            body = t.substitute({"os_type":os_type})
            
            dd = json.dumps(json.loads(body))
            r = requests.patch(url, data=dd, headers=headers)

        print(r.status_code)
        print(r.text)
        #ins = from_dict(data_class=ImageDetail, data=d)
        return {"msg": "ok"}
    
    
    def openstack_upload_image(self, image_id, data):
        method = "v2/images/%s/file" % image_id
        url = "%s%s" % (self.glance_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Content-Type":"application/octet-stream"
            
        }
        
        r = requests.put(url, headers=headers, data=data)
        
        if r.status_code == 204:
            return {"msg": "ok"}
        return {"msg": "error"}
        
    def openstack_upload_mock_file(self, image_id):
        method = "v2/images/%s/file" % image_id
        url = "%s%s" % (self.glance_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Content-Type":"application/octet-stream"
            
        }
         
         
        data = b'\x00\x01'
        r = requests.put(url, headers=headers, data=data)
        
        if r.status_code == 204:
            return {"msg": "ok"}

        return {"msg": "error"}

    def openstack_upload_image_new(self, image_id, data, content_range):
        method = "v2/images/%s/file" % image_id
        url = "%s%s" % (self.glance_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Content-Type":"application/octet-stream",
            "Content-Range":content_range
        }
        
        r = requests.put(url, headers=headers, files=data)
        
        if r.status_code == 204:
            return {"msg": "ok"}
            
        return {"msg": "error"}
    
    def openstack_reactivate_image(self, image_id):
        method = "/v2/images/%s/actions/reactivate" % image_id
        url = "%s%s" % (self.glance_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        data = """{
            "image_id": "%s"
        }""" % image_id       
        r = requests.post(url, headers=headers)
        print(r.text)
        if r.status_code == 204:
            return {"msg": "ok"}
        return {"msg": "error"}   
    
    def openstack_image_size(self, image_id, size):
        method = "v2/images/%s/tags/%s" % (image_id,size)
        url = "%s%s" % (self.glance_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        
        r = requests.put(url, headers=headers)
        
        if r.status_code == 204:
            return {"msg": "ok"}
            
        return {"msg": "error"}
    
    def openstack_get_all_image_list(self):
        method = "v2/images?visibility=shared&limit=999" 
        url = "%s%s" % (self.glance_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "X-OpenStack-Glance-API-Version": "2.9"
        }
        
        r = requests.get(url, headers=headers)
        
        d = json.loads(r.text)
        res = []
        for ins_dict in d["images"]:
            ins = from_dict(data_class=Image, data=ins_dict)
            if ins_dict["status"] == "active":
                ins.__dict__["size"] = ins_dict["size"]
            res.append(ins.__dict__)
        return res
        