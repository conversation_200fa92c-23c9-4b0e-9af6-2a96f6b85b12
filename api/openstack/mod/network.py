
import requests
import json
import settings
from api.model.network import Network, NetworkDetail,SubnetDetail,Port,NetworkAgent,Vms_for_topology,PolicyDetail
from dataclasses import dataclass
from dacite import from_dict
from string import Template
from datetime import *
from dateutil import tz,zoneinfo


class NeutronClient:
    def openstack_test(self, id):
        print(id)

    def openstack_get_all_list(self):
        method = "/v2.0/networks"
        url = "%s%s" % (self.neutron_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        
        r = requests.get(url, headers=headers)
        
        d = json.loads(r.text)
        res = []
        for ins_dict in d["networks"]:
            ins = from_dict(data_class=Network, data=ins_dict)
            ins.__dict__["network_type"] = ins_dict["provider:network_type"]
            ins.__dict__["vlanid"] = ins_dict.get("provider:segmentation_id","")
            res.append(ins.__dict__)
        return res

    def openstack_get_network_detail(self, id):
        method = "/v2.0/networks/%s" % id
        url = "%s%s" % (self.neutron_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        r = requests.get(url, headers=headers)

        d = json.loads(r.text)
        return d

    def openstack_delete_port(self, port_id):
        method = "/v2.0/ports/%s" % port_id
        url = "%s%s" % (self.neutron_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        r = requests.delete(url, headers=headers)

        if r.status_code == 204:
            return {"msg": "ok"}
        else:
            return {"msg": "error"}
    
    def openstack_create_network(self, networkcreateform):
        method = "/v2.0/networks"
        url = "%s%s" % (self.neutron_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        
        temp = """{
                "network": {
                    "name": "$name",
                    "admin_state_up": "true"
                }
            }"""
        
        t = Template(temp)
        body = t.substitute(networkcreateform.__dict__)
        
        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)
        
        d = json.loads(r.text)
        
        ins = from_dict(data_class=NetworkDetail, data=d["network"])
        return ins.__dict__
    
    def openstack_edit_network(self, networkeditform):
        method = "/v2.0/networks/%s" % networkeditform.id
        url = "%s%s" % (self.neutron_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        
        temp = """{
                "network": {
                    "name": "$name"
                }
            }"""
        
        t = Template(temp)
        body = t.substitute({"name":networkeditform.name})
        
        dd = json.dumps(json.loads(body))
        r = requests.put(url, data=dd, headers=headers)
        
        d = json.loads(r.text)
        
        ins = from_dict(data_class=NetworkDetail, data=d["network"])
        return ins.__dict__

    def openstack_edit_network_v2(self, networkeditform):
        id = networkeditform.get("id", "")
        name = networkeditform.get("name", "")
        method = "/v2.0/networks/%s" % id
        url = "%s%s" % (self.neutron_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        temp = """{
                "network": {
                    "name": "$name"
                }
            }"""

        t = Template(temp)
        body = t.substitute({"name": name})

        dd = json.dumps(json.loads(body))
        r = requests.put(url, data=dd, headers=headers)

        d = json.loads(r.text)

        ins = from_dict(data_class=NetworkDetail, data=d["network"])
        return ins.__dict__

    def openstack_create_subnet_withdhcp(self, id , form):
        method = "/v2.0/subnets"
        url = "%s%s" % (self.neutron_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        
        """
        if form.host_routes:
            host_routes = form.host_routes.split(",")
        else:
            host_routes = []
            
        if form.dns_nameservers:
            dns_nameservers = form.dns_nameservers.split(",")
        else:
            dns_nameservers = []
        """

        if form.get("dns_nameservers", ""):
            dns_nameservers = form.get("dns_nameservers", "").replace(',', '","')
        else:
            dns_nameservers = ""

        
        if form.get("startip", ""):
            if dns_nameservers == "":
                temp = """{
                                "subnet": {
                                    "name": "$name",
                                    "network_id": "$id",
                                    "enable_dhcp": "true",
                                    "cidr": "$cidr",
                                    "gateway_ip": "$gateway_ip",
                                    "ip_version": 4,
                                    "allocation_pools":[
                                    {
                                        "start":"$start",
                                        "end":"$end"
                                    }],
                                    "host_routes": $host_routes
                                }
                            }"""
            else:
                temp = """{
                                "subnet": {
                                    "name": "$name",
                                    "network_id": "$id",
                                    "enable_dhcp": "true",
                                    "cidr": "$cidr",
                                    "gateway_ip": "$gateway_ip",
                                    "ip_version": 4,
                                    "allocation_pools":[
                                    {
                                        "start":"$start",
                                        "end":"$end"
                                    }],
                                    "host_routes": $host_routes,
                                    "dns_nameservers": ["$dns_nameservers"]
                                }
                            }"""

            
            t = Template(temp)
            body = t.substitute({"id":id,"name":form.get("name"),"cidr":form.get("cidr"),"gateway_ip":form.get("gateway_ip"),
                             "start":form.get("startip"),"end":form.get("endip"), "host_routes": form.get("host_routes"), "dns_nameservers":dns_nameservers})
        
        else:
            if dns_nameservers == "":
                temp = """{
                    "subnet": {
                        "name": "$name",
                        "network_id": "$id",
                        "enable_dhcp": "true",
                        "cidr": "$cidr",
                        "gateway_ip": "$gateway_ip",
                        "ip_version": 4,
                        "host_routes": $host_routes
                    }
                }"""
            else:
                temp = """{
                    "subnet": {
                        "name": "$name",
                        "network_id": "$id",
                        "enable_dhcp": "true",
                        "cidr": "$cidr",
                        "gateway_ip": "$gateway_ip",
                        "ip_version": 4,
                        "host_routes": $host_routes,
                        "dns_nameservers": ["$dns_nameservers"]
                        }
                    }"""

                
            t = Template(temp)
            body = t.substitute({"id":id,"name":form.get("name"),"cidr":form.get("cidr"),"gateway_ip":form.get("gateway_ip"), "host_routes": form.get("host_routes"), "dns_nameservers":dns_nameservers})

        
        dd = json.dumps(json.loads(body.replace("'", "\"")))
        r = requests.post(url, data=dd, headers=headers)
        print(r)
        
        if r.status_code == 201:
            return {"msg":"ok"}
        elif r.status_code == 409:
            return {"msg":"409"}
        elif r.status_code == 400:
            json_data = r.json()
            message = json_data.get('NeutronError', {}).get('message')
            return {"msg": "{}".format(message)}
        else:
            return {"msg":"error"}
    
    def openstack_create_subnet(self, id , form):
        method = "/v2.0/subnets"
        url = "%s%s" % (self.neutron_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        
        

        if form.get("dns_nameservers", ""):
            dns_nameservers = form.get("dns_nameservers", "").replace(',', '","')
        else:
            dns_nameservers = ""
        
        
        if   dns_nameservers == "":
            temp = """{
                    "subnet": {
                        "name": "$name",
                        "network_id": "$id",
                        "enable_dhcp": "false",
                        "cidr": "$cidr",
                        "gateway_ip": "$gateway_ip",
                        "ip_version": 4,
                        "host_routes": $host_routes
                    }
                }"""            
        else: 
            temp = """{
                    "subnet": {
                        "name": "$name",
                        "network_id": "$id",
                        "enable_dhcp": "false",
                        "cidr": "$cidr",
                        "gateway_ip": "$gateway_ip",
                        "ip_version": 4,
                        "host_routes": $host_routes,
                        "dns_nameservers": ["$dns_nameservers"]
                    }
                }"""
        
        t = Template(temp)
        body = t.substitute({"id":id,"name":form.get("name"),"cidr":form.get("cidr"),"gateway_ip":form.get("gateway_ip"), "host_routes": form.get("host_routes"), "dns_nameservers":dns_nameservers})
        
        dd = json.dumps(json.loads(body.replace("'", "\"")))
        r = requests.post(url, data=dd, headers=headers)
        
        if r.status_code == 201:
            return {"msg":"ok"}
        elif r.status_code == 409:
            return {"msg":"409"}
        else:
            return {"msg":"error"}

    def openstack_put_subnet(self, form):
        method = "/v2.0/subnets/"
        url = f"{self.neutron_uri}{method}{form.get('subnet_id')}"
        headers = {
            "X-Auth-Token": self.token,
            "Content-Type": "application/json"  # 设置请求头的Content-Type
        }

        # 从form字典中移除"subnet_id"字段
        form.pop("subnet_id", None)

        data = {
            "subnet": form  # 将你的数据放在一个"subnet"对象内
        }

        r = requests.put(url, data=json.dumps(data), headers=headers)

        if r.status_code == 200:
            return {"msg": "ok"}
        elif r.status_code == 409:
            return {"msg": "409"}
        else:
            return {"msg": "error"}



    def openstack_create_subnet_new(self, id , form):
        method = "/v2.0/subnets"
        url = "%s%s" % (self.neutron_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        
        temp = """{
                "subnet": {
                    "name": "$name",
                    "network_id": "$id",
                    "enable_dhcp": "false",
                    "cidr": "$cidr",
                    "gateway_ip": "$gateway_ip",
                    "ip_version": 4
                }
            }"""
        
        t = Template(temp)
        body = t.substitute({"id":id,"name":form.get("name"),"cidr":form.get("cidr"),"gateway_ip":form.get("gateway_ip")})
        
        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)
        
        if r.status_code == 201:
            return {"msg":"ok"}
        elif r.status_code == 409:
            return {"msg":"409"}
        else:
            return {"msg":"error"}

    def openstack_delete_subnetid(self, subnet_id):
        method = "/v2.0/subnets/%s" % subnet_id
        url = "%s%s" % (self.neutron_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        r = requests.delete(url, headers=headers)
        if r.status_code == 204:
            return {"msg": "ok"}
        elif r.status_code == 409:
            return {"msg": "此子网中的IP正在被使用，无法删除子网！"}
        else:
            return {"msg": "error"}
    
    def openstack_get_subnet_detail(self , id):
        method = "/v2.0/subnets/%s" %  id
        url = "%s%s" % (self.neutron_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        
        r = requests.get(url, headers=headers)
        
        d = json.loads(r.text)

        net = from_dict(data_class=SubnetDetail, data=d["subnet"])
        if d["subnet"]["gateway_ip"]:
            net.__dict__["gateway_ip"] = d["subnet"]["gateway_ip"]
        else:
            net.__dict__["gateway_ip"] = "-"
        net.__dict__["dns_nameservers"] = d["subnet"].get("dns_nameservers", "")
        net.__dict__["allocation_pools"] = d["subnet"].get("allocation_pools", [])
        net.__dict__["host_routes"] = d["subnet"].get("host_routes", "")
        net.__dict__["gateway_ip"] = d["subnet"].get("gateway_ip", "")
        return net.__dict__
    
    def openstack_create_flatnetwork(self, provider):
        method = "/v2.0/networks"
        url = "%s%s" % (self.neutron_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        
        temp = """{
                "network": {
                    "name": "$name",
                    "admin_state_up": "true",
                    "router:external": "true",
                    "provider:physical_network": "provider",
                    "provider:network_type": "$network_type"
                }
            }"""
        
        t = Template(temp)
        body = t.substitute({"name":provider.name,"network_type":provider.network_type})
        
        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)
        
        d = json.loads(r.text)
        
        ins = from_dict(data_class=NetworkDetail, data=d["network"])
        return ins.__dict__
    
    def openstack_create_vlannetwork(self, provider):
        method = "/v2.0/networks"
        url = "%s%s" % (self.neutron_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        
        temp = """{
                "network": {
                    "name": "$name",
                    "admin_state_up": "true",
                    "router:external": "true",
                    "provider:network_type": "$network_type",
                    "provider:physical_network": "provider",
                    "provider:segmentation_id": $segmentation_id
                }
            }"""
        
        t = Template(temp)
        body = t.substitute({"name":provider.name,"network_type":provider.network_type,"segmentation_id":provider.segmentation_id})
        
        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)
        
        d = json.loads(r.text)
        
        ins = from_dict(data_class=NetworkDetail, data=d["network"])
        return ins.__dict__

    def openstack_delete_network(self, network_id):
        method = "/v2.0/networks/%s" % network_id
        url = "%s%s" % (self.neutron_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        
        r = requests.delete(url, headers=headers)
        
        if r.status_code == 204:
            return {"msg":"ok"}
        elif r.status_code == 409:
            return {"msg":"此网络中的IP正在被使用，无法删除网络！"}
        else:
            return {"msg":"error"}
        
    def openstack_get_all_agent_list(self):
        method = "/v2.0/agents"
        url = "%s%s" % (self.neutron_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        
        r = requests.get(url, headers=headers)
        
        d = json.loads(r.text)
        res = []
        for ins_dict in d["agents"]:
            ins = from_dict(data_class=NetworkAgent, data=ins_dict)
            res.append(ins.__dict__)
        return res
    
    def openstack_get_service_providers(self):
        
        tz_sh = tz.gettz('Asia/Shanghai')
        

        time = datetime.now(tz=tz_sh).replace(microsecond=0)
        aaa = time.date()
        method = "/"
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        
        r = requests.get(url, headers=headers)
        
        d = json.loads(r.text)
        
        updatetime = d["version"]["updated"]
        uptime = datetime.strptime("2020-03-03" ,'%Y-%m-%d')
        
        #da = (datetime.sprptime(time ,format) -datetime.sprptime(d["version"]["updated"] ,format))
        
        res = []
        for ins_dict in d["networks"]:
            ins = from_dict(data_class=Network, data=ins_dict)
            ins.__dict__["network_type"] = ins_dict["provider:network_type"]
            ins.__dict__["vlanid"] = ins_dict.get("provider:segmentation_id","")
            res.append(ins.__dict__)
        return res
    
    def openstack_get_all_network_for_topology(self):
        method = "/v2.0/subnets"
        url = "%s%s" % (self.neutron_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        
        r   = requests.get(url, headers=headers)
        d   = json.loads(r.text)
        res = []

        for ins_dict in d["subnets"]:
            res.append({"subid": ins_dict["id"],
                        "subname": ins_dict["name"],
                        "sub_gateway_ip": ins_dict["gateway_ip"]})
        return res

    def openstack_get_vm_by_network_id_for_topology(self,id):
        method = "/v2.0/ports?network_id=%s" % id
        url = "%s%s" % (self.neutron_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        
        r = requests.get(url, headers=headers)
        
        d = json.loads(r.text)
        res = []
        for ins_dict in d["ports"]:
            ins = from_dict(data_class=Vms_for_topology, data=ins_dict)
            for ip in ins.__dict__["fixed_ips"]:
                ins.__dict__["ip"] = ip["ip_address"]
            if ins.__dict__["status"] == "ACTIVE":
                ins.__dict__["class"] = "c2up"
            elif ins.__dict__["status"] == "DOWN":
                ins.__dict__["class"] = "c2down"
            elif ins.__dict__["status"] == "ERROR":
                ins.__dict__["class"] = "c2down"
            
            res.append(ins.__dict__)
        return res
    
    def openstack_get_vm_for_topology(self, hostname):
        method  = "/servers/detail"
        url     = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "X-OpenStack-Nova-API-Version": "2.88"
        }
        r = requests.get(url, headers=headers)
        d = json.loads(r.text)
        power_state = {
            0: 'NOSTATE',
            1: 'RUNNING',
            3: 'PAUSED',
            4: 'SHUTDOWN',
            6: 'CRASHED',
            7: 'SUSPENDED'
        }

        res = {}

        for ins_dict in d["servers"]:
            hypervisor_hostname = ins_dict["OS-EXT-SRV-ATTR:hypervisor_hostname"]
            if hypervisor_hostname == hostname:
                vms = {
                    "id": ins_dict["id"],
                    "name": ins_dict["name"],
                    "power_state": power_state[ins_dict["OS-EXT-STS:power_state"]],
                    "vm_state": ins_dict["OS-EXT-STS:vm_state"],
                    "hostname": hypervisor_hostname,
                    "host_status": ins_dict["host_status"]
                }

                for subname, _ in ins_dict["addresses"].items():
                    if res.get(subname):
                        res[subname].append(vms)
                    else:
                        res[subname] = [vms]

        return res

    def openstack_get_all_ports(self):
        method = "/v2.0/ports"
        url = "%s%s" % (self.neutron_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        
        r = requests.get(url, headers=headers)
        
        d = json.loads(r.text)
        res = []
        for ins_dict in d["ports"]:
            ins = from_dict(data_class=Port, data=ins_dict)
            res.append(ins.__dict__)
        return res

    def openstack_get_all_filter_ports(self, filter):
        if filter:
            method = "/v2.0/ports?{}".format(filter)
        else:
            method = "/v2.0/ports"
        url = "%s%s" % (self.neutron_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        r = requests.get(url, headers=headers)

        d = json.loads(r.text)
        res = []
        for ins_dict in d["ports"]:
            ins = from_dict(data_class=Port, data=ins_dict)
            res.append(ins)
        return res
    
    def openstack_get_subnet_from_network(self,id):
        method = "/v2.0/networks/%s" % id
        url = "%s%s" % (self.neutron_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        
        r = requests.get(url, headers=headers)
        
        d = json.loads(r.text)
        if "network" in d:
            if "subnets" in d["network"] and len(d["network"]["subnets"]) > 0:
                return d["network"]["subnets"][0]

        return ""
    
    def openstack_create_port(self, networkid,ipv4,subnetid):
        method = "/v2.0/ports"
        url = "%s%s" % (self.neutron_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        
        temp = """{
                "port": {
                    "name": "$ipv4",
                    "network_id": "$networkid",
                    "fixed_ips": [{
                        "ip_address" : "$ipv4",
                        "subnet_id" : "$subnetid"
                    }]
                }
            }"""
        
        t = Template(temp)
        body = t.substitute({"ipv4":ipv4,"networkid":networkid,"subnetid":subnetid})
        
        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)
        
        d = json.loads(r.text)
        print("openstack_create_port返回结果:", d)
        if r.status_code == 201:
            return d["port"]["id"]
        else:
            return ""
        
    def openstack_create_port_withoutip(self, networkid,subnetid,vmid):
        method = "/v2.0/ports"
        url = "%s%s" % (self.neutron_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        
        temp = """{
                "port": {
                    "name": "$vmid",
                    "network_id": "$networkid",
                    "fixed_ips": [{
                        "subnet_id" : "$subnetid"
                    }]
                }
            }"""
        
        t = Template(temp)
        body = t.substitute({"networkid":networkid,"subnetid":subnetid,"vmid":vmid})
        
        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)
        
        d = json.loads(r.text)
        
        if r.status_code == 201:
            return d["port"]["id"]
        else:
            return ""

    def openstack_get_qos_policies(self ):
        method = "/v2.0/qos/policies" 
        url = "%s%s" % (self.neutron_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        
        r = requests.get(url, headers=headers)
        
        d = json.loads(r.text)

        #policy = from_dict(data_class=PolicyDetail, data=d["policies"])
        res = []
        for policy in d["policies"]:
            #ins = from_dict(data_class=Port, data=ins_dict)
            policy = from_dict(data_class=PolicyDetail, data=policy)
            res.append(policy.__dict__)
        return res
    
    def openstack_create_qos_policy(self, name):
        method = "/v2.0/qos/policies"
        url = "%s%s" % (self.neutron_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        
        temp = """{
                "policy": {
                    "name": "$name",
                    "description": "thxh policy",
                    "shared": "false"
                }
            }"""
        
        t = Template(temp)
        body = t.substitute({"name":name})
        
        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)
        
        d = json.loads(r.text)
        
        if r.status_code == 201:
        #ins = from_dict(data_class=NetworkDetail, data=d["network"])
            policy = from_dict(data_class=PolicyDetail, data=d["policy"])
            return policy.__dict__
        else:
            return {"msg":"error"}
        
    def openstack_create_bandwidth_limit_rule(self, policy_id,bandwidth,direction):
        method = "/v2.0/qos/policies/%s/bandwidth_limit_rules" % policy_id
        url = "%s%s" % (self.neutron_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        
        temp = """{
                "bandwidth_limit_rule": {
                    "max_kbps": "$bandwidth",
                    "max_burst_kbps": "$bandwidth",
                    "direction": "$direction"
                }
            }"""
        
        t = Template(temp)
        body = t.substitute({"bandwidth":bandwidth,"direction":direction})
        
        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)
        
        d = json.loads(r.text)
        
        if r.status_code == 201:
        #ins = from_dict(data_class=NetworkDetail, data=d["network"])
            #policy = from_dict(data_class=PolicyDetail, data=d["policy"])
            #return policy
            return {"msg":"ok"}
        else:
            return {"msg":"error"}
        
    def openstack_delete_qos_bandwidth_rule(self, policy_id, rule_id):
        method = "/v2.0/qos/policies/%s/bandwidth_limit_rules/%s" % (policy_id,rule_id)
        url = "%s%s" % (self.neutron_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        
        r = requests.delete(url, headers=headers)
        
        if r.status_code == 204:
            return {"msg":"ok"}
        else:
            return {"msg":"error"}
        
    def openstack_delete_qos_policy(self, policy_id):
        method = "/v2.0/qos/policies/%s" % policy_id
        url = "%s%s" % (self.neutron_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        
        r = requests.delete(url, headers=headers)
        
        if r.status_code == 204:
            return {"msg":"ok"}
        else:
            return {"msg":"error"}
        
    def openstack_update_port(self, portid):
        method = "/v2.0/ports/%s" % portid
        url = "%s%s" % (self.neutron_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        
        temp = """{
                "port": {
                    "qos_policy_id": null
                }
            }"""
        
        t = Template(temp)
        body = t.substitute()
        
        dd = json.dumps(json.loads(body))
        r = requests.put(url, data=dd, headers=headers)
        
        d = json.loads(r.text)
        
        if r.status_code == 201:
            return d["port"]["id"]
        else:
            return ""

    def openstack_network_interface_mappings(self):
        method  = "/v2.0/agents"
        url     = "%s%s" % (self.neutron_uri, method)
        headers = {"X-Auth-Token": self.token}

        r = requests.get(url, headers=headers)
        d = json.loads(r.text)

        res = []
        for ins_dict in d["agents"]:
            provider = ins_dict.get("configurations").get("interface_mappings")
            if provider:
                device    = provider["provider"]
                hostname  = ins_dict["host"]
                ipaddress = ins_dict["configurations"]["tunneling_ip"]

                res.append({"device": device,
                            "hostname": hostname,
                            "ipaddress": ipaddress})
        return res

    def openstack_get_all_filter_ports_v2(self, filter):
        res = []

        @dataclass
        class Port:
            device_id: str
            device_owner: str
            fixed_ips: list
            id: str
            mac_address: str
            name: str
            port_security_enabled: bool
            status: str

        if filter:
            method = "/v2.0/ports?{}".format(filter)
        else:
            return res

        url     = "%s%s" % (self.neutron_uri, method)
        headers = {"X-Auth-Token": self.token}
        r       = requests.get(url, headers=headers)
        d       = json.loads(r.text)

        for ins_dict in d["ports"]:
            ins = from_dict(data_class=Port, data=ins_dict)
            res.append(ins)
        return res

    def openstack_add_port_address_pairs(self, port, mac_address):
        method  = f"/v2.0/ports/{port.id}/add_allowed_address_pairs"
        url     = "%s%s" % (self.neutron_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        temp = """{
            "port": {
                "allowed_address_pairs": [
                    {
                        "ip_address": "$ip_address",
                        "mac_address": "$mac_address"
                    }
                ]
            }
        }"""

        t    = Template(temp)
        body = t.substitute({"ip_address": port.fixed_ips[0],
                             "mac_address": mac_address.strip()})
        dd   = json.dumps(json.loads(body))
        r    = requests.put(url, data=dd, headers=headers)
        d    = json.loads(r.text)

        if r.status_code == 200:
            return "ok"
        else:
            return d

    def openstack_remove_port_address_pairs(self, port):
        method  = f"/v2.0/ports/{port.id}/remove_allowed_address_pairs"
        url     = "%s%s" % (self.neutron_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        temp = """{
            "port": {
                "allowed_address_pairs": [
                    {
                        "ip_address": "$ip_address",
                        "mac_address": "$mac_address"
                    }
                ]
            }
        }"""

        t    = Template(temp)
        body = t.substitute({"ip_address": port.fixed_ips[0]["ip_address"],
                             "mac_address": port.mac_address.strip()})
        dd   = json.dumps(json.loads(body))
        r    = requests.put(url, data=dd, headers=headers)
        d    = json.loads(r.text)

        if r.status_code == 200:
            return "ok"
        else:
            return d

    def openstack_edit_port(self, port, mac_address):
        method  = f"/v2.0/ports/{port.id}"
        url     = "%s%s" % (self.neutron_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        temp = """{
            "port": {
                "name": "$name",
                "mac_address": "$mac_address"
            }
        }"""

        t    = Template(temp)
        body = t.substitute({"name": port.name, "mac_address": mac_address})
        dd   = json.dumps(json.loads(body))
        r    = requests.put(url, data=dd, headers=headers)
        d    = json.loads(r.text)

        if r.status_code == 200:
            return "ok"
        else:
            return d
