import requests
import json
import settings
from api.model.securitygroup import SecurityGroup , SecurityGroupDetail
from dataclasses import dataclass
from dacite import from_dict
from string import Template
class SecuritygroupClient:
    def openstack_test(self, id):
        print(id)

    def openstack_get_all_securitygroup_list(self):
        method = "/v2.0/security-groups?project_id=%s" % self.tenant_id
        url = "%s%s" % (self.neutron_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        
        r = requests.get(url, headers=headers)
        
        d = json.loads(r.text)
        res = []
        for ins_dict in d["security_groups"]:
            ins = from_dict(data_class=SecurityGroup, data=ins_dict)
            res.append(ins.__dict__)
        return res
    
    def openstack_get_securitygroup_detail(self, id):
        method = "/v2.0/security-groups/%s" % id
        url = "%s%s" % (self.neutron_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        
        r = requests.get(url, headers=headers)
        
        d = json.loads(r.text)
          
        if d.get("security_group", None):
            #detail = from_dict(data_class=SecurityGroupDetail, data=d.get("security_group", None))
            return d
        
    def openstack_create_securitygroup(self,name,desc):
        method = "/v2.0/security-groups"
        url = "%s%s" % (self.neutron_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Content-Type":"application/json"
        }
    
        temp = """{
                "security_group": {
                    "name": "$name",
                    "description": "$desc",
                    "stateful": true
                }
            }"""
        
        t = Template(temp)
        body = t.substitute({"name":name,"desc":desc})
        
        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)
        d = json.loads(r.text)
        
        if r.status_code == 201:
            sec = from_dict(data_class=SecurityGroup, data=d["security_group"])
            return  sec.__dict__
        else:
            return  {"msg":"error"}
    
    def openstack_delete_securitygroup(self, id):
        method = "/v2.0/security-groups/%s" % id
        url = "%s%s" % (self.neutron_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        
        r = requests.delete(url, headers=headers)
        
        if r.status_code == 204:
            return {"msg":"ok"}
        elif r.status_code == 409:
            return {"msg":"安全组正在使用中，无法删除！"}
        else:
            return {"msg":"error"}
    
    def openstack_edit_securitygroup(self, id,name,desc):
        method = "/v2.0/security-groups/%s" % id
        url = "%s%s" % (self.neutron_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        
        temp = """{
                "security_group": {
                    "name": "$name",
                    "description": "$desc",
                    "stateful": true
                }
            }"""
        
        t = Template(temp)
        body = t.substitute({"name":name,"desc":desc})
        
        dd = json.dumps(json.loads(body))
        r = requests.put(url, data=dd, headers=headers)
        
        d = json.loads(r.text)
        
        ins = from_dict(data_class=SecurityGroup, data=d["security_group"])
        return ins.__dict__
    
    def openstack_create_securitygroup_rule_withip(self,form):
        method = "/v2.0/security-group-rules"
        url = "%s%s" % (self.neutron_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        temp = """{
                "security_group_rule": {
                    "security_group_id": "$security_group_id",
                    "direction": "$direction",
                    "protocol": "$protocol",
                    "ethertype": "IPv4",
                    "port_range_min": $port_range_min,
                    "port_range_max": $port_range_max,
                    "description": "$description",
                    "remote_ip_prefix": "$remote_ip_prefix"
                }
            }"""
        
        t = Template(temp)
        body = t.safe_substitute(form)
        body = body.replace("None","null")
        
        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)
        d = json.loads(r.text)
        
        
        if r.status_code == 201:
            return  {"msg":"ok"}
        elif r.status_code == 409:
            return  {"msg":"规则已存在，请勿重复提交！"}
        else:
            return  {"msg":"规则创建失败！"}
        
    def openstack_create_securitygroup_rule_withsg(self,form):
        method = "/v2.0/security-group-rules"
        url = "%s%s" % (self.neutron_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        #
        temp = """{
                "security_group_rule": {
                    "security_group_id": "$security_group_id",
                    "direction": "$direction",
                    "protocol": "$protocol",
                    "ethertype": "IPv4",
                    "port_range_min": $port_range_min,
                    "port_range_max": $port_range_max,
                    "description": "$description",
                    "remote_group_id": "$remote_group_id"
                }
            }"""
        
        t = Template(temp)
        body = t.safe_substitute(form)
        body = body.replace("None","null")
        
        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)
        d = json.loads(r.text)
        
        
        if r.status_code == 201:
            return  {"msg":"ok"}
        elif r.status_code == 409:
            return  {"msg":"规则已存在，请勿重复提交！"}
        else:
            return  {"msg":"规则创建失败！"}
        
    def openstack_delete_securitygroup_rule(self, id):
        method = "/v2.0/security-group-rules/%s" % id
        url = "%s%s" % (self.neutron_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        
        r = requests.delete(url, headers=headers)
        
        if r.status_code == 204:
            return {"msg":"ok"}
        else:
            return {"msg":"error"}
        
    def openstack_get_securitygroup_byserver(self, id):
        method = "/servers/%s/os-security-groups" % id
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        
        r = requests.get(url, headers=headers)
        
        d = json.loads(r.text)
        
        res = []
        
        
        if r.status_code == 200:
            for sec in d["security_groups"]:
                ins = from_dict(data_class=SecurityGroup, data=sec)
                res.append(ins.__dict__)
            return res
        else:
            return {"msg":"error"}
        
    
    def openstack_add_securitygroup_to_server(self, id,name):
        method = "/servers/%s/action" % id
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        
        temp = """{
                "addSecurityGroup": {
                    "name": "$name"
                }
            }"""
        
        t = Template(temp)
        body = t.substitute({"name":name})
        
        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)
        
        if r.status_code == 202:
            return {"msg":"ok"}
        else:
            return {"msg":"error"}
        
    def openstack_remove_securitygroup_from_server(self, id,name):
        method = "/servers/%s/action" % id
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        
        temp = """{
                "removeSecurityGroup": {
                    "name": "$name"
                }
            }"""
        
        t = Template(temp)
        body = t.substitute({"name":name})
        
        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)
        
        
        if r.status_code == 202:
            return {"msg":"ok"}
        else:
            return {"msg":"error"}
        
        