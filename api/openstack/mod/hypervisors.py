import requests
import json
import settings
from api.model.hypervisors import Hypervisor_id, Hypervisor, CreateAggregates,HypervisorDetail, HypervisorStatistics, HypervisorDetail_version, Aggregate, Aggregates
from dataclasses import dataclass
from dacite import from_dict
from string import Template


class HypervisorsClient:
    def openstack_test(self, id):
        print(id)


    def openstack_get_statistics(self):
        method = "/os-hypervisors/statistics"
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        r = requests.get(url, headers=headers)

        d = json.loads(r.text)
        ins = from_dict(data_class=HypervisorStatistics, data=d["hypervisor_statistics"])
        return ins.__dict__

    def openstack_get_all_list(self):
        method = "/os-hypervisors"
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "X-OpenStack-Nova-API-Version": "2.53"
        }

        r = requests.get(url, headers=headers)

        d = json.loads(r.text)
        res = []
        for ins_dict in d["hypervisors"]:
            ins = from_dict(data_class=Hypervisor, data=ins_dict)
            res.append(ins.__dict__)
        return res

    def openstack_get_hosts_all_vms(self, host):
        method = "/os-hypervisors/%s/servers" % host
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token,
        }

        r = requests.get(url, headers=headers)

        d = json.loads(r.text)
        res = []
        for ins_dict in d["hypervisors"]:
            ins = from_dict(data_class=Hypervisor_id, data=ins_dict)
            res.append(ins.__dict__)
        return res


    def openstack_get_licence(self):
        method = "/license"
        url = "%s%s" % ("http://**************:8088", method)
        headers = {
            "X-Auth-Token": self.token
        }

        r = requests.get(url, headers=headers)

        d = json.loads(r.text)

        return d

    def openstack_get_all_cluster_detail(self):
        method = "/os-hypervisors/detail"
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "X-OpenStack-Nova-API-Version": "2.87"
        }

        r = requests.get(url, headers=headers)
        print("---------------------")
        print(r.text)
        d = json.loads(r.text)
        print("----")
        print(d)
        print("--------")
        res = []
        for ins_dict in d["hypervisors"]:
            ins = from_dict(data_class=HypervisorDetail, data=ins_dict)

            res.append(ins.__dict__)
        return res

    def openstack_get_hy_87detail(self, id):
        method = "/os-hypervisors/%s" % id
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "X-OpenStack-Nova-API-Version": "2.87"
        }

        r = requests.get(url, headers=headers)

        d = json.loads(r.text)
        res = from_dict(data_class=HypervisorDetail, data=d["hypervisor"])
        return res.__dict__

    def openstack_get_hy_detail(self, id):
        method = "/os-hypervisors/%s" % id
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "X-OpenStack-Nova-API-Version": "2.88"
        }

        r = requests.get(url, headers=headers)

        d = json.loads(r.text)
        res = from_dict(data_class=HypervisorDetail_version, data=d["hypervisor"])
        return res.__dict__

    def openstack_get_all_aggregates_list(self):
        method = "/os-aggregates"
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        r = requests.get(url, headers=headers)

        d = json.loads(r.text)
        res = []
        for ins_dict in d["aggregates"]:
            ins = from_dict(data_class=Aggregates, data=ins_dict)
            res.append(ins.__dict__)
        return res

    def openstack_get_all_availabilityzone_list(self):
        method = "/os-availability-zone/detail"
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        r = requests.get(url, headers=headers)

        d = json.loads(r.text)
        res = []
        for ava_zone in d["availabilityZoneInfo"]:
            #ins = from_dict(data_class=Aggregates, data=ins_dict)
            #res.append(ins.__dict__)
            if ava_zone["zoneName"] != "internal" and ava_zone["zoneName"] != "nova" :
                for key in ava_zone["hosts"]:
                    print(key)
                    res.append(key)


        return res

    def openstack_get_name_availabilityzone_by_zonename(self):
        method = "/os-aggregates"
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        r = requests.get(url, headers=headers)

        d = json.loads(r.text)
        res = {}
        for ava_zone in d["aggregates"]:

            res[ava_zone["availability_zone"]] = ava_zone["name"]

        return res


    def openstack_get_aggregates_list(self, id):
        method = "/os-aggregates/%s" % id
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        r = requests.get(url, headers=headers)

        d = json.loads(r.text)
        res = from_dict(data_class=Aggregate, data=d["aggregate"])
        return res

    def openstack_add_aggregate_host(self, id, host):
        method = "/os-aggregates/%s/action" % id
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        temp = """{"add_host": {"host": "$host"}}"""

        t = Template(temp)
        body = t.substitute({"host":host})

        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)

        d = json.loads(r.text)

        ins = from_dict(data_class=Aggregate, data=d["aggregate"])
        return ins.__dict__

    def openstack_delete_aggregate_host(self, id, host):
        method = "/os-aggregates/%s/action" % id
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        temp = """{"remove_host": {"host": "$host"}}"""

        t = Template(temp)
        body = t.substitute({"host":host})

        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)

        d = json.loads(r.text)

        ins = from_dict(data_class=Aggregate, data=d["aggregate"])
        return ins.__dict__

    def openstack_update_aggregate_name(self, id, name):
        method = "/os-aggregates/%s" % id
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        temp = """{
                "aggregate":
                {
                    "name": "$name"
                }
            }"""

        t = Template(temp)
        body = t.substitute({"name":name})

        dd = json.dumps(json.loads(body))
        r = requests.put(url, data=dd, headers=headers)

        d = json.loads(r.text)

        ins = from_dict(data_class=Aggregate, data=d["aggregate"])
        return ins.__dict__

    def openstack_update_aggregate_full(self, id, name, availability_zone):
        """
        更新主机聚合的名称和可用区
        """
        method = "/os-aggregates/%s" % id
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        # 构建请求体，只包含需要更新的字段
        aggregate_data = {}
        if name is not None:
            aggregate_data["name"] = name
        if availability_zone is not None:
            aggregate_data["availability_zone"] = availability_zone

        body_data = {"aggregate": aggregate_data}

        dd = json.dumps(body_data)
        r = requests.put(url, data=dd, headers=headers)

        # 检查响应状态
        if r.status_code == 400:
            error_data = json.loads(r.text)
            if "One or more hosts contain instances in this zone" in error_data.get("badRequest", {}).get("message", ""):
                # 如果是因为有实例运行而无法更新可用区，尝试只更新名称
                if name is not None and availability_zone is not None:
                    # 先只更新名称
                    name_only_data = {"aggregate": {"name": name}}
                    name_dd = json.dumps(name_only_data)
                    name_r = requests.put(url, data=name_dd, headers=headers)

                    if name_r.status_code == 200:
                        name_result = json.loads(name_r.text)
                        # 返回结果，但标记可用区更新失败
                        result = from_dict(data_class=Aggregate, data=name_result["aggregate"])
                        result_dict = result.__dict__
                        result_dict["availability_zone_update_failed"] = True
                        result_dict["error_message"] = "无法更新可用区：主机聚合中有正在运行的实例"
                        return result_dict
                    else:
                        raise Exception(f"更新名称也失败: {name_r.text}")
                else:
                    # 如果只是更新可用区失败，抛出详细错误
                    raise Exception("无法更新可用区：主机聚合中有正在运行的实例。请先迁移或停止所有实例后再试。")
            else:
                # 其他400错误
                raise Exception(f"请求错误: {error_data}")
        elif r.status_code != 200:
            # 其他HTTP错误
            raise Exception(f"HTTP错误 {r.status_code}: {r.text}")

        # 成功情况
        d = json.loads(r.text)
        ins = from_dict(data_class=Aggregate, data=d["aggregate"])
        return ins.__dict__

    def openstack_update_aggregate_metadata(self, id, metadata):
        """
        更新主机聚合的元数据
        """
        method = "/os-aggregates/%s/action" % id
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        body_data = {
            "set_metadata": {
                "metadata": metadata
            }
        }

        dd = json.dumps(body_data)
        r = requests.post(url, data=dd, headers=headers)

        if r.status_code != 200:
            raise Exception(f"更新元数据失败: HTTP {r.status_code} - {r.text}")

        d = json.loads(r.text)

        ins = from_dict(data_class=Aggregate, data=d["aggregate"])
        return ins.__dict__

    def openstack_check_aggregate_instances(self, id):
        """
        检查主机聚合中是否有运行的实例
        返回: {
            "has_instances": bool,
            "instance_count": int,
            "hosts_with_instances": [host_list],
            "instances": [instance_list]
        }
        """
        # 首先获取聚合信息
        aggregate = self.openstack_get_aggregates_list(id)
        hosts = aggregate.hosts if hasattr(aggregate, 'hosts') else []

        total_instances = 0
        hosts_with_instances = []
        all_instances = []

        for host in hosts:
            try:
                # 获取每个主机上的实例
                host_instances = self.openstack_get_hosts_all_vms(host)
                if host_instances:
                    for hypervisor in host_instances:
                        if hasattr(hypervisor, 'servers') and hypervisor.servers:
                            instances_count = len(hypervisor.servers)
                            if instances_count > 0:
                                total_instances += instances_count
                                hosts_with_instances.append(host)
                                all_instances.extend(hypervisor.servers)
            except Exception as e:
                # 如果获取某个主机的实例失败，记录但继续
                print(f"获取主机 {host} 的实例信息失败: {e}")
                continue

        return {
            "has_instances": total_instances > 0,
            "instance_count": total_instances,
            "hosts_with_instances": hosts_with_instances,
            "instances": all_instances,
            "total_hosts": len(hosts)
        }

    def openstack_create_aggregate(self, cluster):
        name = cluster.name
        method = "/os-aggregates"
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        temp = """{"aggregate": {"name": "$name", "availability_zone": "$name"}}"""

        t = Template(temp)
        body = t.substitute({"name":name})

        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)
        if r.status_code == 409:
            return {"msg":"409"}
        d = json.loads(r.text)

        ins = from_dict(data_class=CreateAggregates, data=d["aggregate"])
        return ins.__dict__

    def openstack_create_aggregate_v2(self, cluster):
        name = cluster.get("name", "")
        method = "/os-aggregates"
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        temp = """{"aggregate": {"name": "$name", "availability_zone": "$name"}}"""

        t = Template(temp)
        body = t.substitute({"name": name})

        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)
        if r.status_code == 409:
            return {"msg": "409"}
        d = json.loads(r.text)

        ins = from_dict(data_class=CreateAggregates, data=d["aggregate"])
        return ins.__dict__

    def openstack_delete_aggregate(self, id):
        method = "/os-aggregates/%s" % id
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        r = requests.delete(url, headers=headers)

        '''if r.status_code == 204:
            return {"msg": "ok"}'''
        return {"msg": "ok"}

