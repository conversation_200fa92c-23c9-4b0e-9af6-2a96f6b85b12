import requests
import json
import settings
from api.model.hypervisors import Hypervisor_id, Hypervisor, CreateAggregates,HypervisorDetail, HypervisorStatistics, HypervisorDetail_version, Aggregate, Aggregates
from dataclasses import dataclass
from dacite import from_dict
from string import Template


class HypervisorsClient:
    def openstack_test(self, id):
        print(id)


    def openstack_get_statistics(self):
        method = "/os-hypervisors/statistics"
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        
        r = requests.get(url, headers=headers)
        
        d = json.loads(r.text)
        ins = from_dict(data_class=HypervisorStatistics, data=d["hypervisor_statistics"])
        return ins.__dict__ 

    def openstack_get_all_list(self):
        method = "/os-hypervisors"
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "X-OpenStack-Nova-API-Version": "2.53"
        }
        
        r = requests.get(url, headers=headers)
        
        d = json.loads(r.text)
        res = []
        for ins_dict in d["hypervisors"]:
            ins = from_dict(data_class=Hypervisor, data=ins_dict)
            res.append(ins.__dict__)
        return res

    def openstack_get_hosts_all_vms(self, host):
        method = "/os-hypervisors/%s/servers" % host
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token,
        }
        
        r = requests.get(url, headers=headers)
        
        d = json.loads(r.text)
        res = []
        for ins_dict in d["hypervisors"]:
            ins = from_dict(data_class=Hypervisor_id, data=ins_dict)
            res.append(ins.__dict__)
        return res

    
    def openstack_get_licence(self):
        method = "/license"
        url = "%s%s" % ("http://**************:8088", method)
        headers = {
            "X-Auth-Token": self.token
        }
        
        r = requests.get(url, headers=headers)
        
        d = json.loads(r.text)
        
        return d
    
    def openstack_get_all_cluster_detail(self):
        method = "/os-hypervisors/detail"
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "X-OpenStack-Nova-API-Version": "2.87"
        }
        
        r = requests.get(url, headers=headers)
        print("---------------------")
        print(r.text)
        d = json.loads(r.text)
        print("----")
        print(d)
        print("--------")
        res = []
        for ins_dict in d["hypervisors"]:
            ins = from_dict(data_class=HypervisorDetail, data=ins_dict)
            
            res.append(ins.__dict__)
        return res
    
    def openstack_get_hy_87detail(self, id):
        method = "/os-hypervisors/%s" % id
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "X-OpenStack-Nova-API-Version": "2.87"
        }
        
        r = requests.get(url, headers=headers)
        
        d = json.loads(r.text)
        res = from_dict(data_class=HypervisorDetail, data=d["hypervisor"])
        return res.__dict__

    def openstack_get_hy_detail(self, id):
        method = "/os-hypervisors/%s" % id
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "X-OpenStack-Nova-API-Version": "2.88"
        }
        
        r = requests.get(url, headers=headers)
        
        d = json.loads(r.text)
        res = from_dict(data_class=HypervisorDetail_version, data=d["hypervisor"])
        return res.__dict__

    def openstack_get_all_aggregates_list(self):
        method = "/os-aggregates"
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        
        r = requests.get(url, headers=headers)
        
        d = json.loads(r.text)
        res = []
        for ins_dict in d["aggregates"]:
            ins = from_dict(data_class=Aggregates, data=ins_dict)
            res.append(ins.__dict__)
        return res
    
    def openstack_get_all_availabilityzone_list(self):
        method = "/os-availability-zone/detail"
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        
        r = requests.get(url, headers=headers)
        
        d = json.loads(r.text)
        res = []
        for ava_zone in d["availabilityZoneInfo"]:
            #ins = from_dict(data_class=Aggregates, data=ins_dict)
            #res.append(ins.__dict__)
            if ava_zone["zoneName"] != "internal" and ava_zone["zoneName"] != "nova" :
                for key in ava_zone["hosts"]:
                    print(key)
                    res.append(key)


        return res

    def openstack_get_name_availabilityzone_by_zonename(self):
        method = "/os-aggregates"
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        
        r = requests.get(url, headers=headers)
        
        d = json.loads(r.text)
        res = {}
        for ava_zone in d["aggregates"]:

            res[ava_zone["availability_zone"]] = ava_zone["name"]

        return res


    def openstack_get_aggregates_list(self, id):
        method = "/os-aggregates/%s" % id
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        
        r = requests.get(url, headers=headers)
        
        d = json.loads(r.text)
        res = from_dict(data_class=Aggregate, data=d["aggregate"])
        return res
    
    def openstack_add_aggregate_host(self, id, host):
        method = "/os-aggregates/%s/action" % id
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        temp = """{"add_host": {"host": "$host"}}"""
                    
        t = Template(temp)
        body = t.substitute({"host":host})
        
        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)
        
        d = json.loads(r.text)
        
        ins = from_dict(data_class=Aggregate, data=d["aggregate"])
        return ins.__dict__

    def openstack_delete_aggregate_host(self, id, host):
        method = "/os-aggregates/%s/action" % id
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        temp = """{"remove_host": {"host": "$host"}}"""
                    
        t = Template(temp)
        body = t.substitute({"host":host})
        
        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)
        
        d = json.loads(r.text)
        
        ins = from_dict(data_class=Aggregate, data=d["aggregate"])
        return ins.__dict__

    def openstack_update_aggregate_name(self, id, name):
        method = "/os-aggregates/%s" % id
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        temp = """{
                "aggregate":
                {
                    "name": "$name"
                }
            }"""
                    
        t = Template(temp)
        body = t.substitute({"name":name})
        
        dd = json.dumps(json.loads(body))
        r = requests.put(url, data=dd, headers=headers)
        
        d = json.loads(r.text)
        
        ins = from_dict(data_class=Aggregate, data=d["aggregate"])
        return ins.__dict__
        
    def openstack_create_aggregate(self, cluster):
        name = cluster.name
        method = "/os-aggregates"
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        temp = """{"aggregate": {"name": "$name", "availability_zone": "$name"}}"""
                    
        t = Template(temp)
        body = t.substitute({"name":name})
        
        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)
        if r.status_code == 409:
            return {"msg":"409"}
        d = json.loads(r.text)
        
        ins = from_dict(data_class=CreateAggregates, data=d["aggregate"])
        return ins.__dict__

    def openstack_create_aggregate_v2(self, cluster):
        name = cluster.get("name", "")
        method = "/os-aggregates"
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        temp = """{"aggregate": {"name": "$name", "availability_zone": "$name"}}"""

        t = Template(temp)
        body = t.substitute({"name": name})

        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)
        if r.status_code == 409:
            return {"msg": "409"}
        d = json.loads(r.text)

        ins = from_dict(data_class=CreateAggregates, data=d["aggregate"])
        return ins.__dict__

    def openstack_delete_aggregate(self, id):
        method = "/os-aggregates/%s" % id
        url = "%s%s" % (self.nova_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        r = requests.delete(url, headers=headers)
        
        '''if r.status_code == 204:
            return {"msg": "ok"}'''
        return {"msg": "ok"}
        
    