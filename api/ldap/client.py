#!/usr/bin/python 
# -*- coding: utf-8 -*-
'''
Created on Apr 13, 2022

@author: maojj
'''
from datetime import datetime

import ldap
import settings
import uuid
import math
from ldap import modlist
from enum import Enum
import traceback

from ldap3 import Server, Connection, ALL, NTLM, MODIFY_REPLACE

from pyrestful.types import unicode

# {'objectClass': [b'top', b'group'], 'cn': [b'\xe6\x82\xa8\xe5\xa5\xbd\xe7\xbb\x84'], 'distinguishedName': [b'CN=\xe6\x82\xa8\xe5\xa5\xbd\xe7\xbb\x84,DC=thxh,DC=com'], 'instanceType': [b'4'], 'whenCreated': [b'**************.0Z'], 'whenChanged': [b'**************.0Z'], 'uSNCreated': [b'57855'], 'uSNChanged': [b'57855'], 'name': [b'\xe6\x82\xa8\xe5\xa5\xbd\xe7\xbb\x84'], 
#'objectGUID': [b'\xfd\x00\xaa\x02\xbe\x1b\x83C\x96\xf2\xb2\x1e\x9bv\x9fs'], 'objectSid': [b'\x01\x05\x00\x00\x00\x00\x00\x05\x15\x00\x00\x00\xfc\x87q\x94U/\x16\xed=\x1f\x96\x87]\x04\x00\x00'], 'sAMAccountName': [b'\xe6\x82\xa8\xe5\xa5\xbd\xe7\xbb\x84'], 'sAMAccountType': [b'*********'], 'groupType': [b'-**********'], 'objectCategory': [b'CN=Group,CN=Schema,CN=Configuration,DC=thxh,DC=com'], 'dSCorePropagationData': [b'**************.0Z']}

class OCType(Enum):
    EVERYONE = "(objectClass=*)"
    USER = "(objectClass=user)"
    GROUP = "(objectClass=group)"
    ORGUNIT = "(objectClass=organizationalUnit)"

#组织单位 organizationalUnit
#组 group
#用户 person

class Objectclass(Enum):
    GROUP = "group"
    USER = "preson"
    ORGUNIT = "organizationalUnit"

LDAP_FILTER = 'objectclass=user'

  
class LdapClient(object):
    

    def __init__(self, domain, server, user , password):
        self.ldap_url = "ldap://%s" % server
        self.username = user
        self.domain = domain
        self.password = password
        self.filter = LDAP_FILTER
        self.binduser = "%s@%s" % (user,domain)
        
        try:
            self.conn = ldap.initialize(self.ldap_url, bytes_mode=False)
            self.conn.simple_bind_s(self.binduser, self.password)
            self.conn.protocol_version = ldap.VERSION3
            self.conn.set_option(ldap.OPT_REFERRALS, 0)
            #self.conn.set_option(ldap.OPT_X_TLS_REQUIRE_CERT, 0)
        
        except Exception as e:
            print(e)
            raise Exception(e)
        
    """
    def testconn(self, domain):
        try:
            BASE_DN = self.domain2dn(domain)
            #BASE_DN = "OU=%s,%s" % ("研发中心", BASE_DN)
            result = self.conn.search_s(BASE_DN, ldap.SCOPE_SUBTREE, "(objectClass=*)")
            print(result)
        except Exception as e:
            print(e)
            raise Exception(e)        
    """     
        
    def bashdn2path(self, basedn):
        dnpath = ""
        back_basedn_list = basedn.split(",")
        
        for k in back_basedn_list:
            if k.split("=")[0] == "DC":
                dnpath += "%s." % k.split("=")[1]
            else:
                dnpath += "%s/" % k.split("=")[1]
        
        return dnpath
    
    def domain2dn(self, domain):
        base_dn_list = []
        for k in domain.split("."):
            base_dn_list.append("DC=%s" % k)
        
        BASE_DN = ",".join(base_dn_list)
        return BASE_DN

    def create_users(self, domain, username, password, name, phone, email):
        #BASE_DN = "OU=yanfa,DC=test,DC=com"
        BASE_DN = self.domain2dn(domain)
        BASE_DN = "OU=%s,%s" % ("云用户", BASE_DN)
        
        dn = "CN=%s,%s" % (username, BASE_DN)
        login_name = "%s@%s" % (username, domain)
        print(dn)
        """
        'userAccountControl' (***************)    <class 'list'>: [b'66048']    
        'accountExpires' (***************)    <class 'list'>: [b'9223372036854775807']    

        """
        
        unicode_pass = unicode('\"' + password + '\"', 'iso-8859-1')
        password_value = unicode_pass.encode('utf-16-le')
        attrs = {
            'objectClass': [b'top', b'person', b'organizationalPerson', b'user'],
            #'userAccountControl': [b'512'],
            #'accountExpires': [b'9223372036854775807'],
            #'pwdPolicySubentry': dn.encode("utf-8"),
            'cn': username.encode("utf-8"),
            'sn': username.encode("utf-8"),
            'mail': email.encode("utf-8"),
            'telephoneNumber': phone.encode("utf-8"),
            'userPrincipalName': login_name.encode("utf-8"),
            'name': name.encode("utf-8"),
            'userPassword': password_value,
            #'userAccountControl': [b'66048'],
            #'accountExpires': [b'9223372036854775807']      
        }
        mod_attrs = modlist.addModlist(attrs)
        self.conn.add_s(dn, mod_attrs)

        return "ok"

    def delete_users(self, domain, object_uid):
        BASE_DN = self.domain2dn(domain)
        BASE_DN = "OU=%s,%s" % ("云用户", BASE_DN)
        dn = "cn=%s,%s" % (object_uid, BASE_DN)
        
        self.conn.delete_s(dn)

        return "ok"
    
    def query_users(self, domain, search_str, pagecount, page):
        #BASE_DN = "OU=yanfa,DC=test,DC=com"
        BASE_DN = self.domain2dn(domain)
        BASE_DN = "OU=%s,%s" % ("云用户", BASE_DN)
        result = self.conn.search_s(BASE_DN, ldap.SCOPE_SUBTREE, "(objectClass=*)")
        
        res = []
        for basedn, objectclass in result:
            if basedn:
                #print(objectclass.get("objectClass"))
                if "user".encode() in objectclass.get("objectClass"):
                    dn_path = self.bashdn2path(basedn)
                    osid = objectclass.get("objectSid")
                    cn = objectclass.get("cn")[0].decode()
                    #username = objectclass.get("userPrincipalName")[0].decode()
                    #name = objectclass.get("name")[0].decode()
                    
                    name = cn
                    if objectclass.get("displayName", ""):
                        name = objectclass.get("displayName")[0].decode()
                    
                    email = ""
                    if objectclass.get("mail", ""):
                        email = objectclass.get("mail")[0].decode()
                    phone = ""
                    if objectclass.get("telephoneNumber", ""):
                        phone = objectclass.get("telephoneNumber")[0].decode()
                    created_at = ""
                    if objectclass.get("whenCreated", ""):
                        when_created = objectclass.get("whenCreated")[0].decode()
                        created_at = datetime.strptime(when_created, "%Y%m%d%H%M%S.%fZ")
                    
                    #'telephoneNumber' (140718905884784)    <class 'list'>: [b'111111']    

                    if search_str in cn:
                        objectguid = str(uuid.UUID(bytes= objectclass.get("objectGUID")[0]))
                        #username = objectclass.get("userPrincipalName").decode()
                        tmp = {}
                        tmp["cn"] = cn
                        tmp["id"] = cn
                        tmp["name"] = name
                        tmp["email"] = email
                        tmp["phone"] = phone
                        tmp["username"] = cn
                        tmp["dnpath"] = dn_path
                        tmp["objectguid"] = objectguid
                        tmp["objecttype"] = "user"
                        tmp["created_at"] = created_at
                        res.append(tmp)
                        res.reverse()
                        
                        #print(osid[0].decode(encoding="utf-8", errors="strict"))
        
        count = len(res)
        if page > count / pagecount + 1:
            return {"msg": "page error"}
        
        start = (page - 1) * pagecount
        #end =  (count - 1) if  (start + pagecount >= count) else (start + pagecount)
        end =  start + pagecount
        
        pages = math.floor(count / pagecount + 1)

        # Define the function to format time back to string
        def format_time(dt):
            return dt.strftime("%Y-%m-%d %H:%M:%S.%fZ")[:-3] + "Z"

        # Sort the data by created_at in descending order
        sorted_data = sorted(res, key=lambda x: x['created_at'], reverse=True)

        # Calculate the start and end index for pagination
        start = (page - 1) * pagecount
        end = min(start + pagecount, count)

        # Apply pagination
        paginated_data = sorted_data[start:end]

        # Format the datetime objects back to strings
        formatted_data = [{**entry, 'created_at': format_time(entry['created_at'])} for entry in paginated_data]

        result = {
            "total": count,
            "pages": pages,
            "pagesize": pagecount,
            "data": formatted_data
        }

        return result

    def query_groups(self, domain, search_str, pagecount, page):
        #BASE_DN = "OU=yanfa,DC=test,DC=com"
        BASE_DN = self.domain2dn(domain)
        #BASE_DN = "OU=%s,%s" % ("研发中心", BASE_DN)
        result = self.conn.search_s(BASE_DN, ldap.SCOPE_SUBTREE, "(objectClass=*)")
        res = []
        for basedn, objectclass in result:
            if basedn:
                if "group".encode() in objectclass.get("objectClass"):
                    dn_path = self.bashdn2path(basedn)
                    osid = objectclass.get("objectSid")
                    cn = objectclass.get("cn")[0].decode()
                    username = objectclass.get("name")[0].decode()
                    #name = objectclass.get("name")[0].decode()
                    
                    name = cn
                    if objectclass.get("displayName", ""):
                        name = objectclass.get("displayName")[0].decode()
                        
                    objectguid = str(uuid.UUID(bytes= objectclass.get("objectGUID")[0]))
                    if search_str in cn:

                        tmp = {}
                        tmp["cn"] = cn
                        tmp["username"] = cn
                        tmp["name"] = name
                        tmp["dnpath"] = dn_path
                        tmp["objectguid"] = objectguid
                        tmp["objecttype"] = "group"
                        res.append(tmp)
                        
        count = len(res)
        if page > count / pagecount + 1:
            return {"msg": "page error"}
        
        start = (page - 1) * pagecount
        #end =  (count - 1) if  (start + pagecount >= count) else (start + pagecount)
        end =  start + pagecount
        pages = count / pagecount + 1
        
        r = {
            "count":count,
            "pages": pages,
            "pagecount": pagecount,
            "data": res[start:end]
            }
        
        return r            
        return res

            
            
        
    def query_users_by_group(self, domain, cn):
        #BASE_DN = "OU=yanfa,DC=test,DC=com"
        BASE_DN = self.domain2dn(domain)
        BASE_DN = "CN=%s,%s" % (cn, BASE_DN)
        result = self.conn.search_s(BASE_DN, ldap.SCOPE_SUBTREE, "(objectClass=*)")
        
        res = []
        for basedn, objectclass in result:
            if basedn:

                members = objectclass.get("member")
                for bdn in members:
                    
                    r = self.conn.search_s(bdn.decode(), ldap.SCOPE_SUBTREE, "(objectClass=*)")
                    for b, oc in r:
                        if b:
                            #print(objectclass.get("objectClass"))
                            if "user".encode() in oc.get("objectClass"):
                                dn_path = self.bashdn2path(b)
                                osid = oc.get("objectSid")
                                cn = oc.get("cn")[0].decode()
                                name = oc.get("name")[0].decode()
                                name = cn
                                if objectclass.get("displayName", ""):
                                    name = objectclass.get("displayName")[0].decode()                                
                                objectguid = str(uuid.UUID(bytes= oc.get("objectGUID")[0]))
                                username = oc.get("userPrincipalName")[0].decode()                    
                                tmp = {}
                                tmp["cn"] = cn
                                tmp["name"] = name
                                tmp["username"] = username
                                tmp["dnpath"] = dn_path
                                tmp["objectguid"] = objectguid
                                tmp["objecttype"] = "user"
                                res.append(tmp)
                        
        

        
        return res


    def query_users_by_group_new(self, dnpath):
        #BASE_DN = "OU=yanfa,DC=test,DC=com"
        #dnpath = "123123/研发中心/thxh.com."
        dnlist = dnpath.split("/")
        domain = dnlist[-1][:-1]
        BASE_DN = self.domain2dn(domain)
        cn_str = dnlist[0]
        ou_str = ""
        for ou_i in dnlist[1:-1]:
            ou_str += "OU=%s," % ou_i
        
        BASE_DN = "CN=%s,%s%s" % (cn_str, ou_str, BASE_DN)
        
        result = self.conn.search_s(BASE_DN, ldap.SCOPE_SUBTREE, "(objectClass=*)")
        
        res = []
        for basedn, objectclass in result:
            if basedn:

                members = objectclass.get("member")
                for bdn in members:
                    
                    r = self.conn.search_s(bdn.decode(), ldap.SCOPE_SUBTREE, "(objectClass=*)")
                    for b, oc in r:
                        if b:
                            #print(objectclass.get("objectClass"))
                            if "user".encode() in oc.get("objectClass"):
                                dn_path = self.bashdn2path(b)
                                osid = oc.get("objectSid")
                                cn = oc.get("cn")[0].decode()
                                name = oc.get("name")[0].decode()
                                
                                objectguid = str(uuid.UUID(bytes= oc.get("objectGUID")[0]))
                                username = oc.get("userPrincipalName")[0].decode()                    
                                tmp = {}
                                tmp["cn"] = cn
                                tmp["name"] = name
                                tmp["username"] = username
                                tmp["dnpath"] = dn_path
                                tmp["objectguid"] = objectguid
                                tmp["objecttype"] = "user"
                                res.append(tmp)
                        
        

        
        return res
       
        



class Ldap3Client(object):
    

    def __init__(self, domain, server, user , password):
        self.ldap_url = "ldap://%s" % server
        self.ldap_server = server
        self.username = user
        self.domain = domain
        self.ldap_user = user
        self.ldap_password = password
        self.base_dn = f'DC={self.domain.split(".")[0]},DC={self.domain.split(".")[1]}'
        
        try:
            server = Server(self.ldap_server, use_ssl=True, port=636, get_info=ALL)
            self.conn = Connection(server, user=f'{self.ldap_user}@{self.domain}', password=self.ldap_password, auto_bind=True)
        
        except Exception as e:
            traceback.print_exc()
            raise Exception(e)

    def bashdn2path(self, basedn):
        dnpath = ""
        back_basedn_list = basedn.split(",")
        
        for k in back_basedn_list:
            if k.split("=")[0] == "DC":
                dnpath += "%s." % k.split("=")[1]
            else:
                dnpath += "%s/" % k.split("=")[1]
        
        return dnpath
    
    def domain2dn(self, domain):
        base_dn_list = []
        for k in domain.split("."):
            base_dn_list.append("DC=%s" % k)
        
        BASE_DN = ",".join(base_dn_list)
        return BASE_DN
    
        
    def create_users(self, domain, username, password, name, phone, email):

        base_dn = f'DC={domain.split(".")[0]},DC={domain.split(".")[1]}'
        
        #username = 'maojj012'
        user_dn = f'CN={username},OU=云用户,{base_dn}'
        

        attrs = {
            'objectClass': ['top', 'person', 'organizationalPerson', 'user'],
            'cn': username,
            'sAMAccountName': username,
            'displayName': name,
            'name': name,
            'userPrincipalName': f'{username}@{domain}'
            #'userAccountControl': '66048',  # 正常账户且密码永不过期
        }
        
        if phone:
            attrs["telephoneNumber"] = phone

        if email:
            attrs["mail"] = email


        self.conn.search(user_dn, '(cn={})'.format(username))
        
        if len(self.conn.entries) > 0:
            return "云用户已存在"

        self.conn.add(user_dn, attributes=attrs)
        # 定义用户密码
        password = 'thecloud'
        self.conn.extend.microsoft.modify_password(user_dn, new_password=password)        
        # 修改用户属性（例如：设置密码不过期）
        modifications = {'userAccountControl': [(MODIFY_REPLACE, ['66048'])]}  # 正常账户且密码永不过期
        self.conn.modify(user_dn, modifications)
        
        self.conn.unbind()
        
        return "ok"
        
        
    def check_user_and_create_user(self, domain, username, password, name, phone, email):

        base_dn = f'DC={domain.split(".")[0]},DC={domain.split(".")[1]}'
        
        #username = 'maojj012'
        user_dn = f'CN={username},OU=云用户,{base_dn}'
        

        attrs = {
            'objectClass': ['top', 'person', 'organizationalPerson', 'user'],
            'cn': username,
            'sAMAccountName': username,
            'name': name,
            'userPrincipalName': f'{username}@{domain}'
            #'userAccountControl': '66048',  # 正常账户且密码永不过期
        }
        
        if phone:
            attrs["telephoneNumber"] = phone

        if email:
            attrs["mail"] = email

 
        self.conn.add(user_dn, attributes=attrs)
        # 定义用户密码
        password = 'thecloud'
        
        
        self.conn.extend.microsoft.modify_password(user_dn, new_password=password)        
        # 修改用户属性（例如：设置密码不过期）
        modifications = {'userAccountControl': [(MODIFY_REPLACE, ['66048'])]}  # 正常账户且密码永不过期
        self.conn.modify(user_dn, modifications)
        
        self.conn.unbind()
        
        return "ok"




        