import json
from dataclasses import dataclass, asdict
from datetime import datetime

from loguru import logger as loguru_logger
import settings as se  # 假设se模块包含LOG_PATH等配置

@dataclass
class OperationLog:
    username: str      # 操作用户名
    category: str      # 类别
    action: str        # 操作
    result: str        # 操作结果
    role: str          # 操作角色
    description: str   # 详细描述
    timestamp: str     # 时间戳


class CustomLogger:
    def __init__(self, sink=None):
        loguru_logger.remove()

        if sink is None:
            sink = se.JSON_LOG_PATH

        loguru_logger.add(
            sink=sink,
            format=self.format_message,
            enqueue=True,
            rotation="4 months",  # 多少日志文件转存
            retention="12 months",  # 多少时间之后清理
            encoding="utf-8",
            backtrace=True,
            diagnose=True,
            compression="zip",
        )

    def format_message(self, record):
        # 返回自定义 JSON 字符串
        return "{message}\n"

    def create_operation_log(self, username: str, category: str, action: str, result: str, role: str, description: str) -> OperationLog:
        # 获取当前时间
        current_time = datetime.now()
        # 格式化时间为指定格式
        formatted_time = current_time.strftime("%Y/%m/%d %H:%M:%S")
        return OperationLog(username, category, action, result, role, description, formatted_time)

    def log(self, username: str, category: str, action: str, result: str, role: str, description: str, level: str = "INFO"):
        operation_log = self.create_operation_log(username, category, action, result, role, description)
        loguru_logger.log(level, self.data_processing(operation_log))

    def data_processing(self, operation_log: OperationLog) -> str:
        # 将OperationLog实例转换为字典，然后转换为JSON字符串
        # return json.dumps(asdict(operation_log))
        return json.dumps(asdict(operation_log), ensure_ascii=False)


# from api.log.log import CustomLogger
# @get(_path="/v1/groups", _produces=mediatypes.APPLICATION_JSON)
# def thecloud_get_all_groups(self):
#
#     logger1 = CustomLogger()
#     logger1.log(
#         "File Management", "Delete File", "Success", "Admin",
#         "Admin deleted the file 'example.txt' from the server."
#     )