
import requests
import json
import settings
from api.model.network import Network, NetworkDetail,SubnetDetail
from dataclasses import dataclass
from dacite import from_dict
from string import Template


class NeutronClient:
    def openstack_test(self, id):
        print(id)

    def openstack_get_all_list(self):
        method = "/v2.0/networks?project_id=fb2e022b1e6746ffb341914c8fd0ddaf"
        url = "%s%s" % (self.neutron_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        
        r = requests.get(url, headers=headers)
        
        d = json.loads(r.text)
        res = []
        for ins_dict in d["networks"]:
            ins = from_dict(data_class=Network, data=ins_dict)
            ins.__dict__["network_type"] = ins_dict["provider:network_type"]
            ins.__dict__["vlanid"] = ins_dict.get("provider:segmentation_id","")
            res.append(ins.__dict__)
        return res
    
    def openstack_create_network(self, networkcreateform):
        method = "/v2.0/networks"
        url = "%s%s" % (self.neutron_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        
        temp = """{
                "network": {
                    "name": "$name",
                    "admin_state_up": "true"
                }
            }"""
        
        t = Template(temp)
        body = t.substitute(networkcreateform.__dict__)
        
        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)
        
        d = json.loads(r.text)
        
        ins = from_dict(data_class=NetworkDetail, data=d["network"])
        return ins.__dict__
    
    def openstack_edit_network(self, networkeditform):
        method = "/v2.0/networks/%s" % networkeditform.id
        url = "%s%s" % (self.neutron_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        
        temp = """{
                "network": {
                    "name": "$name"
                }
            }"""
        
        t = Template(temp)
        body = t.substitute({"name":networkeditform.name})
        
        dd = json.dumps(json.loads(body))
        r = requests.put(url, data=dd, headers=headers)
        
        d = json.loads(r.text)
        
        ins = from_dict(data_class=NetworkDetail, data=d["network"])
        return ins.__dict__
    
   
    
    def openstack_create_subnet(self, id , form):
        method = "/v2.0/subnets"
        url = "%s%s" % (self.neutron_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        
        temp = """{
                "subnet": {
                    "name": "$name",
                    "network_id": "$id",
                    "enable_dhcp": "false",
                    "cidr": "$cidr",
                    "gateway_ip": "$gateway_ip",
                    "ip_version": 4
                }
            }"""
        
        t = Template(temp)
        body = t.substitute({"id":id,"name":form.name,"cidr":form.cidr,"gateway_ip":form.gateway_ip})
        
        dd = json.dumps(json.loads(body))
        r = requests.post(url, data=dd, headers=headers)
        
        if r.status_code == 201:
            return {"msg":"ok"}
        elif r.status_code == 409:
            return {"msg":"409"}
        else:
            return {"msg":"error"}
    
    def openstack_get_subnet_detail(self , id):
        method = "/v2.0/subnets/%s" %  id
        url = "%s%s" % (self.neutron_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        
        r = requests.get(url, headers=headers)
        
        d = json.loads(r.text)

        net = from_dict(data_class=SubnetDetail, data=d["subnet"])
        if d["subnet"]["gateway_ip"]:
            net.__dict__["gateway_ip"] = d["subnet"]["gateway_ip"]
        else:
            net.__dict__["gateway_ip"] = "-"

        return net.__dict__

    def openstack_delete_network(self, network_id):
        method = "/v2.0/networks/%s" % network_id
        url = "%s%s" % (self.neutron_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }
        
        r = requests.delete(url, headers=headers)
        
        if r.status_code == 204:
            return {"msg":"ok"}
        else:
            return {"msg":"error"}

        
        