import requests
import json
import settings
from api.model.container import ContainerDetail, ContainerCreateDetail
from dataclasses import dataclass
from dacite import from_dict
from string import Template


class ZunClient:
    def openstack_test(self, id):
        print(id)

    def openstack_get_all_container_list(self):
        method = "/containers/"
        url = "%s%s" % (self.zun_uri, method)
        headers = {
            "X-Auth-Token": self.token
        }

        r = requests.get(url, headers=headers)

        d = json.loads(r.text)
        res = []
        for ins_dict in d["containers"]:
            ins = from_dict(data_class=ContainerDetail, data=ins_dict)
            if ins_dict["task_state"]:
                ins.__dict__["task_state"] = ins_dict["task_state"]
            else:
                ins.__dict__["task_state"] = "None"
            res.append(ins.__dict__)
        return res

    def openstack_create_container(self, form):
        """创建容器,支持所有可选参数"""
        method = "/containers"
        url = "%s%s" % (self.zun_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Content-Type": "application/json",
            "OpenStack-API-Version": "container 1.32"
        }

        # 基础参数
        container_params = {
            "name": form.get("name", ""),
            "image": form.get("image", ""),
            "interactive": form.get("interactive", True),
            "image_driver": form.get("image_driver", "docker"),
        }

        # 网络配置
        if form.get("network_id"):
            container_params["nets"] = [{"network": form.get("network_id")}]

        # 可选参数
        optional_params = {
            "memory": form.get("memory", ""),
            "cpu": form.get("cpu", ""),
            "command": form.get("command"),
            "workdir": form.get("workdir"),
            "labels": form.get("labels"),
            "environment": form.get("environment"),
            "restart_policy": form.get("restart_policy"),
            "tty": form.get("tty"),
            "security_groups": form.get("security_groups"),
            "runtime": form.get("runtime"),
            "hostname": form.get("hostname"),
            "auto_remove": form.get("auto_remove"),
            "auto_heal": form.get("auto_heal"),
            "availability_zone": form.get("availability_zone"),
            "hints": form.get("hints"),
            "mounts": form.get("mounts"),
            "privileged": form.get("privileged"),
            "healthcheck": form.get("healthcheck"),
            "exposed_ports": form.get("exposed_ports"),
            "host": form.get("host"),
            "entrypoint": form.get("entrypoint"),
            "image_pull_policy": form.get("image_pull_policy"),
        }

        # 检查值是否为空的辅助函数
        def is_not_empty(value):
            if value is None:
                return False
            if isinstance(value, (str, list, dict)) and not value:
                return False
            return True

        # 合并有效的可选参数
        for key, value in optional_params.items():
            if is_not_empty(value):
                container_params[key] = value

        r = requests.post(url, json=container_params, headers=headers)

        if r.status_code == 202:
            try:
                d = r.json()
                ins = from_dict(data_class=ContainerCreateDetail, data=d)
                return ins.__dict__
            except Exception as e:
                return {"msg": "error", "error": str(e)}
        else:
            error_info = {
                "msg": "error",
                "status_code": r.status_code,
                "error": r.text
            }
            try:
                error_info["error_detail"] = r.json()
            except:
                error_info["error_detail"] = "无法解析错误详情"
            return error_info

    def openstack_container_kill(self, form):
        """Kill一个运行中的容器
        Args:
            form: 包含容器ID和可选signal参数的字典
        Returns:
            成功返回 {"msg":"ok"}
            失败返回 包含错误信息的字典
        """
        container_id = form.get("id")
        signal = form.get("signal", "")

        method = "/containers/%s/kill" % container_id
        if signal != "":
            method = "%s?signal=%s" % (method, signal)

        url = "%s%s" % (self.zun_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Content-Type": "application/json",
            "OpenStack-API-Version": "container 1.32"
        }

        r = requests.post(url, headers=headers)
        if r.status_code == 202:
            return {"msg": "ok"}
        else:
            error_info = {
                "msg": "error",
                "status_code": r.status_code,
                "error": r.text
            }
            try:
                error_info["error_detail"] = r.json()
            except:
                error_info["error_detail"] = "无法解析错误详情"
            return error_info

    def openstack_container_stats(self, id):
        """获取容器的统计信息
        Args:
            id: 容器ID或名称
        Returns:
            成功返回包含stats_info的字典
            失败返回错误信息
        """
        method = "/containers/%s/stats" % id
        url = "%s%s" % (self.zun_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Content-Type": "application/json",
            "OpenStack-API-Version": "container 1.32"
        }

        r = requests.get(url, headers=headers)
        if r.status_code == 200:
            return r.json()
        else:
            error_info = {
                "msg": "error",
                "status_code": r.status_code,
                "error": r.text
            }
            try:
                error_info["error_detail"] = r.json()
            except:
                error_info["error_detail"] = "无法解析错误详情"
            return error_info

    def openstack_container_update_information(self, form):
        """更新容器信息
        Args:
            form: 包含 id 和需要更新的属性(memory/cpu/name)的字典
        Returns:
            成功返回 {"msg":"ok"}
            失败返回 包含错误信息的字典
        """
        container_id = form.get("id")
        method = "/containers/%s" % container_id
        url = "%s%s" % (self.zun_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Content-Type": "application/json",
            "OpenStack-API-Version": "container 1.32"
        }

        # 可更新的参数
        update_params = {}
        if form.get("memory"):
            update_params["memory"] = form["memory"]
        if form.get("cpu"):
            update_params["cpu"] = form["cpu"]
        if form.get("name"):
            update_params["name"] = form["name"]

        r = requests.patch(url, json=update_params, headers=headers)
        if r.status_code == 200:
            return {"msg": "ok"}
        else:
            error_info = {
                "msg": "error",
                "status_code": r.status_code,
                "error": r.text
            }
            try:
                error_info["error_detail"] = r.json()
            except:
                error_info["error_detail"] = "无法解析错误详情"
            return error_info

    def openstack_container_stop(self, id):
        method = "/containers/%s/stop" % id
        url = "%s%s" % (self.zun_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Content-Type": "application/json"
        }
        r = requests.post(url, headers=headers)
        if r.status_code == 202:
            return {"msg": "ok"}
        else:
            return {"msg": "error"}

    def openstack_container_start(self, id):
        method = "/containers/%s/start" % id
        url = "%s%s" % (self.zun_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Content-Type": "application/json"
        }
        r = requests.post(url, headers=headers)
        if r.status_code == 202:
            return {"msg": "ok"}
        else:
            return {"msg": "error"}

    def openstack_container_pause(self, id):
        """暂停容器
        Args:
            id: 容器ID或名称
        Returns:
            成功返回 {"msg":"ok"}
            失败返回 包含错误信息的字典
        """
        method = "/containers/%s/pause" % id
        url = "%s%s" % (self.zun_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Content-Type": "application/json",
            "OpenStack-API-Version": "container 1.32"
        }

        r = requests.post(url, headers=headers)
        if r.status_code == 202:
            return {"msg": "ok"}
        else:
            error_info = {
                "msg": "error",
                "status_code": r.status_code,
                "error": r.text
            }
            try:
                error_info["error_detail"] = r.json()
            except:
                error_info["error_detail"] = "无法解析错误详情"
            return error_info

    def openstack_container_unpause(self, id):
        """恢复已暂停的容器
        Args:
            id: 容器ID或名称
        Returns:
            成功返回 {"msg":"ok"}
            失败返回 包含错误信息的字典
        """
        method = "/containers/%s/unpause" % id
        url = "%s%s" % (self.zun_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Content-Type": "application/json",
            "OpenStack-API-Version": "container 1.32"
        }

        r = requests.post(url, headers=headers)
        if r.status_code == 202:
            return {"msg": "ok"}
        else:
            error_info = {
                "msg": "error",
                "status_code": r.status_code,
                "error": r.text
            }
            try:
                error_info["error_detail"] = r.json()
            except:
                error_info["error_detail"] = "无法解析错误详情"
            return error_info

    def openstack_container_rebuild(self, form):
        """重建容器
        Args:
            form: 包含容器ID和重建参数的字典
                 必需参数: id, image
                 可选参数: image_driver
        Returns:
            成功返回 {"msg":"ok"}
            失败返回 包含错误信息的字典
        """
        container_id = form.get("id")
        method = "/containers/%s/rebuild" % container_id
        url = "%s%s" % (self.zun_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Content-Type": "application/json",
            "OpenStack-API-Version": "container 1.32"
        }

        # 构建请求参数
        rebuild_params = {
            "image": form.get("image")
        }

        # 添加可选参数
        if form.get("image_driver"):
            rebuild_params["image_driver"] = form["image_driver"]

        r = requests.post(url, json=rebuild_params, headers=headers)
        if r.status_code == 202:
            return {"msg": "ok"}
        else:
            error_info = {
                "msg": "error",
                "status_code": r.status_code,
                "error": r.text
            }
            try:
                error_info["error_detail"] = r.json()
            except:
                error_info["error_detail"] = "无法解析错误详情"
            return error_info

    def openstack_container_restart(self, id):
        method = "/containers/%s/reboot" % id
        url = "%s%s" % (self.zun_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Content-Type": "application/json"
        }
        r = requests.post(url, headers=headers)
        if r.status_code == 202:
            return {"msg": "ok"}
        else:
            return {"msg": "error"}

    def openstack_container_rename(self, id, name):
        """重命名容器
        Args:
            id: 容器ID或名称
            name: 新的容器名称
        Returns:
            成功返回 {"msg":"ok"}
            失败返回 包含错误信息的字典
        """
        method = "/containers/%s/rename?new_name=%s" % (id, name)
        url = "%s%s" % (self.zun_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Content-Type": "application/json",
            "OpenStack-API-Version": "container 1.32"
        }

        r = requests.post(url, headers=headers)
        if r.status_code == 200:
            return {"msg": "ok"}
        else:
            error_info = {
                "msg": "error",
                "status_code": r.status_code,
                "error": r.text
            }
            try:
                error_info["error_detail"] = r.json()
            except:
                error_info["error_detail"] = "无法解析错误详情"
            return error_info

    def openstack_container_get_archive(self, form):
        """获取容器中文件的tar归档
        Args:
            form: 包含容器ID和路径的字典
                 必需参数: id - 容器ID或名称
                          source_path - 容器内的文件路径
        Returns:
            成功返回 包含data和stat的字典
            失败返回 包含错误信息的字典
        """
        container_id = form.get("id")
        source_path = form.get("source_path")

        if not source_path:
            return {"msg": "error", "error": "source_path is required"}

        method = "/containers/%s/get_archive?source_path=%s" % (
            container_id, source_path)
        url = "%s%s" % (self.zun_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Content-Type": "application/json",
            "OpenStack-API-Version": "container 1.32"
        }

        r = requests.get(url, headers=headers)
        if r.status_code == 200:
            try:
                res = r.json()
                return {
                    "msg": "ok",
                    "data": res.get("data"),
                    "stat": res.get("stat")
                }
            except Exception as e:
                return {"msg": "error", "error": str(e)}
        else:
            error_info = {
                "msg": "error",
                "status_code": r.status_code,
                "error": r.text
            }
            try:
                error_info["error_detail"] = r.json()
            except:
                error_info["error_detail"] = "无法解析错误详情"
            return error_info

    def openstack_container_put_archive(self, form):
        """上传tar归档到容器
        Args:
            form: 包含参数的字典:
                 - id: 容器ID或名称
                 - destination_path: 容器内的目标路径
                 - data: tar文件内容
        Returns:
            成功返回 {"msg":"ok"}
            失败返回 包含错误信息的字典
        """
        container_id = form.get("id")
        destination_path = form.get("destination_path")
        data = form.get("data")

        if not destination_path:
            return {"msg": "error", "error": "destination_path is required"}
        if not data:
            return {"msg": "error", "error": "data is required"}

        method = "/containers/%s/put_archive?destination_path=%s" % (
            container_id, destination_path)
        url = "%s%s" % (self.zun_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Content-Type": "application/json",
            "OpenStack-API-Version": "container 1.32"
        }

        # 构造请求体
        body = {
            "data": data
        }

        r = requests.post(url, json=body, headers=headers)
        if r.status_code == 200:
            return {"msg": "ok"}
        else:
            error_info = {
                "msg": "error",
                "status_code": r.status_code,
                "error": r.text
            }
            try:
                error_info["error_detail"] = r.json()
            except:
                error_info["error_detail"] = "无法解析错误详情"
            return error_info

    def openstack_container_add_security_group(self, form):
        """为容器添加安全组
        Args:
            form: 包含容器ID和安全组名称的字典
                 必需参数: 
                 - id: 容器ID或名称
                 - security_group: 要添加的安全组名称
        Returns:
            成功返回 {"msg":"ok"}
            失败返回 包含错误信息的字典
        """
        container_id = form.get("id")
        security_group = form.get("security_group")

        if not security_group:
            return {"msg": "error", "error": "security_group is required"}

        method = "/containers/%s/add_security_group?security_group=%s" % (
            container_id, security_group)
        url = "%s%s" % (self.zun_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Content-Type": "application/json",
            "OpenStack-API-Version": "container 1.32"
        }

        r = requests.post(url, headers=headers)
        if r.status_code == 202:
            return {"msg": "ok"}
        else:
            error_info = {
                "msg": "error",
                "status_code": r.status_code,
                "error": r.text
            }
            try:
                error_info["error_detail"] = r.json()
            except:
                error_info["error_detail"] = "无法解析错误详情"
            return error_info

    def openstack_container_remove_security_group(self, form):
        """为容器移除安全组
        Args:
            form: 包含容器ID和安全组名称的字典
                 必需参数: 
                 - id: 容器ID或名称
                 - security_group: 要添加的安全组名称
        Returns:
            成功返回 {"msg":"ok"}
            失败返回 包含错误信息的字典
        """
        container_id = form.get("id")
        security_group = form.get("security_group")

        if not security_group:
            return {"msg": "error", "error": "security_group is required"}

        method = "/containers/%s/remove_security_group?security_group=%s" % (
            container_id, security_group)
        url = "%s%s" % (self.zun_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Content-Type": "application/json",
            "OpenStack-API-Version": "container 1.32"
        }

        r = requests.post(url, headers=headers)
        if r.status_code == 202:
            return {"msg": "ok"}
        else:
            error_info = {
                "msg": "error",
                "status_code": r.status_code,
                "error": r.text
            }
            try:
                error_info["error_detail"] = r.json()
            except:
                error_info["error_detail"] = "无法解析错误详情"
            return error_info

    def openstack_container_resize(self, form):
        """调整容器TTY大小
        Args:
            form: 包含参数的字典:
                必需参数:
                - id: 容器ID或名称
                - width: TTY宽度
                - height: TTY高度
        Returns:
            成功返回 {"msg":"ok"}
            失败返回 包含错误信息的字典
        """
        container_id = form.get("id")
        width = form.get("width")
        height = form.get("height")

        if not width or not height:
            return {"msg": "error", "error": "width and height are required"}

        method = "/containers/%s/resize?width=%s&height=%s" % (
            container_id, width, height)
        url = "%s%s" % (self.zun_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Content-Type": "application/json",
            "OpenStack-API-Version": "container 1.32"
        }

        r = requests.post(url, headers=headers)
        if r.status_code == 200:
            return {"msg": "ok"}
        else:
            error_info = {
                "msg": "error",
                "status_code": r.status_code,
                "error": r.text
            }
            try:
                error_info["error_detail"] = r.json()
            except:
                error_info["error_detail"] = "无法解析错误详情"
            return error_info

    def openstack_container_delete(self, form):
        """
        删除容器
        Args:
            id: 容器的UUID或名称
            force: 是否强制删除容器 (默认: True)
            stop: 是否在删除前停止容器 (默认: False)
        Returns:
            成功返回 {"msg": "ok"}
            失败返回 包含错误信息的字典
        """
        container_id = form.get("container_id")
        force = form.get("force", True)
        stop = form.get("stop", False)

        # 构建请求路径
        query_params = []
        if force:
            query_params.append("force=True")
        if stop:
            query_params.append("stop=True")
        method = f"/containers/{container_id}"
        if query_params:
            method = f"{method}?{'&'.join(query_params)}"

        url = f"{self.zun_uri}{method}"
        headers = {
            "X-Auth-Token": self.token,
            "Content-Type": "application/json",
            "OpenStack-API-Version": "container 1.32"
        }

        # 发送DELETE请求
        r = requests.delete(url, headers=headers)
        if r.status_code == 204:
            return {"msg": "ok"}
        else:
            error_info = {
                "msg": "error",
                "status_code": r.status_code,
                "error": r.text
            }
            try:
                error_info["error_detail"] = r.json()
            except:
                error_info["error_detail"] = "无法解析错误详情"
            return error_info

    def openstack_container_logs(self, form):
        """获取容器日志
        Args:
            form: 包含参数的字典:
                 必需参数:
                 - id: 容器ID或名称
                 可选参数:
                 - timestamps: 是否显示时间戳(布尔值)
                 - tail: 显示最后多少行日志
                 - stderr: 是否获取标准错误输出(布尔值)
                 - stdout: 是否获取标准输出(布尔值)
                 - since: 从指定时间开始显示日志(datetime或epoch秒数)
        Returns:
            成功返回 {"msg":"ok", "logs":日志内容}
            失败返回 包含错误信息的字典
        """
        container_id = form.get("id")

        # 构建查询参数
        params = []
        if "timestamps" in form:
            params.append("timestamps=%s" % str(form["timestamps"]).lower())
        if "tail" in form:
            params.append("tail=%s" % form["tail"])
        if "stderr" in form:
            params.append("stderr=%s" % str(form["stderr"]).lower())
        if "stdout" in form:
            params.append("stdout=%s" % str(form["stdout"]).lower())
        if "since" in form:
            params.append("since=%s" % form["since"])

        method = "/containers/%s/logs" % container_id
        if params:
            method = "%s?%s" % (method, "&".join(params))

        url = "%s%s" % (self.zun_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Content-Type": "application/json",
            "OpenStack-API-Version": "container 1.32"
        }

        r = requests.get(url, headers=headers)
        if r.status_code == 200:
            logs = r.text.replace("\\r\\n", "<br/>")
            return {"msg": "ok", "logs": logs}
        else:
            error_info = {
                "msg": "error",
                "status_code": r.status_code,
                "error": r.text
            }
            try:
                error_info["error_detail"] = r.json()
            except:
                error_info["error_detail"] = "无法解析错误详情"
            return error_info

    def openstack_container_hosts(self):
        """获取所有容器宿主机信息
        Returns:
            成功返回 包含宿主机信息的字典:
            {
                "msg": "ok",
                "hosts": [
                    {
                        "architecture": "x86_64",
                        "cpus": "16",
                        "cpu_used": "4",
                        "disk_total": "1024GB", 
                        "disk_used": "256GB",
                        "hostname": "compute1",
                        "kernel_version": "4.15.0-76-generic",
                        "labels": {"zone": "az1"},
                        "mem_total": "128GB",
                        "mem_used": "64GB", 
                        "os": "Ubuntu 18.04.4 LTS",
                        "os_type": "linux",
                        "total_containers": "10",
                        "uuid": "550e8400-e29b-41d4-a716-************",
                        "enable_cpu_pinning": false
                    }
                ]
            }
            失败返回 包含错误信息的字典
        """
        method = "/hosts"
        url = "%s%s" % (self.zun_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Content-Type": "application/json",
            "OpenStack-API-Version": "container 1.32"
        }

        r = requests.get(url, headers=headers)
        if r.status_code == 200:
            try:
                hosts = r.json().get("hosts", [])
                return {
                    "msg": "ok",
                    "hosts": [
                        {
                            "architecture": host.get("architecture"),
                            "cpus": host.get("cpus"),
                            "cpu_used": host.get("cpu_used"),
                            "disk_total": host.get("disk_total"),
                            "disk_used": host.get("disk_used"),
                            "hostname": host.get("hostname"),
                            "kernel_version": host.get("kernel_version"),
                            "labels": host.get("labels"),
                            "mem_total": host.get("mem_total"),
                            "mem_used": host.get("mem_used"),
                            "os": host.get("os"),
                            "os_type": host.get("os_type"),
                            "total_containers": host.get("total_containers"),
                            "uuid": host.get("uuid"),
                            "enable_cpu_pinning": host.get("enable_cpu_pinning", False)
                        }
                        for host in hosts
                    ]
                }
            except Exception as e:
                return {"msg": "error", "error": str(e)}
        else:
            error_info = {
                "msg": "error",
                "status_code": r.status_code,
                "error": r.text
            }
            try:
                error_info["error_detail"] = r.json()
            except:
                error_info["error_detail"] = "无法解析错误详情"
            return error_info

    def openstack_container_host_details(self, host_ident):
        """获取指定宿主机的详细信息
        Args:
            host_ident: 宿主机的UUID或名称
        Returns:
            成功返回 包含主机详细信息的字典:
            {
                "msg": "ok",
                "host": {
                    "architecture": "x86_64",
                    "cpus": "16",
                    "cpu_used": "4",
                    "disk_total": "1024GB",
                    "disk_used": "256GB",
                    "hostname": "compute1",
                    "kernel_version": "4.15.0-76-generic",
                    "labels": {"zone": "az1"},
                    "mem_total": "128GB",
                    "mem_used": "64GB",
                    "os": "Ubuntu 18.04.4 LTS",
                    "os_type": "linux",
                    "total_containers": "10",
                    "uuid": "550e8400-e29b-41d4-a716-************",
                    "enable_cpu_pinning": false
                }
            }
            失败返回 包含错误信息的字典
        """
        method = "/hosts/%s" % host_ident
        url = "%s%s" % (self.zun_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Content-Type": "application/json",
            "OpenStack-API-Version": "container 1.32"
        }

        r = requests.get(url, headers=headers)
        if r.status_code == 200:
            try:
                host = r.json()
                return {
                    "msg": "ok",
                    "host": {
                        "architecture": host.get("architecture"),
                        "cpus": host.get("cpus"),
                        "cpu_used": host.get("cpu_used"),
                        "disk_total": host.get("disk_total"),
                        "disk_used": host.get("disk_used"),
                        "hostname": host.get("hostname"),
                        "kernel_version": host.get("kernel_version"),
                        "labels": host.get("labels"),
                        "mem_total": host.get("mem_total"),
                        "mem_used": host.get("mem_used"),
                        "os": host.get("os"),
                        "os_type": host.get("os_type"),
                        "total_containers": host.get("total_containers"),
                        "uuid": host.get("uuid"),
                        "enable_cpu_pinning": host.get("enable_cpu_pinning", False)
                    }
                }
            except Exception as e:
                return {"msg": "error", "error": str(e)}
        else:
            error_info = {
                "msg": "error",
                "status_code": r.status_code,
                "error": r.text
            }
            try:
                error_info["error_detail"] = r.json()
            except:
                error_info["error_detail"] = "无法解析错误详情"
            return error_info

    def openstack_get_availability_zones(self):
        method = "/v2.0/availability_zones"
        url = "%s%s" % (self.neutron_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Content-Type": "application/json",
            "OpenStack-API-Version": "container 1.32"
        }
        r = requests.get(url, headers=headers)
        if r.status_code == 200:
            d = json.loads(r.text)
            return d
        else:
            error_info = {
                "msg": "error",
                "status_code": r.status_code,
                "error": r.text  # 获取原始错误响应
            }
            try:
                error_info["error_detail"] = r.json()  # 尝试解析详细错误信息
            except:
                error_info["error_detail"] = "无法解析错误详情"
            return error_info

    def openstack_get_all_volumes(self):
        method = "/api/cinder/volumes/"
        url = "%s%s" % (self.cinderv3_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Content-Type": "application/json",
            "OpenStack-API-Version": "container 1.32"
        }

        r = requests.get(url, headers=headers)
        d = json.loads(r.text)

        if r.status_code == 200:
            return d
        else:
            return {"msg": "error", "data": d}

    def openstack_container_commit(self, form):
        """将容器提交为新镜像
        Args:
            form: 包含参数的字典:
                 必需参数:
                 - id: 容器ID或名称
                 - repository: 镜像仓库
                 可选参数:
                 - tag: 镜像标签
        Returns:
            成功返回 包含镜像信息的字典
            失败返回 包含错误信息的字典
        """
        container_id = form.get("id")
        repository = form.get("repository")
        tag = form.get("tag")

        if not repository:
            return {"msg": "error", "error": "repository is required"}

        method = "/containers/%s/commit?repository=%s" % (
            container_id, repository)
        if tag:
            method = "%s&tag=%s" % (method, tag)

        url = "%s%s" % (self.zun_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Content-Type": "application/json",
            "OpenStack-API-Version": "container 1.32"
        }

        r = requests.post(url, headers=headers)
        if r.status_code == 202:
            try:
                res = r.json()
                return {
                    "msg": "ok",
                    "image": res.get("image")
                }
            except Exception as e:
                return {"msg": "error", "error": str(e)}
        else:
            error_info = {
                "msg": "error",
                "status_code": r.status_code,
                "error": r.text
            }
            try:
                error_info["error_detail"] = r.json()
            except:
                error_info["error_detail"] = "无法解析错误详情"
            return error_info

    def openstack_container_attach(self, id):
        """附加到运行中的容器
        Args:
            id: 容器ID或名称
        Returns:
            成功返回 响应内容
            失败返回 包含错误信息的字典
        """
        method = "/containers/%s/attach" % id
        url = "%s%s" % (self.zun_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Content-Type": "application/json",
            "OpenStack-API-Version": "container 1.32"
        }

        r = requests.get(url, headers=headers)
        if r.status_code == 200:
            return r.content
        else:
            error_info = {
                "msg": "error",
                "status_code": r.status_code,
                "error": r.text
            }
            try:
                error_info["error_detail"] = r.json()
            except:
                error_info["error_detail"] = "无法解析错误详情"
            return error_info

    def openstack_container_network_detach(self, form):
        """从容器分离网络
        Args:
            form: 包含参数的字典:
                 必需参数:
                 - id: 容器ID或名称
                 可选参数:
                 - network: 要分离的网络ID或名称
                 - port: 要分离的端口ID或名称
                 注意: network和port参数互斥
        Returns:
            成功返回 {"msg":"ok"}
            失败返回 包含错误信息的字典
        """
        container_id = form.get("id")
        network = form.get("network")
        port = form.get("port")

        if network and port:
            return {"msg": "error", "error": "network and port are mutually exclusive"}

        method = "/containers/%s/network_detach" % container_id
        if network:
            method = "%s?network=%s" % (method, network)
        elif port:
            method = "%s?port=%s" % (method, port)

        url = "%s%s" % (self.zun_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Content-Type": "application/json",
            "OpenStack-API-Version": "container 1.32"
        }

        r = requests.post(url, headers=headers)
        if r.status_code == 202:
            return {"msg": "ok"}
        else:
            error_info = {
                "msg": "error",
                "status_code": r.status_code,
                "error": r.text
            }
            try:
                error_info["error_detail"] = r.json()
            except:
                error_info["error_detail"] = "无法解析错误详情"
            return error_info

    def openstack_container_network_attach(self, form):
        """将网络附加到容器
        Args:
            form: 包含参数的字典:
                 必需参数:
                 - id: 容器ID或名称
                 可选参数:
                 - network: 要附加的网络ID或名称
                 - port: 要绑定的端口ID或名称
                 - fixed_ip: 固定IP地址
                 注意: 
                 - network和port参数互斥
                 - 不指定network时不能指定fixed_ip
        Returns:
            成功返回 {"msg":"ok"}
            失败返回 包含错误信息的字典
        """
        container_id = form.get("id")
        network = form.get("network")
        port = form.get("port")
        fixed_ip = form.get("fixed_ip")

        if network and port:
            return {"msg": "error", "error": "network and port are mutually exclusive"}

        if fixed_ip and not network:
            return {"msg": "error", "error": "fixed_ip requires network parameter"}

        method = "/containers/%s/network_attach" % container_id
        params = []

        if network:
            params.append("network=%s" % network)
        if port:
            params.append("port=%s" % port)
        if fixed_ip:
            params.append("fixed_ip=%s" % fixed_ip)

        if params:
            method = "%s?%s" % (method, "&".join(params))

        url = "%s%s" % (self.zun_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Content-Type": "application/json",
            "OpenStack-API-Version": "container 1.32"
        }

        r = requests.post(url, headers=headers)
        if r.status_code == 202:
            return {"msg": "ok"}
        else:
            error_info = {
                "msg": "error",
                "status_code": r.status_code,
                "error": r.text
            }
            try:
                error_info["error_detail"] = r.json()
            except:
                error_info["error_detail"] = "无法解析错误详情"
            return error_info

    def openstack_container_execute(self, form):
        """在运行中的容器内执行命令
        Args:
            form: 包含参数的字典:
                 必需参数:
                 - id: 容器ID或名称
                 - command: 要执行的命令
                 可选参数:
                 - run: 是否立即执行命令 (布尔值)
                 - interactive: 是否开启交互模式 (布尔值) 
        Returns:
            run=true时返回:
            {
                "msg": "ok",
                "output": "命令输出",
                "exit_code": "退出码",
                "exec_id": None,
                "url": None
            }
            run=false时返回:
            {
                "msg": "ok", 
                "output": None,
                "exit_code": None,
                "exec_id": "执行ID",
                "url": "执行URL"
            }
            失败返回 包含错误信息的字典
        """
        container_id = form.get("id")
        command = form.get("command")
        run = form.get("run", "")
        interactive = form.get("interactive", "")

        if not command:
            return {"msg": "error", "error": "command is required"}

        method = "/containers/%s/execute?command=%s" % (container_id, command)
        if run != "":
            method = "%s&run=false" % method
        if interactive != "":
            method = "%s&interactive=true" % method

        url = "%s%s" % (self.zun_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Content-Type": "application/json",
            "OpenStack-API-Version": "container 1.32"
        }

        r = requests.post(url, headers=headers)
        if r.status_code == 200:
            try:
                res = r.json()
                return {
                    "msg": "ok",
                    "output": res.get("output"),
                    "exit_code": res.get("exit_code"),
                    "exec_id": res.get("exec_id"),
                    "url": res.get("url")
                }
            except Exception as e:
                return {"msg": "error", "error": str(e)}
        else:
            error_info = {
                "msg": "error",
                "status_code": r.status_code,
                "error": r.text
            }
            try:
                error_info["error_detail"] = r.json()
            except:
                error_info["error_detail"] = "无法解析错误详情"
            return error_info

    def openstack_container_execute_resize(self, form):
        """调整执行命令时的TTY大小
        Args:
            form: 包含参数的字典:
                 必需参数:
                 - id: 容器ID或名称
                 - exec_id: 执行实例ID
                 - width: TTY宽度
                 - height: TTY高度
        Returns:
            成功返回 包含exec_resize_output的字典
            失败返回 包含错误信息的字典
        """
        container_id = form.get("id")
        exec_id = form.get("exec_id")
        width = form.get("width")
        height = form.get("height")

        if not all([exec_id, width, height]):
            return {"msg": "error", "error": "exec_id, width and height are required"}

        method = "/containers/%s/execute_resize?exec_id=%s&width=%s&height=%s" % (
            container_id, exec_id, width, height)

        url = "%s%s" % (self.zun_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Content-Type": "application/json",
            "OpenStack-API-Version": "container 1.32"
        }

        r = requests.post(url, headers=headers)
        if r.status_code == 200:
            try:
                res = r.json()
                return {
                    "msg": "ok",
                    "exec_resize_output": res.get("exec_resize_output")
                }
            except Exception as e:
                return {"msg": "error", "error": str(e)}
        else:
            error_info = {
                "msg": "error",
                "status_code": r.status_code,
                "error": r.text
            }
            try:
                error_info["error_detail"] = r.json()
            except:
                error_info["error_detail"] = "无法解析错误详情"
            return error_info

    def openstack_container_top(self, form):
        """获取容器运行进程列表
        Args:
            form: 包含参数的字典:
                 必需参数:
                 - id: 容器ID或名称
                 可选参数:
                 - ps_args: ps命令的参数
        Returns:
            成功返回 包含ps_output的字典
            失败返回 包含错误信息的字典
        """
        container_id = form.get("id")
        ps_args = form.get("ps_args")

        method = "/containers/%s/top" % container_id
        if ps_args:
            method = "%s?ps_args=%s" % (method, ps_args)

        url = "%s%s" % (self.zun_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Content-Type": "application/json",
            "OpenStack-API-Version": "container 1.32"
        }

        r = requests.get(url, headers=headers)
        if r.status_code == 200:
            try:
                res = r.json()
                return {
                    "msg": "ok",
                    "ps_output": res.get("ps_output")
                }
            except Exception as e:
                return {"msg": "error", "error": str(e)}
        else:
            error_info = {
                "msg": "error",
                "status_code": r.status_code,
                "error": r.text
            }
            try:
                error_info["error_detail"] = r.json()
            except:
                error_info["error_detail"] = "无法解析错误详情"
            return error_info

    def openstack_container_network_list(self, form):
        """获取容器的网络列表
        Args:
            form: 包含容器ID的字典:
                 必需参数:
                 - id: 容器ID或名称
        Returns:
            成功返回 包含网络列表的字典
            失败返回 包含错误信息的字典
        """
        container_id = form.get("id")

        method = "/containers/%s/network_list" % container_id
        url = "%s%s" % (self.zun_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Content-Type": "application/json",
            "OpenStack-API-Version": "container 1.32"
        }

        r = requests.get(url, headers=headers)
        if r.status_code == 200:
            try:
                networks = r.json()
                return {
                    "msg": "ok",
                    "networks": [
                        {
                            "net_id": net.get("net_id"),
                            "subnet_id": net.get("subnet_id"),
                            "port_id": net.get("port_id"),
                            "version": net.get("version"),
                            "ip_address": net.get("ip_address"),
                            "fixed_ips": net.get("fixed_ips")
                        }
                        for net in networks
                    ]
                }
            except Exception as e:
                return {"msg": "error", "error": str(e)}
        else:
            error_info = {
                "msg": "error",
                "status_code": r.status_code,
                "error": r.text
            }
            try:
                error_info["error_detail"] = r.json()
            except:
                error_info["error_detail"] = "无法解析错误详情"
            return error_info

    def openstack_container_actions(self, form):
        """获取容器的操作记录列表
        Args:
            form: 包含容器ID的字典:
                 必需参数:
                 - id: 容器ID或名称
        Returns:
            成功返回 包含操作记录列表的字典
            失败返回 包含错误信息的字典
        """
        container_id = form.get("id")

        method = "/containers/%s/container_actions" % container_id
        url = "%s%s" % (self.zun_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Content-Type": "application/json",
            "OpenStack-API-Version": "container 1.32"
        }

        r = requests.get(url, headers=headers)
        if r.status_code == 200:
            try:
                actions = r.json()
                return {
                    "msg": "ok",
                    "actions": [
                        {
                            "action": action.get("action"),
                            "container_uuid": action.get("container_uuid"),
                            "message": action.get("message"),
                            "request_id": action.get("request_id"),
                            "start_time": action.get("start_time"),
                            "project_id": action.get("project_id"),
                            "user_id": action.get("user_id")
                        }
                        for action in actions
                    ]
                }
            except Exception as e:
                return {"msg": "error", "error": str(e)}
        else:
            error_info = {
                "msg": "error",
                "status_code": r.status_code,
                "error": r.text
            }
            try:
                error_info["error_detail"] = r.json()
            except:
                error_info["error_detail"] = "无法解析错误详情"
            return error_info

    def openstack_container_action_details(self, form):
        """获取容器操作的详细信息
        Args:
            form: 包含参数的字典:
                 必需参数:
                 - id: 容器ID或名称
                 - request_id: 操作请求ID
        Returns:
            成功返回 包含操作详情的字典
            失败返回 包含错误信息的字典
        """
        container_id = form.get("id")
        request_id = form.get("request_id")

        method = "/containers/%s/container_actions/%s" % (
            container_id, request_id)
        url = "%s%s" % (self.zun_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Content-Type": "application/json",
            "OpenStack-API-Version": "container 1.32"
        }

        r = requests.get(url, headers=headers)
        if r.status_code == 200:
            try:
                action = r.json()
                return {
                    "msg": "ok",
                    "action": {
                        "action": action.get("action"),
                        "container_uuid": action.get("container_uuid"),
                        "message": action.get("message"),
                        "project_id": action.get("project_id"),
                        "request_id": action.get("request_id"),
                        "start_time": action.get("start_time"),
                        "user_id": action.get("user_id"),
                        "events": [
                            {
                                "event": event.get("event"),
                                "start_time": event.get("start_time"),
                                "finish_time": event.get("finish_time"),
                                "result": event.get("result"),
                                "traceback": event.get("traceback")
                            }
                            for event in action.get("events", [])
                        ]
                    }
                }
            except Exception as e:
                return {"msg": "error", "error": str(e)}
        else:
            error_info = {
                "msg": "error",
                "status_code": r.status_code,
                "error": r.text
            }
            try:
                error_info["error_detail"] = r.json()
            except:
                error_info["error_detail"] = "无法解析错误详情"
            return error_info

    def openstack_get_zun_services(self):
        """获取所有Zun服务的状态信息
        Returns:
            成功返回 包含服务列表的字典
            失败返回 包含错误信息的字典
        """
        method = "/services"
        url = "%s%s" % (self.zun_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Content-Type": "application/json",
            "OpenStack-API-Version": "container 1.32"
        }

        r = requests.get(url, headers=headers)
        if r.status_code == 200:
            try:
                services = r.json().get("services", [])
                return {
                    "msg": "ok",
                    "services": [
                        {
                            "binary": svc.get("binary"),
                            "availability_zone": svc.get("availability_zone"),
                            "created_at": svc.get("created_at"),
                            "state": svc.get("state"),
                            "report_count": svc.get("report_count"),
                            "updated_at": svc.get("updated_at"),
                            "host": svc.get("host"),
                            "disabled_reason": svc.get("disabled_reason"),
                            "id": svc.get("id")
                        }
                        for svc in services
                    ]
                }
            except Exception as e:
                return {"msg": "error", "error": str(e)}
        else:
            error_info = {
                "msg": "error",
                "status_code": r.status_code,
                "error": r.text
            }
            try:
                error_info["error_detail"] = r.json()
            except:
                error_info["error_detail"] = "无法解析错误详情"
            return error_info

    def openstack_delete_service(self, form):
        """删除指定的Zun服务
        Args:
            form: 包含参数的字典:
                 必需参数:
                 - binary: Zun服务的二进制名称
                 - host: 服务所在主机名
        Returns:
            成功返回 {"msg":"ok"}
            失败返回 包含错误信息的字典
        """
        binary = form.get("binary")
        host = form.get("host")

        if not binary or not host:
            return {"msg": "error", "error": "binary and host are required"}

        method = "/services"
        url = "%s%s" % (self.zun_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Content-Type": "application/json",
            "OpenStack-API-Version": "container 1.32"
        }

        # 构造请求体
        body = {
            "binary": binary,
            "host": host
        }

        r = requests.delete(url, json=body, headers=headers)
        if r.status_code == 200:
            return {"msg": "ok"}
        else:
            error_info = {
                "msg": "error",
                "status_code": r.status_code,
                "error": r.text
            }
            try:
                error_info["error_detail"] = r.json()
            except:
                error_info["error_detail"] = "无法解析错误详情"
            return error_info

    def openstack_enable_service(self, form):
        """启用指定的Zun服务
        Args:
            form: 包含参数的字典:
                 必需参数:
                 - binary: Zun服务的二进制名称
                 - host: 服务所在主机名
        Returns:
            成功返回 {"msg":"ok"}
            失败返回 包含错误信息的字典
        """
        binary = form.get("binary")
        host = form.get("host")

        if not binary or not host:
            return {"msg": "error", "error": "binary and host are required"}

        method = "/services/enable"
        url = "%s%s" % (self.zun_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Content-Type": "application/json",
            "OpenStack-API-Version": "container 1.32"
        }

        # 构造请求体
        body = {
            "binary": binary,
            "host": host
        }

        r = requests.put(url, json=body, headers=headers)
        if r.status_code == 200:
            return {"msg": "ok"}
        else:
            error_info = {
                "msg": "error",
                "status_code": r.status_code,
                "error": r.text
            }
            try:
                error_info["error_detail"] = r.json()
            except:
                error_info["error_detail"] = "无法解析错误详情"
            return error_info

    def openstack_disable_service(self, form):
        """禁用指定的Zun服务
        Args:
            form: 包含参数的字典:
                 必需参数:
                 - binary: Zun服务的二进制名称
                 - host: 服务所在主机名
                 可选参数:
                 - disabled_reason: 禁用原因
        Returns:
            成功返回 包含服务信息的字典
            失败返回 包含错误信息的字典
        """
        binary = form.get("binary")
        host = form.get("host")

        if not binary or not host:
            return {"msg": "error", "error": "binary and host are required"}

        method = "/services/disable"
        url = "%s%s" % (self.zun_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Content-Type": "application/json",
            "OpenStack-API-Version": "container 1.32"
        }

        # 构造请求体
        body = {
            "binary": binary,
            "host": host
        }

        # 添加可选的禁用原因
        if form.get("disabled_reason"):
            body["disabled_reason"] = form["disabled_reason"]

        r = requests.put(url, json=body, headers=headers)
        if r.status_code == 200:
            try:
                service = r.json().get("service", {})
                return {
                    "msg": "ok",
                    "service": {
                        "host": service.get("host"),
                        "binary": service.get("binary"),
                        "disabled": service.get("disabled"),
                        "disabled_reason": service.get("disabled_reason")
                    }
                }
            except Exception as e:
                return {"msg": "error", "error": str(e)}
        else:
            error_info = {
                "msg": "error",
                "status_code": r.status_code,
                "error": r.text
            }
            try:
                error_info["error_detail"] = r.json()
            except:
                error_info["error_detail"] = "无法解析错误详情"
            return error_info

    def openstack_force_down_service(self, form):
        """强制设置Zun服务的状态
        Args:            form: 包含参数的字典:
                 必需参数:
                 - binary: Zun服务的二进制名称
                 - host: 服务所在主机名
                 - forced_down: 是否强制设置为down状态(布尔值)
        Returns:
            成功返回 包含服务信息的字典
            失败返回 包含错误信息的字典
        """
        binary = form.get("binary")
        host = form.get("host")
        forced_down = form.get("forced_down")

        if not binary or not host or forced_down is None:
            return {"msg": "error", "error": "binary, host and forced_down are required"}

        method = "/services/force_down"
        url = "%s%s" % (self.zun_uri, method)
        headers = {
            "X-Auth-Token": self.token,
            "Content-Type": "application/json",
            "OpenStack-API-Version": "container 1.32"
        }

        # 构造请求体
        body = {
            "binary": binary,
            "host": host,
            "forced_down": forced_down
        }

        r = requests.put(url, json=body, headers=headers)
        if r.status_code == 200:
            try:
                service = r.json().get("service", {})
                return {
                    "msg": "ok",
                    "service": {
                        "host": service.get("host"),
                        "binary": service.get("binary"),
                        "forced_down": service.get("forced_down")
                    }
                }
            except Exception as e:
                return {"msg": "error", "error": str(e)}
        else:
            error_info = {
                "msg": "error",
                "status_code": r.status_code,
                "error": r.text
            }
            try:
                error_info["error_detail"] = r.json()
            except:
                error_info["error_detail"] = "无法解析错误详情"
            return error_info

    def openstack_container_details(self, container_ident):
        """
        获取容器的详细信息
        Args:
            container_ident: 容器的UUID或名称
        Returns:
            成功返回包含容器详情的字典
            失败返回包含错误信息的字典
        """
        method = f"/containers/{container_ident}"
        url = f"{self.zun_uri}{method}"
        headers = {
            "X-Auth-Token": self.token,
            "Content-Type": "application/json",
            "OpenStack-API-Version": "container 1.32"
        }

        r = requests.get(url, headers=headers)
        if r.status_code == 200:
            try:
                container = r.json()
                return {
                    "msg": "ok",
                    "container": {
                        "links": container.get("links"),
                        "addresses": container.get("addresses"),
                        "name": container.get("name"),
                        "image": container.get("image"),
                        "labels": container.get("labels"),
                        "image_driver": container.get("image_driver"),
                        "security_groups": container.get("security_groups"),
                        "command": container.get("command"),
                        "cpu": container.get("cpu"),
                        "memory": container.get("memory"),
                        "workdir": container.get("workdir"),
                        "environment": container.get("environment"),
                        "restart_policy": container.get("restart_policy"),
                        "interactive": container.get("interactive"),
                        "tty": container.get("tty"),
                        "uuid": container.get("uuid"),
                        "hostname": container.get("hostname"),
                        "status": container.get("status"),
                        "status_detail": container.get("status_detail"),
                        "host": container.get("host"),
                        "task_state": container.get("task_state"),
                        "status_reason": container.get("status_reason"),
                        "ports": container.get("ports"),
                        "privileged": container.get("privileged"),
                        "healthcheck": container.get("healthcheck"),
                        "user_id": container.get("user_id"),
                        "project_id": container.get("project_id"),
                        "disk": container.get("disk"),
                        "registry_id": container.get("registry_id"),
                        "cpu_policy": container.get("cpu_policy"),
                        "entrypoint": container.get("entrypoint")
                    }
                }
            except Exception as e:
                return {"msg": "error", "error": str(e)}
        elif r.status_code == 401:
            return {"msg": "error", "error": "Unauthorized"}
        elif r.status_code == 403:
            return {"msg": "error", "error": "Forbidden"}
        elif r.status_code == 404:
            return {"msg": "error", "error": "Not Found"}
        else:
            return {"msg": "error", "error": r.text}
