'''
Created on Jan 20, 2022

@author: maojj
'''
import requests
import json
import settings
from api.model.instances import Clusters, Instances, InstanceDetail
from dataclasses import dataclass
from dacite import from_dict
import importlib
from pathlib import Path
from pkgutil import iter_modules
from inspect import isclass
import os


    
class ZunClient(object):
    
    zun_uri = ""
    neutron_uri = ""
    glance_uri = ""
    cinder_uri = ""
    cinderv3_uri = ""

    token = ""
    def __init__(self):
        self.openstatck_get_token()

        package_perfix= "api.zun.mod"
        
        package_dir = os.path.abspath(os.path.join(__file__, '../mod'))
        for module_name in iter_modules([package_dir]):
            
            module_str = '%s.%s' % (package_perfix, module_name[1])
            module = importlib.import_module(module_str)

            for attribute_name in dir(module):
                attribute = getattr(module, attribute_name)
        
                if isclass(attribute) and attribute.__module__ == module_str:              
                    self.__dict__[attribute_name] = attribute
        

    def openstatck_get_token(self):
        toke_url = "%s/auth/tokens" % settings.ZUN_AUTH_URI
        body = """
        {
            "auth": {
                "identity": {
                    "methods": [
                        "password"
                    ],
                    "password": {
                        "user": {
                            "name": "admin",
                            "domain": {
                                "name": "Default"
                            },
                            "password": "%s"
                        }
                    }
                },
                "scope": {
                    "project": {
                        "name": "admin",
                        "domain": {
                            "name": "Default"
                        }
                    }
                }
            }
        }
        """ % settings.ZUN_PASSWORD
        
        r = requests.post(toke_url, body)
        
        data = json.loads(r.text)
        for catalog in data["token"]["catalog"]:
            if catalog["name"] == "zun":
                for endpoint  in catalog["endpoints"]:
                    if endpoint["interface"] == "public":
                        self.zun_uri = endpoint["url"]
            if catalog["name"] == "neutron":
                for endpoint  in catalog["endpoints"]:
                    if endpoint["interface"] == "public":
                        self.neutron_uri = endpoint["url"]
            if catalog["name"] == "glance":
                for endpoint  in catalog["endpoints"]:
                    if endpoint["interface"] == "public":
                        if endpoint["url"][:-1] == "/":
                            self.glance_uri = endpoint["url"]
                        else:
                            self.glance_uri = "%s/" % endpoint["url"]
            if catalog["name"] == "cinder":
                for endpoint  in catalog["endpoints"]:
                    if endpoint["interface"] == "public":
                        self.cinder_uri = endpoint["url"]
            if catalog["name"] == "cinderv3":
                for endpoint  in catalog["endpoints"]:
                    if endpoint["interface"] == "public":
                        self.cinderv3_uri = endpoint["url"]                 
            
                            
        self.token = r.headers.get("X-Subject-Token")


#client = ZunClient()
#test = client.ImageClient.openstack_get_all_image_list(client)
#print(test)

