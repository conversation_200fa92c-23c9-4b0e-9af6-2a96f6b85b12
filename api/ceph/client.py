'''
Created on Mar 1, 2022

@author: maojj
'''
import requests
import json
import settings
#from model.instances import Clusters, Instances, InstanceDetail
from dataclasses import dataclass
from dacite import from_dict
import importlib
from pathlib import Path
from pkgutil import iter_modules
from inspect import isclass
import os


    
class Client(object):
    
    api_url = settings.CEPH_AUTH_URI
    token = ""
    def __init__(self):
        self.ceph_get_token()
        
        package_perfix= "api.ceph.mod"
        
        package_dir = os.path.abspath(os.path.join(__file__, '../mod'))
        for module_name in iter_modules([package_dir]):
            
            module_str = '%s.%s' % (package_perfix, module_name[1])
            module = importlib.import_module(module_str)

            for attribute_name in dir(module):
                attribute = getattr(module, attribute_name)
        
                if isclass(attribute) and attribute.__module__ == module_str:              
                    self.__dict__[attribute_name] = attribute

    def ceph_get_token(self):
        toke_url = "%s/api/auth" % settings.CEPH_AUTH_URI

        headers = self.find_ceph_header()

        body = """
                {
                    "username": "%s",
                    "password": "%s"
                }
        """ % (settings.CEPH_USER, settings.CEPH_PWD)
        
        r = requests.post(toke_url, data=body, headers=headers)
        #print(r.text)
        data = json.loads(r.text)

        self.token = data["token"]

    def find_ceph_header(self):
        if settings.CEPH_VERSION == "v16":
            headers = {
                "Content-Type": "application/json",
                "Accept": "application/vnd.ceph.api.v1.0+json"
            }
        if settings.CEPH_VERSION != "v16":
            headers = {
                "Content-Type": "application/json"
            }
        return headers

    def find_ceph_header_with_token(self, token):
        if settings.CEPH_VERSION == "v16":
            headers = {
                "Content-Type": "application/json",
                "Accept": "application/vnd.ceph.api.v1.0+json",
                "Authorization": "Bearer %s" % token
            }
        if settings.CEPH_VERSION != "v16":
            headers = {
                "Content-Type": "application/json",
                "Authorization": "Bearer %s" % token
            }
        return headers

 