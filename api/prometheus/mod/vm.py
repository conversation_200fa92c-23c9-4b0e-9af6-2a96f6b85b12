import requests
import json
import settings
from api.model.hosts import Host
from dataclasses import dataclass
from dacite import from_dict
from string import Template
import time
from datetime import datetime, timedelta
from api.grafana.client import Client
from ..client import fill_black_list
import numpy as np

POINT_COUNT = 12


class VMClient:
    def host_test(self, id):
        print(self.query_url)
        print(id)

    @fill_black_list
    def get_vm_cpu_usage_rate(self, start, end, query_str):
        if query_str == "":
            query = """(1 - avg(rate(node_cpu_seconds_total{ job=~"openstacklinux|openstackwindows",
                    mode="idle"}[5m])) by (instance)) * 100"""
        else:
            query = """(1 - avg(rate(node_cpu_seconds_total{ job=~"openstacklinux|openstackwindows",instance=~"%s",
                    mode="idle"}[5m])) by (instance)) * 100""" % query_str

        # current_time = time.time()
        # one_hour_ago = current_time - 3600
        #
        # end = "{:.3f}".format(current_time)
        # start = "{:.3f}".format(one_hour_ago)

        res = self.query_vector_by_query_range(query, start, end, round((end - start) / POINT_COUNT))

        if query_str == "":
            query = """(1 - avg(rate(windows_cpu_time_total{ job=~"openstacklinux|openstackwindows",
                    mode="idle"}[5m])) by (instance)) * 100"""
        else:
            query = """(1 - avg(rate(windows_cpu_time_total{ job=~"openstacklinux|openstackwindows",instance=~"%s",
                    mode="idle"}[5m])) by (instance)) * 100""" % query_str


        res1 = self.query_vector_by_query_range(query, start, end, round((end - start) / POINT_COUNT))
        

        return self.convert_response_json_two_list(res, res1)

    @fill_black_list
    def get_vm_cpu_usage_rate_by_ins_id(self, start, end, ins_id):

        query = """(1 - avg(rate(node_cpu_seconds_total{instance_id="%s",
                    mode="idle"}[5m])) by (instance)) * 100""" % ins_id
        res = self.query_vector_by_query_range(query, start, end, round((end - start) / POINT_COUNT))

        query = """(1 - avg(rate(windows_cpu_time_total{instance_id="%s",
                    mode="idle"}[5m])) by (instance)) * 100""" % ins_id
        res1 = self.query_vector_by_query_range(query, start, end, round((end - start) / POINT_COUNT))

        return self.convert_response_json_two_list(res, res1)

    @fill_black_list
    def get_vm_mem_usage_rate_by_ins_id(self, start, end, ins_id):

        query = """(1 - (avg by (instance)(node_memory_MemAvailable_bytes{instance_id="%s"}) /
         avg by (instance)(node_memory_MemTotal_bytes{instance_id="%s"}))) * 100""" % (ins_id, ins_id)
        res = self.query_vector_by_query_range(query, start, end, round((end - start) / POINT_COUNT))

        query = """(1 - (avg by (instance)(windows_os_physical_memory_free_bytes{instance_id="%s"}) /
         avg by (instance)(windows_cs_physical_memory_bytes{instance_id="%s"}))) * 100""" % (ins_id, ins_id)
        res1 = self.query_vector_by_query_range(query, start, end, round((end - start) / POINT_COUNT))

        return self.convert_response_json_two_list(res, res1)

    @fill_black_list
    def get_vm_mem_usage_rate(self, start, end, query_str):
        if query_str == "":
            query = """(1 - (avg by (instance)(node_memory_MemAvailable_bytes{job=~"openstacklinux|openstackwindows"}) / 
                        avg by (instance)(node_memory_MemTotal_bytes{job=~"openstacklinux|openstackwindows"}))) * 100"""
        else:
            query = """(1 - (avg by (instance)(node_memory_MemAvailable_bytes{job=~"openstacklinux|openstackwindows",
            instance=~"%s"}) / avg by (instance)(node_memory_MemTotal_bytes{job=~"openstacklinux|openstackwindows",
            instance=~"%s"}))) * 100""" % (query_str, query_str)

        # current_time = time.time()
        # one_hour_ago = current_time - 3600
        #
        # end = "{:.3f}".format(current_time)
        # start = "{:.3f}".format(one_hour_ago)

        res = self.query_vector_by_query_range(query, start, end, round((end - start) / POINT_COUNT))


        if query_str == "":
            query = """(1 - (avg by (instance)(windows_os_physical_memory_free_bytes{job=~"openstacklinux|openstackwindows"}) / 
                        avg by (instance)(windows_cs_physical_memory_bytes{job=~"openstacklinux|openstackwindows"}))) * 100"""
        else:
            query = """(1 - (avg by (instance)(windows_os_physical_memory_free_bytes{job=~"openstacklinux|openstackwindows",
            instance=~"%s"}) / avg by (instance)(windows_cs_physical_memory_bytes{job=~"openstacklinux|openstackwindows",
            instance=~"%s"}))) * 100""" % (query_str, query_str)

        # current_time = time.time()
        # one_hour_ago = current_time - 3600
        #
        # end = "{:.3f}".format(current_time)
        # start = "{:.3f}".format(one_hour_ago)

        res1 = self.query_vector_by_query_range(query, start, end, round((end - start) / POINT_COUNT))

        return self.convert_response_json_two_list(res, res1)

    @fill_black_list
    def get_vm_io_usage_rate(self, start, end, query_str):


        if query_str == "":
            query = """( (sum by (instance)(avg(node_filesystem_size_bytes{job=~"openstacklinux|openstackwindows", 
            fstype=~"xfs|ext.*"}) by (instance, device) - avg(node_filesystem_free_bytes{ 
            job=~"openstacklinux|openstackwindows", fstype=~"xfs|ext.*"}) by (instance, device))) * 100 ) / (sum by ( 
            instance)(avg(node_filesystem_size_bytes{job=~"openstacklinux|openstackwindows", fstype=~"xfs|ext.*"}) by 
            ( instance)))"""
        else:
            query = """( (sum by (instance)(avg(node_filesystem_size_bytes{job=~"openstacklinux|openstackwindows", 
            instance=~"%s",fstype=~"xfs|ext.*"}) by (instance, device) - avg(node_filesystem_free_bytes{ 
            job=~"openstacklinux|openstackwindows",instance=~"%s", fstype=~"xfs|ext.*"}) by (instance, device))) * 
            100 ) / (sum by ( instance)(avg(node_filesystem_size_bytes{job=~"openstacklinux|openstackwindows",
            instance=~"%s", fstype=~"xfs|ext.*"}) by ( instance)))""" % (query_str, query_str, query_str)
        # current_time = time.time()
        # one_hour_ago = current_time - 3600
        #
        # end = "{:.3f}".format(current_time)
        # start = "{:.3f}".format(one_hour_ago)

        res = self.query_vector_by_query_range(query, start, end, round((end - start) / POINT_COUNT))

        if query_str == "":
            query = """( (sum by (instance)(avg(windows_logical_disk_size_bytes{job=~"openstacklinux|openstackwindows"
            }) by (instance, device) - avg(windows_logical_disk_free_bytes{ 
            job=~"openstacklinux|openstackwindows"}) by (instance, device))) * 100 ) / (sum by ( 
            instance)(avg(windows_logical_disk_size_bytes{job=~"openstacklinux|openstackwindows"}) by 
            ( instance)))"""
        else:
            query = """( (sum by (instance)(avg(windows_logical_disk_size_bytes{job=~"openstacklinux|openstackwindows", 
            instance=~"%s"}) by (instance, device) - avg(windows_logical_disk_free_bytes{ 
            job=~"openstacklinux|openstackwindows",instance=~"%s"}) by (instance, device))) * 
            100 ) / (sum by ( instance)(avg(windows_logical_disk_size_bytes{job=~"openstacklinux|openstackwindows",
            instance=~"%s"}) by ( instance)))""" % (query_str, query_str, query_str)
        # current_time = time.time()
        # one_hour_ago = current_time - 3600
        #
        # end = "{:.3f}".format(current_time)
        # start = "{:.3f}".format(one_hour_ago)

        res1 = self.query_vector_by_query_range(query, start, end, round((end - start) / POINT_COUNT))

        return self.convert_response_json_two_list(res, res1)

    @fill_black_list
    def get_vm_network(self, start, end, query_str):
        if query_str == "":
            up_query = """sum by (instance)(increase(node_network_transmit_bytes_total{
            job=~"openstacklinux|openstackwindows",device!~"lo|docker.*"}[60m]))"""
        else:
            up_query = """sum by (instance)(increase(node_network_transmit_bytes_total{
            job=~"openstacklinux|openstackwindows",instance=~"%s",device!~"lo|docker.*"}[60m]))""" % query_str

        up_res = self.query_vector_by_query_range(up_query, start, end, round((end - start) / POINT_COUNT))


        if query_str == "":
            up_query = """sum by (instance)(increase(windows_net_bytes_sent_total{
            job=~"openstacklinux|openstackwindows",device!~"lo|docker.*"}[5m]))"""
        else:
            up_query = """sum by (instance)(increase(windows_net_bytes_sent_total{
            job=~"openstacklinux|openstackwindows",instance=~"%s",device!~"lo|docker.*"}[5m]))""" % query_str

        up_res1 = self.query_vector_by_query_range(up_query, start, end, round((end - start) / POINT_COUNT))


        if query_str == "":
            down_query = """sum by (instance)(increase(node_network_receive_bytes_total{
            job=~"openstacklinux|openstackwindows",device!~"lo|docker.*"}[60m]))"""
        else:
            down_query = """sum by (instance)(increase(node_network_receive_bytes_total{
            job=~"openstacklinux|openstackwindows",instance=~"%s",device!~"lo|docker.*"}[60m]))""" % query_str

        down_res = self.query_vector_by_query_range(down_query, start, end, round((end - start) / POINT_COUNT))


        if query_str == "":
            down_query = """sum by (instance)(increase(windows_net_bytes_received_total{
            job=~"openstacklinux|openstackwindows",nic!~"isatap.*|VPN.*"}[5m]))"""
        else:
            down_query = """sum by (instance)(increase(windows_net_bytes_received_total{
            job=~"openstacklinux|openstackwindows",instance=~"%s",nic!~"isatap.*|VPN.*"}[5m]))""" % query_str

        down_res1 = self.query_vector_by_query_range(down_query, start, end, round((end - start) / POINT_COUNT))



        return self.convert_network_response_json(up_res, up_res1, down_res, down_res1)

    def get_vm_network_up(self, start, end, query_str):
        if query_str == "":
            query = """sum by (instance)(increase(node_network_transmit_bytes_total{
            job=~"openstacklinux|openstackwindows",device!~"lo|docker.*"}[60m]))"""
        else:
            query = """sum by (instance)(increase(node_network_transmit_bytes_total{
            job=~"openstacklinux|openstackwindows",instance=~"%s",device!~"lo|docker.*"}[60m]))""" % query_str

        # current_time = time.time()
        # one_hour_ago = current_time - 3600
        #
        # end = "{:.3f}".format(current_time)
        # start = "{:.3f}".format(one_hour_ago)

        res = self.query_vector_by_query_range(query, start, end, round((end - start) / POINT_COUNT))

        return self.convert_response_json(res)

    def get_vm_network_down(self, start, end, query_str):
        if query_str == "":
            query = """sum by (instance)(increase(node_network_receive_bytes_total{
            job=~"openstacklinux|openstackwindows",device!~"lo|docker.*"}[60m]))"""
        else:
            query = """sum by (instance)(increase(node_network_receive_bytes_total{
            job=~"openstacklinux|openstackwindows",instance=~"%s",device!~"lo|docker.*"}[60m]))""" % query_str

        # current_time = time.time()
        # one_hour_ago = current_time - 3600
        #
        # end = "{:.3f}".format(current_time)
        # start = "{:.3f}".format(one_hour_ago)

        res = self.query_vector_by_query_range(query, start, end, round((end - start) / POINT_COUNT))

        return self.convert_response_json(res)


