from datetime import time

import numpy as np
import requests
import json
import settings
from api.model.hosts import Host
from dataclasses import dataclass
from string import Template
from ..client import fill_black_list


class StorageClient:

    # ALERTS counts
    def get_storage_alert_counts(self, start, end):
        query = """count(ALERTS{job='ceph'})"""
        res = self.query_vector_by_query_range(query, start, end, 4)
        return res

    def get_storage_total(self, start, end):
        # 获取当前时间戳
        query = """sum without (instance) (ceph_cluster_total_bytes{cluster=""})"""
        res = self.query_vector_by_query_range(query, start, end, 4)
        return res

    def get_storage_used(self, start, end):
        query = """sum without (instance) (ceph_cluster_total_used_bytes{cluster=""})"""
        res = self.query_vector_by_query_range(query, start, end, 4)
        return res

    def get_storage_residue(self, start, end):
        query = """sum without (instance) (ceph_cluster_total_bytes{cluster=""}-ceph_cluster_total_used_bytes{cluster=""})"""
        res = self.query_vector_by_query_range(query, start, end, 4)
        return res

    def get_storage_status(self, start, end):
        query = """sum without (instance) (ceph_health_status{cluster=""})"""
        res = self.query_vector_by_query_range(query, start, end, 4)
        return res

    def get_storage_osd_out(self, start, end):
        query = """count without (instance, ceph_daemon) (ceph_osd_up{cluster=""}) - count without (instance, ceph_daemon) (ceph_osd_in{cluster=""})"""
        res = self.query_vector_by_query_range(query, start, end, 4)
        return res

    def get_storage_osd_in(self, start, end):
        query = """sum without (instance, ceph_daemon) (ceph_osd_in{cluster=""})"""
        res = self.query_vector_by_query_range(query, start, end, 4)
        return res

    def get_storage_osd_down(self, start, end):
        query = """count(ceph_osd_up{cluster=""} == 0.0) OR vector(0)"""
        res = self.query_vector_by_query_range(query, start, end, 4)
        return res

    def get_storage_osd_up(self, start, end):
        query = """sum without (instance, ceph_daemon) (ceph_osd_up{cluster=""})"""
        res = self.query_vector_by_query_range(query, start, end, 4)
        return res

    def get_storage_iops_w(self, start, end):
        query = """sum without (instance, ceph_daemon) (irate(ceph_osd_op_w{cluster=""}[5m]))"""
        res = self.query_vector_by_query_range(query, start, end, round((end-start)/12))
        return res

    def get_storage_iops_r(self, start, end):
        query = """sum without (instance, ceph_daemon) (irate(ceph_osd_op_r{cluster=""}[5m]))"""
        res = self.query_vector_by_query_range(query, start, end, round((end-start)/12))
        return res

    def get_storage_throughput_w(self, start, end):
        query = """sum without (instance, ceph_daemon) (irate(ceph_osd_op_w_in_bytes{job="ceph"}[5m]))"""
        res = self.query_vector_by_query_range(query, start, end, round((end-start)/12))
        return res

    def get_storage_throughput_r(self, start, end):
        query = """sum without (instance, ceph_daemon) (irate(ceph_osd_op_r_out_bytes{job="ceph"}[5m]))"""
        res = self.query_vector_by_query_range(query, start, end, round((end-start)/12))
        return res

    def get_storage_capacity(self, start, end):
        # query = """(sum without (instance) (ceph_cluster_total_used_bytes{cluster=""}) / sum without (instance) (ceph_cluster_total_bytes{cluster=""})) * 100"""
        query = """avg_over_time(
  (
    sum without (instance) (ceph_cluster_total_used_bytes{cluster=""})
    / sum without (instance) (ceph_cluster_total_bytes{cluster=""})
  )[1h:]
) * 100
"""
        res = self.query_vector_by_query_range(query, start, end,  round((end-start)/12))
        return res

    def get_storage_net_up(self, start, end):
        query = """sum(rate(node_network_transmit_bytes_total{job='node-exporter',device=~"(eno.*|ens.*|eth.*|enp.*)"}[2m])*8) by(instance)"""
        res = self.query_vector_by_query_range(query, start, end, round((end-start)/12))
        return res

    def get_storage_net_down(self, start, end):
        query = """sum(rate(node_network_receive_bytes_total{job='node-exporter',device=~"(eno.*|ens.*|eth.*|enp.*)"}[2m])*8) by(instance)"""
        res = self.query_vector_by_query_range(query, start, end, round((end-start)/12))
        return res

    def get_storage_node_io_r(self, start, end):
        query = """sum(rate(node_disk_reads_completed_total{job=~"node-exporter",device=~'sd.*'}[2m])) by(instance)"""
        res = self.query_vector_by_query_range(query, start, end, round((end-start)/12))
        return res

    def get_storage_node_io_w(self, start, end):
        query = """sum(rate(node_disk_writes_completed_total{job=~"node-exporter",device=~'sd.*'}[2m])) by(instance)"""
        res = self.query_vector_by_query_range(query, start, end, round((end-start)/12))
        return res

    def get_storage_mem(self, start, end):
        query = """sum without (instance) (node_memory_Active_anon_bytes{cluster='', job="openstack_hypervisors"})"""
        res = self.query_vector_by_query_range(query, start, end, round((end-start)/12))
        return res

    def get_storage_cpu(self, start, end):
        query = """avg by (instance) (irate(node_cpu_seconds_total{cluster='',mode!="idle"}[10s])) * 100"""
        res = self.query_vector_by_query_range(query, start, end, round((end-start)/12))
        return res

    def query_vector_convert(self, input_data, title):
        output_data={}
        # 提取时间和数据
        if len(input_data['data']['result']) > 0:
            values = np.array(input_data['data']['result'][0]['values'])
            time = values[:, 0].tolist()
            data = values[:, 1].astype(float).tolist()

        # 创建输出数据结构
            output_data = {
                "time": time,
                "data": [
                    {
                        "title": title,
                        "list": data
                    }
                ]
            }
        else:
            output_data = {
                "time": [],
                "data": []
            }
        return output_data

    @fill_black_list
    def fill_black_query_vector_convert(self, start, end, input_data, title):
        output_data={}
        # 提取时间和数据
        if len(input_data['data']['result']) > 0:
            values = np.array(input_data['data']['result'][0]['values'])
            time = values[:, 0].tolist()
            data = values[:, 1].astype(float).tolist()

        # 创建输出数据结构
            output_data = {
                "time": time,
                "data": [
                    {
                        "title": title,
                        "list": data
                    }
                ]
            }
        else:
            output_data = {
                "time": [],
                "data": []
            }
        return output_data

    def query_vector_convert_negative(self, input_data, title):
        output_data={}
        # 提取时间和数据
        if len(input_data['data']['result']) > 0:
            values = np.array(input_data['data']['result'][0]['values'])
            time = values[:, 0].tolist()
            data = (-values[:, 1].astype(float)).tolist()

        # 创建输出数据结构
            output_data = {
                "time": time,
                "data": [
                    {
                        "title": title,
                        "list": data
                    }
                ]
            }
        else:
            output_data = {
                "time": [],
                "data": []
            }
        return output_data

    def query_vector_montage(self, input_data1, input_data2):
        # 选择更长的时间
        merged_time = input_data1["time"] if len(input_data1["time"]) > len(input_data2["time"]) else input_data2["time"]

        # 拼接数据
        merged_data = input_data1["data"] + input_data2["data"]

        # 创建合并的数据结构
        merged = {
            "time": merged_time,
            "data": merged_data
        }

        return merged

    def query_vector_montage_mem(self, input_data):
        # 初始化输出数据结构
        output_data = {
            "time": [],
            "data": []
        }

        # 遍历输入数据中的每个结果
        for result in input_data["data"]["result"]:
            # 提取值并将它们转换为NumPy数组
            values = np.array(result["values"])

            # 提取时间和数据
            time = values[:, 0].tolist()
            data = values[:, 1].astype(float).astype(int).tolist()

            # 如果新的时间更长，则更新输出数据中的时间
            if len(time) > len(output_data["time"]):
                output_data["time"] = time

            # 将新的数据添加到输出数据中
            output_data["data"].append({
                "title": result["metric"]["hypervisor_hostname"],
                "list": data 
            })

        return output_data
    
    # def get_host_network_usage_rate(self):
    #     query = """avg(1 - avg(rate(node_cpu_seconds_total{mode="idle"}[30m])) by (instance)) * 100"""
    #
    #     current_time = time.time()
    #     one_hour_ago = current_time - 3600
    #
    #     end = "{:.3f}".format(current_time)
    #     start = "{:.3f}".format(one_hour_ago)
    #
    #     res = self.query_vector_by_query_range(query, start, end, 15)
    #
    #     return res

        