import numpy as np

POINT_COUNT = 12

class PhysicalClient:
    def ipmi_chassis_cooling_fault_state(self):
        """
        当前冷却/风扇故障状态
        """
        query = """ipmi_chassis_cooling_fault_state"""
        res = self.query_vector_by_query(query)
        return self.PhysicalClient.convert_host_response_json(res)

    def ipmi_chassis_power_state(self):
        """
        当前电源状态
        """
        query = """ipmi_chassis_power_state"""
        res = self.query_vector_by_query(query)
        return self.PhysicalClient.convert_host_response_json(res)

    def ipmi_fan_speed_rpm(self, start, end):
        """
        风扇速度
        """
        query = """ipmi_fan_speed_rpm"""
        res = self.query_vector_by_query_range(query, start, end, round((end-start)/POINT_COUNT))
        return self.PhysicalClient.convert_host_response_json_v2(res)

    def ipmi_fan_speed_state(self):
        """
        风扇速度传感器的状态
        """
        query = """ipmi_fan_speed_state"""
        res = self.query_vector_by_query(query)
        return self.PhysicalClient.convert_host_response_json(res)

    def ipmi_temperature_celsius(self, start, end):
        """
        摄氏度温度读数
        """
        query = """ipmi_temperature_celsius"""
        res = self.query_vector_by_query_range(query, start, end, round((end-start)/POINT_COUNT))
        return self.PhysicalClient.convert_host_response_json_v2(res)

    def ipmi_temperature_state(self):
        """
        温度传感器的状态
        """
        query = """ipmi_temperature_state"""
        res = self.query_vector_by_query(query)
        return self.PhysicalClient.convert_host_response_json(res)

    def ipmi_voltage_state(self):
        """
        电压传感器的状态
        """
        query = """ipmi_voltage_state"""
        res = self.query_vector_by_query(query)
        return self.PhysicalClient.convert_host_response_json(res)

    def ipmi_voltage_volts(self, start, end):
        """
        电压读数
        """
        query = """ipmi_voltage_volts"""
        res = self.query_vector_by_query_range(query, start, end, round((end-start)/POINT_COUNT))
        return self.PhysicalClient.convert_host_response_json_v2(res)

    def ipmi_fan_speed_rpm_current(self):
        """
        风扇速度/风扇速度传感器的状态
        """
        query_rpm   = """ipmi_fan_speed_rpm"""
        query_state = """ipmi_fan_speed_state"""
        rpms        = self.query_vector_by_query(query_rpm)
        states      = self.query_vector_by_query(query_state)

        res    = []
        titles = []

        for data in list(zip(rpms, states)):
            title = data[0]["metric"]["instance"].split(":")[0]
            name  = data[0]["metric"]["name"]
            value = data[0]["value"][1]
            state = data[1]["value"][1]
            tmp   = {"name": name, "volue": value, "state": state}

            if title in titles:
                res[titles.index(title)]["list"].append(tmp)
            else:
                titles.append(title)
                res.append({"title": title, "list": [tmp]})

        return res

    def ipmi_temperature_celsius_current(self):
        """
        摄氏度温度读数
        """
        query = """ipmi_temperature_celsius"""
        res = self.query_vector_by_query(query)
        return self.PhysicalClient.convert_host_current_response_json(res)

    def ipmi_voltage_volts_current(self):
        """
        电压读数
        """
        query = """ipmi_voltage_volts"""
        res = self.query_vector_by_query(query)
        return self.PhysicalClient.convert_host_current_response_json(res)

    def convert_host_current_response_json(params):
        res    = []
        titles = []
        for e in params:
            metric = e["metric"]
            value  = e["value"][1]
            title  = metric["instance"].split(":")[0]
            name   = metric["name"]
            tmp    = {"name": name, "volue": value}
            if title in titles:
                res[titles.index(title)]["list"].append(tmp)
            else:
                titles.append(title)
                res.append({"title": title, "list": [tmp]})

        return {"data": res}

    def convert_host_response_json(params):
        res = []
        for e in params:
            metric = e["metric"]
            value  = e["value"][1]
            t      = dict()
            t["name"] = metric["__name__"]
            t["instance"] =  metric["instance"]
            t["state"] = value
            if "name" in metric:
                t["device"] = metric["name"]
            res.append(t)
        return res

    def convert_host_response_json_v2(input_data):

        output_data = {}
        if len(input_data['data']['result']) == 0:
            return {"time": [], "data": []}
        # 提取时间和数据
        time_values = np.array(input_data['data']['result'][0]['values'])
        output_data["time"] = time_values[:, 0].tolist()

        data = []
        for result in input_data['data']['result']:
            values = np.array(result["values"])
            title = result["metric"]
            tmp = {
                "title": title,
                "list": np.round(values[:, 1].astype(float), 2).tolist()
                }
            data.append(tmp)

        # 创建输出数据结构
        output_data["data"] = data
        return output_data
