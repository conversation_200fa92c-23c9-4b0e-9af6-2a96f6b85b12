import requests
import json
import settings
from api.model.hosts import Host
from dataclasses import dataclass
from dacite import from_dict
from string import Template
import time
from datetime import datetime, timedelta
from api.grafana.client import Client
import numpy as np

POINT_COUNT = 12

class DockerClient:
    def host_test(self, id):
        print(self.query_url)
        print(id)

    def get_ceph_osd_mem_usage_rate(self):
        query = """avg_over_time(container_memory_usage_bytes{name=~"ceph-osd.*"}[1h]) / 1024 / 1024 / 1024"""
        res = self.query_vector_by_query(query)
        # return self.convert_host_response_json(res)


        def transform_data(input_data):
            transformed_data = []
            
            for entry in input_data:
                metric = entry["metric"]
                value = entry["value"]
                
                transformed_entry = {
                    "ip": metric.get("instance", "").split(":")[0],
                    "name": metric.get("name", ""),
                    "value": value[1],
                    "time": value[0]
                }
                
                transformed_data.append(transformed_entry)
            
            return transformed_data
        
        return transform_data(res)




    
                   
        