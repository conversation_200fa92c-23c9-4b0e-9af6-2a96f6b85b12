'''
Created on Mar 1, 2022

@author: maojj
'''
from datetime import datetime

import requests
# import sys
import json
from dataclasses import dataclass
from dacite import from_dict
import importlib
from pathlib import Path
from pkgutil import iter_modules
from inspect import isclass
import os
from prometheus_client.parser import text_string_to_metric_families
import copy
import time
import urllib
import numpy as np
# Add the project directory to sys.path
# project_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../'))
# sys.path.append(project_path)
import settings

# # Now you can import settings
# try:
#     import settings
# except ImportError as e:
#     print(f"ImportError: {e}")
#     sys.exit(1)

# # Print the attributes to verify
# print(f"Project path: {project_path}")
# print(f"Settings module path: {settings.__file__}")

# # Check if the required attributes are present
# required_attributes = ['RROMETHEUS_ALERT_URI', 'RROMETHEUS_QUERY_URI']
# for attr in required_attributes:
#     if not hasattr(settings, attr):
#         raise AttributeError(f"Required attribute {attr} is missing in settings.py")

class Client(object):
    
    #metrics_url = settings.METRICS_URI
    alert_url = settings.RROMETHEUS_ALERT_URI
    query_url = settings.RROMETHEUS_QUERY_URI
    query_range_url = settings.RROMETHEUS_QUERY_RANGE_URI
    token = ""
    data = {}
    
    def __init__(self):
        #self.init_metrics_data()

        package_perfix= "api.prometheus.mod"
        
        package_dir = os.path.abspath(os.path.join(__file__, '../mod'))
        for module_name in iter_modules([package_dir]):
            
            module_str = '%s.%s' % (package_perfix, module_name[1])
            module = importlib.import_module(module_str)

            for attribute_name in dir(module):
                attribute = getattr(module, attribute_name)
        
                if isclass(attribute) and attribute.__module__ == module_str:              
                    self.__dict__[attribute_name] = attribute
    
    def init_metrics_data(self):

        r = requests.get(self.metrics_url)
        
        for family in text_string_to_metric_families(r.text):
            for sample in family.samples:
                
                if sample[0] == "ceph_pool_metadata":
                    row = self.data.get("pool_id_%s" % sample[1].get("pool_id"), {})
                    row["id"] = sample[1].get("pool_id")
                    row["name"] = sample[1].get("name")
                    self.data["pool_id_%s" % sample[1].get("pool_id")] = row

                if sample[0] == "ceph_pool_max_avail":
                    row = self.data.get("pool_id_%s" % sample[1].get("pool_id"), {})
                    row["ceph_pool_max_avail"] = sample[2]
                    self.data["pool_id_%s" % sample[1].get("pool_id")] = row
                
                if sample[0] == "ceph_pool_stored":
                    row = self.data.get("pool_id_%s" % sample[1].get("pool_id"), {})
                    row["ceph_pool_stored"] = sample[2]
                    self.data["pool_id_%s" % sample[1].get("pool_id")] = row

        
    def get_ceph_pool_list(self):
        self.init_metrics_data()
        res = []
        for k, v in self.data.items():
            if v.get("name") in ["cinder.volumes"]:
                res.append(v)
        return res
    
    def get_alert_list(self):
        
        r = requests.get(self.alert_url)
        data = json.loads(r.text)
        res = []
        for o in data["data"]["alerts"]:
            print(o)
            res.append(o)
        return res
    
    
    def query_list(self):
        r = requests.get(self.alert_url)
        data = json.loads(r.text)
        res = []
        for o in data["data"]["result"]:
            print(o)
            res.append(o)
        return res
    
    def query_vector_by_query_parm(self, parm):
        rate_parm = "rate(%s[5m])" % parm
        url = "%s?query=%s" % (self.query_url, rate_parm)
        r = requests.get(url)
        data = json.loads(r.text)
        res = []
        for o in data["data"]["result"]:
            metric = o.get("metric", {})
            device_name = metric.get("device")
            
            first_time = o["value"][0]
            count = int(o["value"][0])
            if not (count == "0"):
                res.append(o)
            else:
                if device_name[:4] != "vnet":
                    res.append(o)
                    
        return res

    def query_vector_by_query(self, parm):

        p = urllib.parse.quote(parm)

        url = "%s?query=%s" % (self.query_url, p)

        r = requests.get(url)
        data = json.loads(r.text)
        res = []
        for o in data["data"]["result"]:
            res.append(o)
        return res

    def query_vector_by_target(self, parm):

        p = urllib.parse.quote(parm)
        parts = self.query_url.rsplit('/', 1)

        # 将最后一部分替换为 'targets'
        new_url = parts[0] + '/targets'
        url = "%s?state=%s" % (new_url, p)

        r = requests.get(url)
        data = json.loads(r.text)
        res = []
        # for o in data.get["data"]["activeTargets"]["discoveredLabels"]:
        #     res.append(o)

        # 遍历每个activeTargets中的字典
        for target in data["data"]["activeTargets"]:
            # 获取discoveredLabels
            if target["labels"]["job"] != 'openstacklinux' and target["labels"]["job"] != 'openstackwindows':
                continue
            discovered_labels = target
            # 将discoveredLabels追加到res中
            res.append(discovered_labels)
        return res



    def query_vector_by_query_range(self, query, start, end, step):
        
        parm = "query=%s&start=%s&end=%s&step=%s" % (query, start, end, step)

        # p = urllib.parse.quote(parm)
        # print(p)
        url = "%s?%s" % (self.query_range_url, parm)

        r = requests.get(url)
        data = json.loads(r.text)

        res = data
        return res    

    def convert_response_json(self, input_data):

        output_data = {}
        # 提取时间和数据
        if len(input_data['data']['result']) == 0:
            return {"time": [], "data": []}
        time_values = np.array(input_data['data']['result'][0]['values'])
        output_data["time"] = time_values[:, 0].tolist()
        
        
        data = []
        for result in input_data['data']['result']:
            values = np.array(result["values"])
            title = result["metric"]["instance"]
            #print(values)
            tmp = {
                "title": title,
                "list": np.round(values[:, 1].astype(float), 2).tolist()
                }
            data.append(tmp)

        # 创建输出数据结构
        output_data["data"] = data
        return output_data

    def convert_response_json_two_list(self, input_data, input_data1):

        output_data = {}
        data = []
        # 提取时间和数据
        if len(input_data['data']['result']) > 0:
            time_values = np.array(input_data['data']['result'][0]['values'])
            output_data["time"] = time_values[:, 0].tolist()

            for result in input_data['data']['result']:
                values = np.array(result["values"])
                title = result["metric"]["instance"]
                #print(values)
                tmp = {
                    "title": title,
                    "list": np.round(values[:, 1].astype(float), 2).tolist()
                    }
                data.append(tmp)

        if not output_data.get("time") and len(input_data1['data']['result']) > 0:
            time_values = np.array(input_data1['data']['result'][0]['values'])
            output_data["time"] = time_values[:, 0].tolist()
        for result in input_data1['data']['result']:
            values = np.array(result["values"])
            title = result["metric"]["instance"]
            #print(values)
            tmp = {
                "title": title,
                "list": np.round(values[:, 1].astype(float), 2).tolist()
                }
            data.append(tmp)


        # 创建输出数据结构
        output_data["data"] = data
        return output_data

    def convert_host_response_json(self, input_data):

        output_data = {}
        if len(input_data['data']['result']) == 0:
            return {"time": [], "data": []}
        # 提取时间和数据
        time_values = np.array(input_data['data']['result'][0]['values'])
        output_data["time"] = time_values[:, 0].tolist()
        
        
        data = []
        for result in input_data['data']['result']:
            values = np.array(result["values"])
            title = result["metric"]["hypervisor_hostname"]
            #print(values)
            tmp = {
                "title": title,
                "list": np.round(values[:, 1].astype(float), 2).tolist()
                }
            data.append(tmp)

        # 创建输出数据结构
        output_data["data"] = data
        return output_data
        
    def convert_network_response_json(self, up_input_data, up_input_data1,  down_input_data, down_input_data1):
        
        output_data = {}

        if len(down_input_data['data']['result']) == 0 and len(down_input_data1['data']['result']) == 0:
            return {"time": [], "data": []}
        
        # 提取时间和数据 这个数据有可能是windows或linux没有数据, 所以要判断
        if len(up_input_data['data']['result']) > 0:
            time_values = np.array(up_input_data['data']['result'][0]['values'])
            output_data["time"] = time_values[:, 0].tolist()
        else:
            time_values = np.array(up_input_data1['data']['result'][0]['values'])
            output_data["time"] = time_values[:, 0].tolist()
        
        
        data = []
        for result in up_input_data['data']['result']:
            values = np.array(result["values"])
            title = result["metric"]["instance"]
            #print(values)
            tmp = {
                "title": "%s上行" % title,
                "list": np.round((-values[:, 1].astype(float)), 2).tolist()
                }
            data.append(tmp)

        for result in up_input_data1['data']['result']:
            values = np.array(result["values"])
            title = result["metric"]["instance"]
            #print(values)
            tmp = {
                "title": "%s上行" % title,
                "list": np.round((-values[:, 1].astype(float)), 2).tolist()
                }
            data.append(tmp)

        for result in down_input_data['data']['result']:
            values = np.array(result["values"])
            title = result["metric"]["instance"]
            #print(values)
            tmp = {
                "title": "%s下行" % title,
                "list": np.round(values[:, 1].astype(float), 2).tolist()
                }
            data.append(tmp)

        for result in down_input_data1['data']['result']:
            values = np.array(result["values"])
            title = result["metric"]["instance"]
            #print(values)
            tmp = {
                "title": "%s下行" % title,
                "list": np.round(values[:, 1].astype(float), 2).tolist()
                }
            data.append(tmp)
            
        # 创建输出数据结构
        output_data["data"] = data
        return output_data


    def convert_host_network_response_json(self, up_input_data, down_input_data):
        
        output_data = {}

        if len(down_input_data['data']['result']) == 0:
            return {"time": [], "data": []}
        # 提取时间和数据
        time_values = np.array(up_input_data['data']['result'][0]['values'])
        output_data["time"] = time_values[:, 0].tolist()
        
        
        data = []
        for result in up_input_data['data']['result']:
            values = np.array(result["values"])
            title = result["metric"]["hypervisor_hostname"]
            #print(values)
            tmp = {
                "title": "%s上行" % title,
                "list": np.round((-values[:, 1].astype(float)), 2).tolist()
                }
            data.append(tmp)

        for result in down_input_data['data']['result']:
            values = np.array(result["values"])
            title = result["metric"]["hypervisor_hostname"]
            #print(values)
            tmp = {
                "title": "%s下行" % title,
                "list": np.round(values[:, 1].astype(float), 2).tolist()
                }
            data.append(tmp)
            
        # 创建输出数据结构
        output_data["data"] = data
        return output_data            

    def string_to_timestamp(self, date_string):
        try:
            # 将字符串转换为 datetime 对象
            date_time_obj = datetime.strptime(date_string, '%Y-%m-%d %H:%M')
            # 将 datetime 对象转换为时间戳并返回
            return date_time_obj.timestamp()
        except ValueError:
            print("输入的日期时间格式不正确！")
            return None


def fill_black_list(func):
    from .mod.vm import POINT_COUNT

    def fill(*args, **kwargs):
        source = func(*args, **kwargs)
        start, end = args[1], args[2]
        print(start, end)
        if source.get('time', None) in [None, []]:
            return source

        left_step = round((end - start) / POINT_COUNT)
        left_count = len(source['time']) - POINT_COUNT
        if left_count < 0:
            tmp_time = np.arange(start, end, left_step, dtype=float)
            source['time'] = np.append(tmp_time[:abs(left_count)], source['time']).tolist()

        for data in source['data']:
            arr_none = np.full((POINT_COUNT), None)
            left_count = len(data["list"]) - POINT_COUNT
            if left_count < 0 and data["list"] != []:
                data["list"] = np.append(arr_none[:abs(left_count)], data["list"]).tolist()

        return source

    return fill


"""
current_time = time.time()
one_hour_ago = current_time - 3600

end = "{:.3f}".format(current_time)
start = "{:.3f}".format(one_hour_ago)

end = current_time
start = one_hour_ago
step = 12
print(end)
print(start)
        
client = Client()
res = client.HostClient.get_host_cpu_usage_rate(client, start, end)
import json
print(json.dumps(res, indent=4))

"""
