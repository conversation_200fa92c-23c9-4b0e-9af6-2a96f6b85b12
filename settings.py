
ETCD_HOST = 'localhost'
LOG_PATH = "/tmp/theweb.log"
#AUTH_URI = "http://keystone.openstack.svc.cluster.local/v3"
AUTH_URI = "http://************:5000/v3"
AUTH_TYPE = "password"
PROJECT_DOMAIN_ID = "default"
USER_DOMAIN_ID = "default"
PROJECT_NAME = "admin"
USERNAME = "admin"
#PASSWORD = "password"
PASSWORD = "thecloud2015.1"


REDIS_HOSTNAME = "redis"
REDIS_PWD = ""
REDIS_PORT = "6379"

DRIVER = "mysql"
DB_USERNAME = "root"
DB_PASSWORD = "thecloud2015.1"
DB_HOSTNAME = "************"
DB_PORT = "3306"
DB_DATABASE = "the"

FLAVOR_UUID = "9f7ba565-e9d1-40c4-8b2c-9ff273adfc83"
NETWORK_UUID = "c8227cf0-7be6-4c16-b446-bae0e8f8e2b5"

CEPH_AUTH_URI="http://mgr.api"
CEPH_USER = "admin"
CEPH_PWD = "thecloud2015.1"


METRICS_URI="http://*************:9283/metrics"
GOAPI_URI="goapi"
GOAPI_PORT="8086"

LDAP_SERVER="*************:389"
LDAP_USER = 'administrator'
LDAP_PSW = '!QAZ2wsx1'
LDAP_FILTER = 'objectclass=user'
