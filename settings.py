JSON_LOG_PATH = "/tmp/jsontheweb.log"
JSON_LOG_UPLOAD_PATH = "/tmp/jsontheupload.log"
JSON_LOG_USER_PATH = "/tmp/jsontheuser.log"
JSON_LOG_ASYN_PATH = "/tmp/jsontheasyn.log"
JSON_LOG_TRIGGER_PATH = "/tmp/jsontriggerasyn.log"
JSON_LOG_SCHEDULER_PATH = "/tmp/jsonschedulerasyn.log"
LOG_ASYN_PATH = "/tmp/theasyn.log"

ETCD_HOST = 'localhost'


AUTH_URI = "http://************:5000/v3"
AUTH_TYPE = "password"
PROJECT_DOMAIN_ID = "default"
USER_DOMAIN_ID = "default"
PROJECT_NAME = "admin"
USERNAME = "admin"
PASSWORD = "thecloud2015.1"

ZUN_AUTH_URI = "http://************:5000/v3"
ZUN_AUTH_TYPE = "password"
ZUN_PROJECT_DOMAIN_ID = "default"
ZUN_USER_DOMAIN_ID = "default"
ZUN_PROJECT_NAME = "admin"
ZUN_USERNAME = "admin"
ZUN_PASSWORD = "thecloud2015.1"

RECYCLE_DAYS = 10

DB_USERNAME = "root"
DB_PASSWORD = "thecloud2015.1"
DB_HOSTNAME = "************"
DB_PORT = "3306"
DB_DATABASE = "the"

FLAVOR_UUID = "9f7ba565-e9d1-40c4-8b2c-9ff273adfc83"
NETWORK_UUID = "c8227cf0-7be6-4c16-b446-bae0e8f8e2b5"

CEPH_AUTH_URI="http://************:8443"
CEPH_USER = "admin"
CEPH_PWD = "p@ssw0rd"
CEPH_VERSION = "v16"


METRICS_URI="http://************:9283/metrics"
RROMETHEUS_ALERT_URI = "http://************:9090/api/v1/alerts"
RROMETHEUS_QUERY_URI = "http://************:9090/api/v1/query"
RROMETHEUS_QUERY_RANGE_URI = "http://************:9090/api/v1/query_range"
GRAFANA_AUTH_URI="*************************************"
#GRAFANA_AUTH_URI="http://************:9090"

THESC_URI="http://************:9002"
TRIGGER_URI="http://************:9001"
SCHEDULER_URI="http://localhost:9004"

ALERTMANAGER_URI = "http://************:9093"


LDAP_SERVER="*************:389"
LDAP_USER = '<EMAIL>'
LDAP_PSW = 'Thxh1111'
LDAP_FILTER = 'objectclass=user'


SYSTEM_VERSION="TH HCI V2.0"
SYSTEM_PRODUCT="TH HCI 9202"
SYSTEM_SER="123456789"
