# -*- coding: utf-8 -*-


from dataclasses import dataclass

@dataclass
class Container(object):
    id : str
    name : str
    
class ContainerDetailFrom:
    id : str
    
class ContainerCreateFrom:
    name : str
    ram : int
    vcpus : int
    imagename : str
    networkid : str
    
@dataclass
class ContainerDetail(object):
    uuid : str
    name : str
    image : str
    cpu : float
    memory : str
    command : list
    status : str
    addresses : dict
    host : str
    status_detail : str
    disk : int
