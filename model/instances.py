# -*- coding: utf-8 -*-

import tornado.ioloop
import pyrestful.rest

from pyrestful import mediatypes
from pyrestful.rest import get, post, put, delete

from dataclasses import dataclass
import json
from sqlalchemy.engine import strategies
from sqlalchemy.orm import strategy_options
from typing import List, Any

from dataclasses import dataclass


@dataclass
class Instances(object):
    id : str
    name : str
    """
    def __repr__(self):
        return json.dumps(self.__dict__)
    def toJson(self):
        return json.dumps(self.__dict__)
    """
    
class InstancesAddtoGroup(object):
    id : str
    group_name : str
    
@dataclass
class InstanceDetail(object):
    id : str
    name : str
    status : str
    created : str
    updated : str
    hostId : str
    addresses : dict
    image : dict
    flavor : dict
    
    
@dataclass
class InstanceGetDetail(object):
    id : str
    name : str
    status : str
    created : str
    updated : str
    hostId : str
    addresses : dict
    flavor : dict



@dataclass
class ServerRes:
    id : str

    
    
class Groups(object):
    instances : dict
    name : str

class Group(object):
    name : str
    page: int
    pagecount: int
    search: str

class GroupRename(object):
    name : str
    new_name : str

class Clusters(object):
    nodes : dict
    name : str
    
class InstancesDetailFrom(object):
    page: int
    pagecount: int
    search: str
    
    
class InstanceCreateFrom(object):
    name : str
    imageRef: str
    flavorRef: str
    networkRef: str
    count: int
    uuid: str
    availability_zone: str


class InstanceCreateFromV2(object):
    name: str
    imageRef: str
    flavorRef: str
    networkRef: str
    count: int
    uuid: str
    os_type: str
    availability_zone: str


class InstanceDeleteFrom(object):
    ids : list
    
class InstanceActionFrom(object):
    id : str
    action : str
    data : str



class InstanceActionFromV2(object):
    ids : []
    action : str
    data : str


class InstanceEditFrom(object):
    id : str
    name : str
    
class InstanceISOEditFrom(object):
    id : str
    description : str
    
class InstanceAttachForm(object):
    id : str
    vmid : str


class InstanceDetachFrom(object):
    vmid: str
    volumeid: str


class ServerWithISOFrom(object):
    name: str
    imageRef: str
    flavorRef: str
    networks: str
    count: int


class InstanceISOCreateFrom(object):
    name: str
    imageRef: str
    flavorRef: str
    networkRef: str
    count: int
    uuid: str
    uuid2: str
    availability_zone: str


class ServerAction:
    def __init__(self):
        self.action: str
        self.actions: list
        self.admin_pass: str
        self.always_startup: bool
        self.client: any
        self.cpu_quota: int
        self.cpu_shares: int
        self.current_flavor_id: str
        self.disk: int
        self.extdisk: int
        self.fail_id: str
        self.current_flavor_id: str
        self.flavor_form: any
        self.numa_nodes: int
        self.op: str
        self.ram: int
        self.ser_form: any
        self.ser_id: str
        self.ser_name: str
        self.vcpus: int

        self.extra_specs          = dict()
        self.always_startup_key   = "CIM_VSSD_AutomaticStartupAction"
        self.always_startup_volue = "Always startup"
        self.cpu_policy_key       = "hw:cpu_policy"
        self.cpu_policy_volue     = "dedicated"
        self.cpu_quota_key        = "quota:cpu_quota"
        self.cpu_resources_key    = "quota:cpu_resources"
        self.cpu_shares_key       = "quota:cpu_shares"
        self.numa_nodes_key       = "hw:numa_nodes"

    def get_flavor_id(self):
        if not self.flavor_id:
            self.flavor_form.name    = "%s-%s-%s" % (self.vcpus, self.ram, self.disk)
            if self.cpu_shares:
                self.flavor_form.name += "-shares_%s" % self.cpu_shares
            if self.cpu_quota:
                self.flavor_form.name += "-quoto_%s" % self.cpu_quota
            if self.numa_nodes:
                self.flavor_form.name += "-numa_%s" % self.numa_nodes
            if self.always_startup:
                self.flavor_form.name += "-startup"

            flavors = self.client.FlavorClient.openstack_get_all_list(self.client)
            for flavor in flavors:
                if flavor["name"] == self.flavor_form.name:
                    self.flavor_id = flavor["id"]
                    break

            self.flavor_form.disk    = self.disk
            self.flavor_form.ram     = self.ram * 1024
            self.flavor_form.vcpus   = self.vcpus
            self.flavor_form.extdisk = self.extdisk

            flavors = self.client.FlavorClient.openstack_get_all_list(self.client)
            for flavor in flavors:
                if flavor["name"] == self.flavor_form.name:
                    self.flavor_id = flavor["id"]
                    break

            if not self.flavor_id:
                flavor = self.client.FlavorClient.openstack_create_flavor(
                    self.client, self.flavor_form)
                self.flavor_id = flavor["id"]

    def server_action(self):
        self.ser_form.id     = self.ser_id

        if self.action == "resize":
            self.ser_form.action = self.action
            self.ser_form.data   = self.flavor_id
        if self.action == "getSPICEConsole":
            self.ser_form.action = self.action
            self.ser_form.data   = ""
        if self.action == "changePassword":
            self.ser_form.action = self.action
            self.ser_form.data   = self.admin_pass

        res = self.client.NovaClient.openstack_server_action(
            self.client, self.ser_form)

        if res["msg"] == "ok" and self.action == "resize":
            self.ser_form.action = "confirm"
            self.ser_form.data   = ""
            self.client.NovaClient.openstack_server_action(
                self.client, self.ser_form)

        return res

    def is_exist_extra_specs_key(self, key):
        try:
            res = self.client.openstack_get_flavor_os_extra_specs_key(
                self.client, self.flavor_id, key)
            if res.status_code == 200:
                return True
        except:
            pass

        return False

    def update_extra_specs(self):
        if self.always_startup:
            self.extra_specs.update({self.always_startup_key: self.always_startup_volue})
        elif self.is_exist_extra_specs_key(self.always_startup_key):
            self.client.FlavorClient.openstack_delete_flavor_os_extra_specs_key(
                self.client, self.flavor_id, self.always_startup_key)

        if self.cpu_quota:
            self.extra_specs.update({self.cpu_quota_key: self.cpu_quota})
        elif self.is_exist_extra_specs_key(self.cpu_quota_key):
            self.client.FlavorClient.openstack_delete_flavor_os_extra_specs_key(
                self.client, self.flavor_id, self.cpu_quota_key)

        if self.cpu_shares:
            self.extra_specs.update({self.cpu_shares_key: self.cpu_shares})
        elif self.is_exist_extra_specs_key(self.cpu_shares_key):
            self.client.FlavorClient.openstack_delete_flavor_os_extra_specs_key(
                self.client, self.flavor_id, self.cpu_shares_key)

        if self.numa_nodes:
            self.extra_specs.update({self.numa_nodes_key: self.numa_nodes,
                                     self.cpu_policy_key: self.cpu_policy_volue})
        elif self.is_exist_extra_specs_key(self.numa_nodes_key):
            self.client.FlavorClient.openstack_delete_flavor_os_extra_specs_key(
                self.client, self.flavor_id, self.numa_nodes_key)

        for key, volue in self.extra_specs.items():
            self.client.FlavorClient.openstack_update_flavor_os_extra_specs_key(
                self.client, self.flavor_id, key, volue)
