# -*- coding: utf-8 -*-


from dataclasses import dataclass
from builtins import int, bool
from typing import List, Dict



@dataclass
class Network(object):
    id : str
    name : str
    created_at : str
    updated_at : str
    mtu : int
    shared : bool
    status : str
    subnets : list


class NetworkCreateForm:
    name : str
    enable_dhcp : str
    cidr : str
    gateway_ip : str
    startip : str
    endip : str
    
class NetworkCreatePhyicalForm:
    name : str
    cidr : str
    vlanid : str
    bridge : str
    bonding : str
    devinfo : str
    speed : str
    type : str
    
class NetworkUpdatePhyicalForm:
    id : str
    name : str
    cidr : str
    vlanid : str
    bridge : str
    bonding : str
    devinfo : str
    speed : str
    type : str
    
class NetworkEditForm:
    name : str
    id : str
    
class AdminNetworkCreateForm:
    name : str
    network_type : str
    segmentation_id : str
    enable_dhcp : bool
    cidr : str
    gateway_ip : str
    startip : str
    endip : str
    dns_nameservers : str
    #host_routes : List[Dict[str, str]]


@dataclass
class NetworkDetail(object):
    id : str
    name : str
    created_at : str
    updated_at : str
    mtu : int
    shared : bool
    status : str
    
class NetworkDeleteFrom:
    id : str
    
@dataclass
class SubnetDetail(object):
    id : str
    name : str
    created_at : str
    updated_at : str
    network_id : str
    ip_version : int
    enable_dhcp : bool
    cidr : str
    gateway_ip : str
    
    
