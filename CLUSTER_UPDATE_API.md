# 主机聚合更新接口文档

## 概述

本文档描述了主机聚合（Host Aggregate）更新接口的功能扩展。现在支持同时更新主机聚合的名称、可用区（Availability Zone）和元数据（Metadata）。

## 接口列表

### 1. 更新集群名称（原有接口）

**接口地址**: `PUT /v1/update/clusters/name`

**功能**: 只更新主机聚合的名称

**请求体**:
```json
{
    "id": 1,
    "name": "new-cluster-name"
}
```

**响应**:
```json
{
    "msg": "ok"
}
```

### 2. 更新集群完整信息（新接口）

**接口地址**: `PUT /v1/update/clusters/full`

**功能**: 同时更新主机聚合的名称、可用区和元数据

**请求体**:
```json
{
    "id": 1,
    "name": "new-cluster-name",
    "availability_zone": "zone-1",
    "metadata": {
        "environment": "production",
        "owner": "admin",
        "description": "生产环境集群"
    }
}
```

**字段说明**:
- `id` (必填): 主机聚合的ID
- `name` (可选): 新的集群名称
- `availability_zone` (可选): 新的可用区名称
- `metadata` (可选): 元数据对象，包含键值对

**响应**:
```json
{
    "msg": "ok"
}
```

## 使用示例

### 示例1: 只更新名称
```bash
curl -X PUT http://localhost:8080/v1/update/clusters/full \
  -H "Content-Type: application/json" \
  -d '{
    "id": 1,
    "name": "production-cluster"
  }'
```

### 示例2: 只更新可用区
```bash
curl -X PUT http://localhost:8080/v1/update/clusters/full \
  -H "Content-Type: application/json" \
  -d '{
    "id": 1,
    "availability_zone": "zone-east-1"
  }'
```

### 示例3: 只更新元数据
```bash
curl -X PUT http://localhost:8080/v1/update/clusters/full \
  -H "Content-Type: application/json" \
  -d '{
    "id": 1,
    "metadata": {
      "environment": "staging",
      "team": "devops",
      "cost_center": "IT-001"
    }
  }'
```

### 示例4: 同时更新所有字段
```bash
curl -X PUT http://localhost:8080/v1/update/clusters/full \
  -H "Content-Type: application/json" \
  -d '{
    "id": 1,
    "name": "production-cluster-v2",
    "availability_zone": "zone-east-1",
    "metadata": {
      "environment": "production",
      "version": "2.0",
      "owner": "<EMAIL>",
      "backup_policy": "daily"
    }
  }'
```

## 技术实现

### 后端实现

1. **数据模型扩展**:
   - 扩展了 `ClusterEditModel` 和 `ClusterEditForm` 以支持新字段
   - 更新了 `Aggregate` 数据类以包含完整的字段定义

2. **OpenStack API 集成**:
   - 新增 `openstack_update_aggregate_full()` 方法支持名称和可用区更新
   - 新增 `openstack_update_aggregate_metadata()` 方法支持元数据更新
   - 保留原有的 `openstack_update_aggregate_name()` 方法以保持向后兼容

3. **处理器方法**:
   - 新增 `thecloud_put_update_cluster_full()` 方法处理完整更新请求
   - 保留原有的 `thecloud_put_update_cluster_name()` 方法

### OpenStack API 调用

接口内部会调用以下 OpenStack Nova API：

1. **更新名称和可用区**:
   ```
   PUT /os-aggregates/{aggregate_id}
   {
     "aggregate": {
       "name": "new-name",
       "availability_zone": "new-zone"
     }
   }
   ```

2. **更新元数据**:
   ```
   POST /os-aggregates/{aggregate_id}/action
   {
     "set_metadata": {
       "metadata": {
         "key1": "value1",
         "key2": "value2"
       }
     }
   }
   ```

## 错误处理

### 常见错误响应

1. **集群不存在**:
```json
{
    "msg": "error",
    "error": "Aggregate not found"
}
```

2. **权限不足**:
```json
{
    "msg": "error",
    "error": "Unauthorized"
}
```

3. **参数错误**:
```json
{
    "msg": "error",
    "error": "Invalid parameters"
}
```

## 注意事项

1. **向后兼容性**: 原有的 `/v1/update/clusters/name` 接口保持不变，确保现有代码不受影响

2. **字段可选性**: 新接口中除了 `id` 字段外，其他字段都是可选的，可以只更新需要修改的字段

3. **元数据覆盖**: 元数据更新是覆盖式的，会替换现有的所有元数据

4. **数据库同步**: 当更新集群名称时，会同步更新数据库中相关主机的 `cluster_name` 字段

5. **日志记录**: 所有操作都会记录到系统日志中，包括成功和失败的操作

## 测试

使用提供的测试脚本 `test_cluster_update.py` 来验证接口功能：

```bash
python test_cluster_update.py
```

测试脚本会验证：
- 原有名称更新接口
- 新的完整更新接口
- 部分字段更新
- 错误处理
