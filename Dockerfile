FROM tianwen1:5000/python:3.8 as builder


RUN apt-get install -y git 

RUN git clone http://************/the/thedb.git \
    && cd /thedb \
    && git log -n 1 | grep commit | awk '{ print $2 }' | cut -c 1-10 > /usr/thedb_commit_version \
    && git describe --abbrev=1  --tags > /usr/thedb_latest_tag \   
    && rm -rf /thedb/.git

RUN git clone http://************/the/theapi.git \
    && cd /theapi \
    && git log -n 1 | grep commit | awk '{ print $2 }' | cut -c 1-10 > /usr/theapi_commit_version \
    && git describe --abbrev=1  --tags > /usr/theapi_latest_tag \ 
    && rm -rf /theapi/.git


RUN git clone http://************/the/theasyn.git \
    && cd /theasyn \
    && git log -n 1 | grep commit | awk '{ print $2 }' | cut -c 1-10 > /usr/theasyn_commit_version \
    && git describe --abbrev=1  --tags > /usr/theasyn_latest_tag \ 
    && rm -rf /theasyn/.git
    
FROM tianwen1:5000/python:3.8

ARG ARG_VERSION=latest
ARG ARG_VERSION
ENV COMMIT_VERSION=$ARG_VERSION

ARG ARG_COMMIT_DATE=2000-01-01-01:01:01
ARG ARG_COMMIT_DATE
ENV COMMIT_DATE=$ARG_COMMIT_DATE

ARG ARG_TAG=v2.1
ARG ARG_TAG
ENV LATEST_TAG=$ARG_TAG

ARG ARG_OS=linux
ARG ARG_OS
ENV OS_VERSION=$ARG_OS


COPY --from=builder /theapi /theapi
COPY --from=builder /thedb /thedb
COPY --from=builder /theasyn /theasyn

ENV PYTHONPATH=/theapi:/thedb:/theasyn

ADD . /code
WORKDIR /code

#RUN pip3 install -r requirements.txt -i  https://pypi.tuna.tsinghua.edu.cn/simple
RUN pip3 install -r requirements.txt -i  https://mirrors.aliyun.com/pypi/simple/
#RUN pip3 install -r requirements.txt

#RUN git clone http://************/maojj/vsphere-automation-sdk-python.git
#RUN cd vsphere-automation-sdk-python && pip3 install --upgrade pip setuptools && pip3 install --upgrade --force-reinstall --ignore-installed -r requirements.txt --extra-index-url file:///vsphere-automation-sdk-python/lib

#RUN pip3 install --upgrade git+http://************/maojj/vsphere-automation-sdk-python.git


CMD python -u main.py

EXPOSE 8006
