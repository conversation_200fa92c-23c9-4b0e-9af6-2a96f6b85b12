FROM tianwen1:5000/python:3.8.2-slim-buster

ARG ARG_VERSION=latest
ARG ARG_VERSION
ENV COMMIT_VERSION=$ARG_VERSION

ARG ARG_COMMIT_DATE=2000-01-01-01:01:01
ARG ARG_COMMIT_DATE
ENV COMMIT_DATE=$ARG_COMMIT_DATE

ARG ARG_TAG=v2.1
ARG ARG_TAG
ENV LATEST_TAG=$ARG_TAG

ARG ARG_OS=linux
ARG ARG_OS
ENV OS_VERSION=$ARG_OS

ADD . /code
WORKDIR /code

RUN pip3 install -r requirements.txt -i  https://pypi.tuna.tsinghua.edu.cn/simple

RUN chmod +x /code/bootstrap.sh
ENTRYPOINT ["/bin/bash", "/code/bootstrap.sh"]
