from tornado_swagger.components import components

@components.schemas.register
class VolumeModel(object):
    """
    ---
    type: object
    description: 记录
    properties:
        id:
            type: integer
            description: 自然数
        name:
            type: string
            description: 云硬盘名字
    """   
    
@components.schemas.register
class VolumesListFormModel(object):
    """
    ---
    type: object
    description: 云硬盘列表
    properties:
        page:
            type: integer
            description: 第几页
        pagecount:
            type: integer
            description: 页数量
        search:
            type: string
            description: 搜索
    """
    
@components.schemas.register
class VolumeDetailForm(object):
    """
    ---
    type: object
    description: 记录
    properties:
        id:
            type: string
            description: 云硬盘ID
        name:
            type: string
            description: 云硬盘名称
        size:
            type: string
            description: 大小
        status:
            type: string
            description: 状态
        volume_type:
            type: integer
            description: 卷类型

    """
    
@components.schemas.register
class ArrayVolumesDetailModel(object):
    """
    ---
    type: object
    description: Array of Volumes Detail
    properties:
        pages:
            type: integer
            description: 总页数
        total:
            type: integer
            description: 数据总条数
        data:
            type: array
            description: 虚拟机列表
            items:
                $ref: '#/components/schemas/VolumeDetailForm'

    """
    
@components.schemas.register
class VolumeTypeModel(object):
    """
    ---
    type: object
    description: 记录
    properties:
        id:
            type: integer
            description: 自然数
        name:
            type: string
            description: 名字
        is_public:
            type: string
            description: 公有
        description:
            type: string
            description: 描述
    """   
    
    
@components.schemas.register
class VolumeDetailModel(object):
    """
    ---
    type: object
    description: 记录
    properties:
        id:
            type: integer
            description: 自然数
        name:
            type: string
            description: 云硬盘名字
        vmname:
            type: string
            description: 虚拟机名字
        mountpoint:
            type: string
            description: 挂载路径
        attachmentid:
            type: string
            description: 挂载路径
    """  
    
@components.schemas.register
class VolumeActionModel(object):
    """
    ---
    type: object
    description: 
    properties:
        id:
            type: integer
            description: 云硬盘id
        action:
            type: string
            description: 动作 extend retype upload detach
        data:
            type: string
            description: 附带参数
    """   
    
@components.schemas.register
class VolumeSnapshotModel(object):
    """
    ---
    type: object
    description: 
    properties:
        id:
            type: integer
            description: 云硬盘id
        action:
            type: string
            description: 动作 create list delete edit
        data:
            type: string
            description: 附带参数
    """
    
@components.schemas.register
class VolumeBackupModel(object):
    """
    ---
    type: object
    description: 
    properties:
        id:
            type: integer
            description: 云硬盘id
        action:
            type: string
            description: 动作 create list delete edit
        data:
            type: string
            description: 附带参数
    """
    
@components.schemas.register
class VolumeDeleteModel(object):
    """
    ---
    type: object
    description: 
    properties:
        id:
            type: integer
            description: 云硬盘id
    """  
    
@components.schemas.register
class VolumeAttachModel(object):
    """
    ---
    type: object
    description: 
    properties:
        id:
            type: integer
            description: 云硬盘id
        vmid:
            type: integer
            description: 云主机id
        mountpoint:
            type: string
            description: 挂载路径
    """  

@components.schemas.register
class VolumeCreateModel(object):
    """
    ---
    type: object
    description: 记录
    properties:
        name:
            type: string
            description: 云硬盘名字
        size:
            type: integer
            description: 云硬盘大小
    """   
    
@components.schemas.register
class VolumeMsgModel(object):
    """
    ---
    type: object
    description: 返回值
    properties:
        msg:
            type: string
            description: 返回值
    """   

@components.schemas.register
class ArrayVolumeModel(object):
    """
    ---
    type: array
    description: Array of Post model representation
    items:
        $ref: '#/components/schemas/VolumeModel'
    """
    
@components.schemas.register
class ArrayVolumeTypeModel(object):
    """
    ---
    type: array
    description: Array of Post model representation
    items:
        $ref: '#/components/schemas/VolumeModel'
    """