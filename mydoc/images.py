from tornado_swagger.components import components


@components.schemas.register
class ImageModel(object):
    """
    ---
    type: object
    description: 记录
    properties:
        id:
            type: string
            description: ID
        name:
            type: string
            description: 镜像的名字
        status:
            type: string
            description: 状态
        disk_format:
            type: string
            description: 磁盘格式
        size:
            type: integer
            description: 大小
        visibility:
            type: string
            description: 可见性
        protected:
            type: bool
            description: 受保护的
        
    """   
@components.schemas.register
class CreateImageModel(object):
    """
    ---
    type: object
    description: 记录
    properties:
        name:
            type: string
            description: 镜像名称
        disk_format:
            type: string
            description: 镜像格式

    """   
    
@components.schemas.register
class UploadImageModel(object):
    """
    ---
    type: object
    description: 记录
    properties:
        id:
            type: string
            description: 镜像id
        file:
            type: string
            description: 镜像文件

    """   

@components.schemas.register
class ArrayImageModel(object):
    """
    ---
    type: array
    description: Array of Post model representation
    items:
        $ref: '#/components/schemas/ImageModel'
    """
    
@components.schemas.register
class ImageEditModel(object):
    """
    ---
    type: object
    description: 记录
    properties:
        id:
            type: string
            description: 镜像id
        name:
            type: string
            description: 镜像名

    """   
    
@components.schemas.register
class ImageDeleteModel(object):
    """
    ---
    type: object
    description: 记录
    properties:
        id:
            type: string
            description: 镜像id

    """   
    
