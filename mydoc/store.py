from tornado_swagger.components import components


@components.schemas.register
class StoreModel(object):
    """
    ---
    type: object
    description: 记录
    properties:
        id:
            type: string
            description: 1 2 3
        name:
            type: string
            description: ceph池名
        ceph_pool_max_avail:
            type: string
            description: 最大容量
        ceph_pool_stored:
            type: string
            description: 已用容量
        hostname:
            type: string
            description: 主机名
        status:
            type: string
            description: 状态
        
    """
@components.schemas.register
class ArrayStoreModel(object):
    """
    ---
    type: array
    description: Array of Post model representation
    items:
        $ref: '#/components/schemas/StoreModel'
    """