from tornado_swagger.components import components

@components.schemas.register
class UserModel(object):
    """
    ---
    type: object
    description: 记录
    properties:
        id:
            type: integer
            description: 用户ID
        name:
            type: string
            description: 用户名
        username:
            type: string
            description: 用户登录名
        status:
            type: string
            description: 用户状态 on | off
        create_at:
            type: string
            description: 创建时间
        updated_at:
            type: string
            description: 修改时间
        expiredday:
            type: string
            description: 过期天数
        


    """   

@components.schemas.register
class UserListModel(object):
    """
    ---
    type: array
    description: Array of Post model representation
    items:
        $ref: '#/components/schemas/UserModel'
    """


@components.schemas.register
class UserStatusFormModel(object):
    """
    ---
    type: object
    description: 创建用户
    properties:
        name:
            type: string
            description: 用户昵称
        status:
            type: string
            description: 用户状态 on | off
    """
    
@components.schemas.register
class UserFormModel(object):
    """
    ---
    type: object
    description: 创建用户
    properties:
        name:
            type: string
            description: 用户昵称
        username:
            type: string
            description: 用户登录名
        password:
            type: string
            description: 用户登录名
        role:
            type: string
            description: 角色， 管理员 | 评审员 | 安全员
        expiredday:
            type: integer
            description: 距离创建时间多少天过期
            
    """  




