from tornado_swagger.components import components


@components.schemas.register
class StatisticsModel(object):
    """
    ---
    type: object
    description: 记录
    properties:
        id:
            type: integer
            description: 机器数
        memory_mb:
            type: integer
            description: 内存总数 M
        local_gb:
            type: integer
            description: 本地磁盘 G
        vcpus_used:
            type: integer
            description: 以使用虚CPU数
        memory_mb_used:
            type: integer
            description: 以使用内存数 M
        local_gb_used:
            type: integer
            description: 以使用本地磁盘 G
        free_ram_mb:
            type: integer
            description: 空闲内存 M
        free_disk_gb:
            type: integer
            description: 空闲空间 G
        current_workload:
            type: integer
            description: 当前负载
        running_vms:
            type: integer
            description: 运行虚机数
        disk_available_least:
            type: integer
            description: 最新可用磁盘

        
    """

@components.schemas.register
class HypervisorModel(object):
    """
    ---
    type: object
    description: 记录
    properties:
        id:
            type: integer
            description: 1 2 3
        hypervisor_hostname:
            type: string
            description: 主机名
        state:
            type: string
            description: up | download
        status:
            type: string
            description: enabled | disabled
        
    """
@components.schemas.register
class ArrayHypervisorModel(object):
    """
    ---
    type: array
    description: Array of Post model representation
    items:
        $ref: '#/components/schemas/HypervisorModel'
    """

@components.schemas.register
class RequestClusterHostModel(object):
    """
    ---
    type: object
    description: 创建主机聚合
    properties:
        id:
            type: string
            description: 主机聚合的id
        hostname:
            type: string
            description: 主机名

    """   
 
@components.schemas.register
class RequestCreateClusterModel(object):
    """
    ---
    type: object
    description: 创建主机聚合
    properties:
        name:
            type: string
            description: 主机聚合的名字

    """   
@components.schemas.register
class ClusterModel(object):
    """
    ---
    type: object
    description: 记录
    properties:
        id:
            type: integer
            description: 自然数
        name:
            type: string
            description: 主机聚合的名字
        created_at:
            type: string
            description: 创建时间

    """   
    
@components.schemas.register
class ClusterEditModel(object):
    """
    ---
    type: object
    description: 记录
    properties:
        id:
            type: integer
            description: 自然数
        name:
            type: string
            description: 主机聚合的名字
            
    """  
    
@components.schemas.register
class ArrayClusterModel(object):
    """
    ---
    type: array
    description: Array of Post model representation
    items:
        $ref: '#/components/schemas/ClusterModel'
    """

@components.schemas.register
class ClusterDetailModel(object):
    """
    ---
    type: object
    description: 记录
    properties:
        id:
            type: integer
            description: 自然数
        hypervisor_hostname:
            type: string
            description: 物理机主机名
        state:
            type: string
            description: up | download
        status:
            type: string
            description: enabled | disabled
        vcpus:
            type: integer
            description: 总虚拟cpu个数
        memory_mb:
            type: integer
            description: 内存总数 单位 M
        free_disk_gb:
            type: integer
            description: 空闲磁盘 单位 G
        vcpus_used:
            type: integer
            description: 已用虚拟cpu个数
        hypervisor_type:
            type: integer
            description: 虚拟化类型 
        host_ip:
            type: integer
            description: 主机IP 
        hypervisor_version:
            type: integer
            description: 虚拟化类型版本
        running_vms:
            type: integer
            description: 正在运行的虚机
        cpu_info:
            type: string
            description: cpu 信息 json串
        
    """   
    
    
@components.schemas.register
class ArrayClusterDetailModel(object):
    """
    ---
    type: array
    description: Array of Post model representation
    items:
        $ref: '#/components/schemas/ClusterDetailModel'
    """