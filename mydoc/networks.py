from tornado_swagger.components import components
    
@components.schemas.register
class RequestCreateNetworkModel(object):
    """
    ---
    type: object
    description: 请求参数
    properties:
        name:
            type: string
            description: 名称
        cidr:
            type: string
            description: 网络地址(CIDR)
        enable_dhcp:
            type: string
            description: 开启dhcp
        gateway_ip:
            type: string
            description: 网关IP
            
    """
    
@components.schemas.register
class RequestEditNetworkModel(object):
    """
    ---
    type: object
    description: 请求参数
    properties:
        name:
            type: string
            description: 名称
        id:
            type: string
            description: 网络ID

    """
    
@components.schemas.register
class RequestCreateAdminNetworkModel(object):
    """
    ---
    type: object
    description: 请求参数
    properties:
        name:
            type: string
            description: 名称
        cidr:
            type: string
            description: 网络地址(CIDR)
        enable_dhcp:
            type: string
            description: 开启dhcp
        gateway_ip:
            type: string
            description: 网关IP
        network_type:
            type: string
            description:  网络类型
        segmentation_id:
            type: integer
            description:  segmentation_id
        
    """
    
@components.schemas.register
class RequestCreatePhyicalNetworkModel(object):
    """
    ---
    type: object
    description: 请求参数
    properties:
        type:
            type: integer
            description:  网络类型
        name:
            type: string
            description: 名称
        cidr:
            type: string
            description: 网络地址(CIDR)
        vlanid:
            type: string
            description: Vlan号
        bridge:
            type: string
            description: 桥接
        bonding:
            type: string
            description:  网卡绑定
        devinfo:
            type: string
            description:  物理网卡
        speed:
            type: string
            description:  速率
        
    """
    
@components.schemas.register
class RequestUpdatePhyicalNetworkModel(object):
    """
    ---
    type: object
    description: 请求参数
    properties:
        id:
            type: string
            description: 物理网络ID
        type:
            type: integer
            description:  网络类型
        name:
            type: string
            description: 名称
        cidr:
            type: string
            description: 网络地址(CIDR)
        vlanid:
            type: string
            description: Vlan号
        bridge:
            type: string
            description: 桥接
        bonding:
            type: string
            description:  网卡绑定
        devinfo:
            type: string
            description:  物理网卡
        speed:
            type: string
            description:  速率
        
    """
    
@components.schemas.register
class NetworkDeleteModel(object):
    """
    ---
    type: object
    description: 记录
    properties:
        id:
            type: string
            description: 网络id

    """   

