from tornado_swagger.components import components

@components.schemas.register
class GroupForm(object):
    """
    ---
    type: object
    description: 记录
    properties:
        name:
            type: string
            description: 虚机分组名称
        pid:
            type: integer
            description: 虚机分组名称pid
    """
    
@components.schemas.register
class GroupPageForm(object):
    """
    ---
    type: object
    description: 记录
    properties:
        name:
            type: string
            description: 虚机分组名称
        page:
            type: integer
            description: 第几页
        pagecount:
            type: integer
            description: 页数量
        search:
            type: string
            description: 搜索
    """
    
@components.schemas.register
class InstanceListFormModel(object):
    """
    ---
    type: object
    description: 虚拟机列表
    properties:
        page:
            type: integer
            description: 第几页
        pagecount:
            type: integer
            description: 页数量
        search:
            type: string
            description: 搜索
    """
    
@components.schemas.register
class InstanceDetailForm(object):
    """
    ---
    type: object
    description: 记录
    properties:
        id:
            type: string
            description: 虚机ID
        name:
            type: string
            description: 虚机名称
        ip:
            type: string
            description: 虚机ip地址
        hostname:
            type: string
            description: 主机
        vcpus:
            type: integer
            description: CPU
        ram:
            type: integer
            description: 内存
        imagename:
            type: string
            description: 镜像名
        status:
            type: string
            description: 状态
    """
    
@components.schemas.register
class ArrayInstanceDetailModel(object):
    """
    ---
    type: object
    description: Array of Instance Detail
    properties:
        pages:
            type: integer
            description: 总页数
        total:
            type: integer
            description: 数据总条数
        data:
            type: array
            description: 虚拟机列表
            items:
                $ref: '#/components/schemas/InstanceDetailForm'

    """
    
    
@components.schemas.register
class GroupRenameForm(object):
    """
    ---
    type: object
    description: 记录
    properties:
        name:
            type: string
            description: 虚机分组旧名称
        new_name:
            type: string
            description: 虚机分组新名称
    """
    
@components.schemas.register
class GroupEditForm(object):
    """
    ---
    type: object
    description: 记录
    properties:
        group_name:
            type: string
            description: 虚机分组名称
        id:
            type: string
            description: 虚拟机ID
    """
    
@components.schemas.register
class ArrayGroupForm(object):
    """
    ---
    type: array
    description: Array of Post model representation
    items:
        $ref: '#/components/schemas/GroupForm'
    """
    
@components.schemas.register
class RequestCreateInstanceModel(object):
    """
    ---
    type: object
    description: 请求参数
    properties:
        name:
            type: string
            description: 名称
        imageRef:
            type: string
            description: 镜像ID
        flavorRef:
            type: string
            description: 云主机类型ID
        networkRef:
            type: string
            description: 网络ID
        count:
            type: integer
            description: 数量
            
    """
    
@components.schemas.register
class RequestDeleteInstanceModel(object):
    """
    ---
    type: object
    description: 请求参数
    properties:
        id:
            type: string
            description: 虚拟机ID
            
    """
    
@components.schemas.register
class RequestActionInstanceModel(object):
    """
    ---
    type: object
    description: 请求参数
    properties:
        id:
            type: string
            description: 虚拟机ID
        action:
            type: string
            description: 动作 start stop reboot resize migrate
        data:
            type: string
            description: 附加参数 
        
    """
    
@components.schemas.register
class RequestEditInstanceModel(object):
    """
    ---
    type: object
    description: 请求参数
    properties:
        id:
            type: string
            description: 虚拟机ID
        name:
            type: string
            description: 虚拟机名字
            
    """
    
@components.schemas.register
class RequestSnapshotInstanceModel(object):
    """
    ---
    type: object
    description: 请求参数
    properties:
        id:
            type: string
            description: 虚拟机ID
        name:
            type: string
            description: 快照名字
            
    """